<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\Gesellschaft;
use App\Models\User;
use Demv\SdkFramework\Gateway\GatewayInterface;

class MaklerVermittlernummerService
{
    private const VERMITTLERNUMMER_CHECK_URL = '/api/v1/{user_id}/broker-nrs';
    public function __construct(
        private readonly GatewayInterface $gateway,
    ) {
    }

    public function hasVermittlernummer(User $user, Gesellschaft $gesellschaft): bool
    {
        try {
            $url = str_replace('{user_id}', (string) $user->external_id, self::VERMITTLERNUMMER_CHECK_URL);
            $url = config('professionalworks_api.base_url') . $url;

            $response = $this->gateway->request('GET', $url);

            if ($response->getStatusCode() !== 200) {
                return false;
            }

            $data = json_decode($response->getBody()->getContents(), true);

            if (is_array($data)) {
                return in_array($gesellschaft->external_id, $data, true);
            }

            return false;

        } catch (\Exception $exception) {
            \Illuminate\Support\Facades\Log::warning('Could not check Vermittlernummer for Makler at Gesellschaft', [
                'makler_id' => $user->external_id,
                'gesellschaft_id' => $gesellschaft->external_id,
                'exception' => $exception->getMessage(),
            ]);

            return false;
        }
    }
}
