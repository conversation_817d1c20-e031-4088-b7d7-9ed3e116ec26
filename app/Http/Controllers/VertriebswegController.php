<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Enums\VorgangstypEmpfaengertyp;
use App\Models\Gesellschaft;
use App\Models\Vertrag;
use App\Models\VorgangTyp;
use App\Services\MaklerVermittlernummerService;
use App\Services\VertriebswegService;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class VertriebswegController extends Controller
{
    private const ANTRAG_EINREICHEN_TYP_ID = 2;
    private const FONDS_FINANZ_EXTERNAL_ID = 701;

    public function __construct(
        private readonly VertriebswegService $vertriebswegService,
        private readonly MaklerVermittlernummerService $maklerVermittlernummerService
    ) {
    }

    public function show(VorgangTyp $vorgangTyp, ?Vertrag $vertrag = null, ?string $gesellschaftId = null): JsonResponse
    {
        abort_if(
            $vertrag === null && $gesellschaftId === null,
            400,
            "Vertrag or Gesellschaft is required"
        );

        $gesellschaft = Gesellschaft::query()->find($gesellschaftId);
        $currentUser = Auth::user();
        $vertriebswegId = null;

        if ($vertrag !== null) {
            $vertriebswegId = match($vorgangTyp->empfaenger_typ) {
                VorgangstypEmpfaengertyp::VERTRIEBSWEG => $this->vertriebswegService->getVertriebsweg($vertrag),
                VorgangstypEmpfaengertyp::GESELLSCHAFT => $vertrag->bafinGesellschaft !== null
                && $vertrag->gesellschaft_external_id !== $vertrag->bafin_gesellschaft_external_id
                    ? $vertrag->bafinGesellschaft->id
                    : $vertrag->gesellschaft?->id,
                default => null,
            };
        } else if ($gesellschaft !== null && $vorgangTyp->id === self::ANTRAG_EINREICHEN_TYP_ID) {
            if ($this->maklerVermittlernummerService->hasVermittlernummer($currentUser, $gesellschaft)) {
                $vertriebswegId = $gesellschaft->attributes->id;
            } else {
                $vertriebswegId = Gesellschaft::query()
                    ->where('external_id', self::FONDS_FINANZ_EXTERNAL_ID)
                    ->select('id')
                    ->first();
            }
        }

        return response()->json(['id' => $vertriebswegId === null ? null : (string) $vertriebswegId]);
    }
}
