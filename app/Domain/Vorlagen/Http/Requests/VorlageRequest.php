<?php

declare(strict_types=1);

namespace App\Domain\Vorlagen\Http\Requests;

use App\Domain\Vorlagen\Models\BriefVorlageContent;
use App\Domain\Vorlagen\Models\KampagneMailVorlageContent;
use App\Domain\Vorlagen\Models\MailVorlageContent;
use App\Domain\Vorlagen\Models\Vorlage;
use App\Enums\VorgangAttachmentType;
use App\Enums\VorgangEmpfaengerType;
use App\Support\JsonApi\JsonApiRequest;
use Illuminate\Contracts\Validation\Validator as ValidatorInstance;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Illuminate\Validation\ValidationException;

class VorlageRequest extends JsonApiRequest
{
    protected string $model = Vorlage::class;

    public function validator(): ValidatorInstance
    {
        $rules = [];

        $rules += $this->prefixRules($this->rules(), 'attributes');
        $rules += $this->prefixRules($this->relationRules(), 'relationships');

        return Validator::make(
            $this->validationData(),
            $rules,
            $this->messages(),
            $this->attributes(),
        );
    }

    public function rules(): array
    {
        return parent::rules() + [
            'usageByOwnerOnly' => ['required', 'boolean'],
            'description' => ['nullable', 'string', 'max:255'],
            // todo: After Laravel 10 Upgrade add !(Auth()->user()->hasPermissionTo(UserPermission::SUPERADMIN) ?? false))
            'isStandard' => ['nullable', 'boolean'],
            'name' => ['required', 'string', 'max:255'],
            'isBasisvorlage' => ['nullable', 'boolean'],
        ];
    }

    public function makeVorlageRules(array $validationData): array
    {
        $arrayOrStringValidation = function ($attribute, $value, $fail) {
            if (!is_array($value) && !is_string($value)) {
                $fail("Must be array or string");
            }
        };

        $arrayOfEnumOrEmailValidation = function ($attribute, $value, $fail) {
            try {
                /**
                 * @throws ValidationException
                 */
                Validator::validate([$value], ['email']);
            } catch (ValidationException) {
                if (VorgangEmpfaengerType::tryFrom($value) === null) {
                    $fail("Must be enum string or valid email");
                }
            }
        };

        // common
        $rules = [
            'attributes.formalContent' => ['required', $arrayOrStringValidation],
            'attributes.informalContent' => ['nullable', $arrayOrStringValidation],
        ];

        // versandart specific
        $vorlagenType = $validationData['data']['type'] ?? null;
        $rules += match ($vorlagenType) {
            BriefVorlageContent::MORPH_TYPE => [
                'attributes.empfaengerType' => ['required', 'string'],
                'attributes.senderTyp' => ['required', 'string'],

                'relationships.vorgangTyp.data.id' => ['required', 'integer'],
            ],
            MailVorlageContent::MORPH_TYPE => [
                'attributes.formalSubject' => ['required', $arrayOrStringValidation],
                'attributes.informalSubject' => ['nullable', $arrayOrStringValidation],
                'attributes.empfaengerTypes' => ['required', 'array'],
                'attributes.empfaengerTypes.*' => ['required', $arrayOfEnumOrEmailValidation],
                'attributes.cc' => ['nullable', 'array'],
                'attributes.cc.*' => ['required', 'string'],
                'attributes.bcc' => ['nullable', 'array'],
                'attributes.bcc.*' => ['required', 'string'],
                'attributes.attachments' => ['nullable', 'array'],
                'attributes.attachments.*' => ['required', Rule::enum(VorgangAttachmentType::class)],

                'relationships.vorgangTyp.data.id' => ['required', 'integer'],
            ],
            KampagneMailVorlageContent::MORPH_TYPE => [
                'attributes.formalSubject' => ['required', $arrayOrStringValidation],
                'attributes.informalSubject' => ['nullable', $arrayOrStringValidation],
                'attributes.attachments' => ['nullable', 'array'],
                'attributes.attachments.*' => ['required', Rule::enum(VorgangAttachmentType::class)],
            ],
            default => [],
        };

        return $this->prefixRules($rules, 'vorlage.data');
    }

    public function relationRules(): array
    {
        $vorlageValidationData = $this->validationData()['relationships']['vorlage'] ?? [];

        return parent::relationRules()
            + $this->makeVorlageRules($vorlageValidationData);
    }

    public function attributes(): array
    {
        return [
            'attributes.name' => 'Vorlagenname',
            'attributes.description' => 'Kurzbeschreibung',

            'relationships.vorlage.data.attributes.formalSubject' => 'Betreff (Siezen)',
            'relationships.vorlage.data.attributes.formalContent' => 'Inhalt (Siezen)',
            'relationships.vorlage.data.attributes.informalSubject' => 'Betreff (Duzen)',
            'relationships.vorlage.data.attributes.informalContent' => 'Inhalt (Duzen)',
            'relationships.vorlage.data.attributes.empfaengerTypes' => 'Empfänger',
            'relationships.vorlage.data.attributes.cc' => 'CC',
            'relationships.vorlage.data.attributes.bcc' => 'BCC',
            'relationships.vorlage.data.relationships.vorgangTyp.data.id' => 'Vorgangstyp',
        ];
    }
}
