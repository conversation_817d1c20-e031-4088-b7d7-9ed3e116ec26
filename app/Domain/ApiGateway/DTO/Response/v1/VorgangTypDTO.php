<?php

declare(strict_types=1);

namespace App\Domain\ApiGateway\DTO\Response\v1;

use OpenApi\Attributes as OA;
use Spatie\LaravelData\Data;

#[OA\Schema(schema: 'VorgangTyp',
    description: 'Definiert die Art eines Vorgangs – z.B. E-Mail, Brief oder interne Aufgabe.
    Vorgangstypen können vorkonfiguriert sein, um z.B. automatisch Ansprechpartner der Gesellschaften, Betreffzeilen oder Textbausteine zu setzen.
    Diese Typisierung erleichtert die Standardisierung und Automatisierung von Bearbeitungsprozessen.')]
class VorgangTypDTO extends Data
{
    public function __construct(
        #[OA\Property(example: 1)]
        public int $id,
        #[OA\Property(example: 'Änderung')]
        public string $titel,
    ) {
    }
}
