<?php

declare(strict_types=1);

namespace App\Domain\ApiGateway\DTO\Response\v1;

use OpenApi\Attributes as OA;
use Spatie\LaravelData\Attributes\MapInputName;
use Spatie\LaravelData\Data;

#[OA\Schema(schema: 'Sparte', description: 'Repräsentiert eine Versicherungssparte,
 wie sie in Professional Works definiert ist.
 Jede Sparte besitzt eine eindeutige ID, einen Namen sowie optional eine gebräuchliche Abkürzung.
 Zusätzlich existiert ein Anzeigename, der ggf. vom technischen Namen abweichen kann,
 um eine geläufigere Bezeichnung darzustellen.
 Sparten sind hierarchisch aufgebaut und können bis zu drei Tiefenstufen umfassen
 (z.B. „Krankenversicherung“ → „Private Krankenversicherung“ → „Zusatzversicherung ambulant“).')]
class SparteDTO extends Data
{
    public function __construct(
        #[OA\Property(example: 132)]
        public int $id,
        #[OA\Property(example: 'Krankenversicherung')]
        public string $name,
        #[OA\Property(example: 'KV')]
        public ?string $abkuerzung,
        #[OA\Property(example: 'Krankenversicherung (KV)')]
        #[MapInputName('display_name')]
        public string $anzeigename,
    ) {
    }
}
