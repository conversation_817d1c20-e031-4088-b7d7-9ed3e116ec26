<?php

declare(strict_types=1);

namespace App\Domain\ApiGateway\DTO\Response\v1;

use App\Enums\TimelineElementType;
use OpenApi\Attributes as OA;
use Spatie\LaravelData\Data;

#[OA\Schema(
    schema: 'TimelineElement',
    description: 'Ein Timeline-Element repräsentiert einen Eintrag in der chronologischen Vorgangs-Zeitleiste.
    Es dient als Container für verschiedene Typen von Aktionen oder Informationen,
    die während der Bearbeitung eines Vorgangs entstehen – z.B.
    <PERSON>spond<PERSON>zen, Gesprächsnotizen, Erinnerungen oder Statusänderungen.
    Der konkrete Inhalt wird über den Typ (elementType) bestimmt und verweist auf ein jeweils spezifisches Unterobjekt,
    das im Detail beschreibt, was genau passiert ist.',
    discriminator: new OA\Discriminator(
        propertyName: 'type',
        mapping: [
            TimelineElementType::KORRESPONDENZEN->value => '#/components/schemas/KorrespondenzElement',
            TimelineElementType::VERTRAG_AENDERUNGEN->value => '#/components/schemas/VertragAenderungenElement',
            TimelineElementType::USER_AENDERUNGEN->value => '#/components/schemas/UserAenderungElement',
            TimelineElementType::FOLGE_VORGANG->value => '#/components/schemas/FolgeVorgangTimelineElement',
            TimelineElementType::VERKNUEPFUNGEN->value => '#/components/schemas/VerknuepfungTimelineElement',
            TimelineElementType::LOESCH_KOMMENTARE->value => '#/components/schemas/LoeschKommentarElement',
            TimelineElementType::SYSTEM_KOMMENTARE->value => '#/components/schemas/SystemkommentarElement',
            TimelineElementType::KOMMENTARE->value => '#/components/schemas/KommentarElement',
            TimelineElementType::STATUS_AENDERUNGEN->value => '#/components/schemas/StatusAenderungenElement',
            TimelineElementType::FAELLIGKEIT_AENDERUNGEN->value => '#/components/schemas/FaelligkeitAenderungenElement',
            TimelineElementType::GESPRAECHS_NOTIZEN->value => '#/components/schemas/GespraechsnotizElement',
            TimelineElementType::ERINNERUNGEN->value => '#/components/schemas/ErinnerungElement',
            TimelineElementType::MAHNUNGEN->value => '#/components/schemas/MahnungElement',
            TimelineElementType::EXTERNE_KORRESPONDENZEN->value => '#/components/schemas/ExterneKorrespondenzElement',
        ]
    )
)]
class TimelineElementDTO extends Data
{
    public function __construct(
        #[OA\Property(example: 1)]
        public int $id,

        #[OA\Property(type: 'string', enum: TimelineElementType::class, example: TimelineElementType::KORRESPONDENZEN->value)]
        public TimelineElementType $elementType,

        #[OA\Property(oneOf: [
            new OA\Schema(ref: '#/components/schemas/KorrespondenzElement'),
            new OA\Schema(ref: '#/components/schemas/VertragAenderungenElement'),
            new OA\Schema(ref: '#/components/schemas/UserAenderungElement'),
            new OA\Schema(ref: '#/components/schemas/FolgeVorgangTimelineElement'),
            new OA\Schema(ref: '#/components/schemas/VerknuepfungTimelineElement'),
            new OA\Schema(ref: '#/components/schemas/LoeschKommentarElement'),
            new OA\Schema(ref: '#/components/schemas/SystemkommentarElement'),
            new OA\Schema(ref: '#/components/schemas/KommentarElement'),
            new OA\Schema(ref: '#/components/schemas/StatusAenderungenElement'),
            new OA\Schema(ref: '#/components/schemas/FaelligkeitAenderungenElement'),
            new OA\Schema(ref: '#/components/schemas/GespraechsnotizElement'),
            new OA\Schema(ref: '#/components/schemas/ErinnerungElement'),
            new OA\Schema(ref: '#/components/schemas/MahnungElement'),
            new OA\Schema(ref: '#/components/schemas/ExterneKorrespondenzElement'),
        ])]
        public object $element,

        #[OA\Property(example: '2025-03-20')]
        public string $created_at,
    ) {
    }
}
