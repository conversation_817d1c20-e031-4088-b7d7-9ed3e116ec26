<?php

declare(strict_types=1);

namespace App\Domain\ApiGateway\DTO\Response\v1;

use OpenApi\Attributes as OA;
use Spatie\LaravelData\Data;

#[OA\Schema(schema: 'Kunde', description: 'Repräsent<PERSON><PERSON> e<PERSON>,
wie er in Professional works gepflegt ist.')]
class KundeDTO extends Data
{
    public function __construct(
        #[OA\Property(example: 154)]
        public int $id,
        #[OA\Property(example: 'Max Mustermann')]
        public string $name,
    ) {
    }
}
