<?php

declare(strict_types=1);

namespace App\Domain\ApiGateway\DTO\Response\v1;

use OpenApi\Attributes as OA;
use Spatie\LaravelData\Data;

#[OA\Schema(schema: 'User',
    description: 'Stellt einen Nutzer innerhalb von Professional Works dar – typischerweise einen Makler oder Sachbearbeiter.
    Das Objekt enthält eine eindeutige Nutzer-ID sowie den Namen der Person und wird z.B.
    bei der Bearbeitung von Vorgängen oder als Autor von Aktionen referenziert.')]
class UserDTO extends Data
{
    public function __construct(
        #[OA\Property(example: 132)]
        public int $id,
        #[OA\Property(example: '<PERSON> Mustermann')]
        public string $name,
    ) {
    }
}
