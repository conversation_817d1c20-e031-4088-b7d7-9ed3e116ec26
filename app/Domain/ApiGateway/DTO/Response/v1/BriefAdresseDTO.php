<?php

namespace App\Domain\ApiGateway\DTO\Response\v1;

use OpenApi\Attributes as OA;
use Spatie\LaravelData\Data;

#[OA\Schema(schema: 'BriefAdresse', description: 'Enthält die Angaben zu einer Briefadresse.
 Dieses Objekt wird verwendet, um Absenderdaten in Briefen strukturiert darzustellen.')]
class BriefAdresseDTO extends Data
{
    public function __construct(
        #[OA\Property(example: '20095')]
        public string $zip,
        #[OA\Property(example: 'Hamburg')]
        public string $city,
        #[OA\Property(example: '1')]
        public string $number,
        #[OA\Property(example: 'Systemstraße')]
        public string $street,
        #[OA\Property(example: 'DEMV-System')]
        public string $company,
        #[OA\Property(example: 'Admin')]
        public string $lastname,
        #[OA\Property(example: 'DEMV')]
        public string $firstname,
    ) {
    }
}
