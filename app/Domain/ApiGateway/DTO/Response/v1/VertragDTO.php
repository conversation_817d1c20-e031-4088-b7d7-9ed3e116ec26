<?php

declare(strict_types=1);

namespace App\Domain\ApiGateway\DTO\Response\v1;

use OpenApi\Attributes as OA;
use Spatie\LaravelData\Attributes\MapInputName;
use Spatie\LaravelData\Data;

#[OA\Schema(schema: 'Vertrag',
    description: 'Abbildung eines konkreten Versicherungsvertrags innerhalb von Professional Works.
    Er enthält u.a. die Vertragsnummer, die zugehörige Kunden-ID, die verantwortliche Nutzer-ID,
    sowie Informationen zur Versicherungsgesellschaft und zur Sparte.
    Zusätzlich wird die sogenannte BaFin-Gesellschaft angegeben – in der Regel identisch mit der normalen Gesellschaft,
    es sei denn, es handelt sich um einen nicht regulierten Anbieter mit übergeordneter regulierter Gesellschaft (z.B. Adcuri → Barmenia).
    Diese Struktur erlaubt eine eindeutige fachliche und regulatorische Zuordnung des Vertrags.')]
class VertragDTO extends Data
{
    public function __construct(
        #[OA\Property(example: 1)]
        public int $id,
        #[OA\Property(example: 'V123456')]
        public string $vertragsnummer,
        #[OA\Property(example: 42)]
        public ?int $user_id,
        #[OA\Property(example: 123)]
        #[MapInputName('kunde_external_id')]
        public int $kunde_id,
        #[OA\Property(nullable: true)]
        public ?GesellschaftDTO $gesellschaft,
        #[OA\Property(nullable: true)]
        public ?GesellschaftDTO $bafin_gesellschaft,
        #[OA\Property(nullable: true)]
        public ?SparteDTO $sparte,
    ) {
    }
}
