<?php

declare(strict_types=1);

namespace App\Domain\ApiGateway\DTO\Response\v1;

use App\Domain\ApiGateway\Constants\V1VorgangsStatus;
use App\Enums\VorgangsStatus;
use Illuminate\Database\Eloquent\Collection;
use OpenApi\Attributes as OA;
use Spatie\LaravelData\Attributes\DataCollectionOf;
use Spatie\LaravelData\Attributes\MapInputName;
use Spatie\LaravelData\Data;

#[OA\Schema(schema: 'Vorgang',
    description: 'Ein Vorgang bildet eine abgeschlossene Arbeitseinheit in Professional Works ab – z.B. eine Korrespondenz,
    ein Antrag, eine Schadenmeldung oder eine Aufgabe.
    Er enthält einen Titel zur inhaltlichen Beschreibung, einen Vorgangstyp (z.B. E-Mail, Brief, Aufgabe),
    optionale Verknüpfungen zu Verträgen, einer Gesellschaft, einem Vertriebsweg (z.B. Fondsfinanz bei Pooling),
    sowie einer Sparte. Zudem umfasst ein Vorgang die komplette chronologische Timeline (Korrespondenzen, Gesprächsnotizen etc.)
    sowie das Erstellungs- und Fälligkeitsdatum.')]
class VorgangDTO extends Data
{
    public function __construct(
        #[OA\Property(example: 1)]
        public readonly int $id,
        #[OA\Property(example: 'Vorgang 1')]
        public readonly string $titel,
        #[OA\Property(ref: '#/components/schemas/VorgangType')]
        public readonly ?VorgangTypDTO $vorgang_typ,
        #[OA\Property(type: 'array', items: new OA\Items(ref: '#/components/schemas/Vertrag'))]
        #[DataCollectionOf(VertragDTO::class)]
        public readonly Collection $vertraege,
        #[OA\Property(ref: '#/components/schemas/Kunde')]
        public readonly ?KundeDTO $kunde,
        #[OA\Property(ref: '#/components/schemas/Gesellschaft')]
        public readonly ?GesellschaftDTO $gesellschaft,
        #[OA\Property(ref: '#/components/schemas/Gesellschaft')]
        public readonly ?GesellschaftDTO $vertriebsweg,
        #[OA\Property(ref: '#/components/schemas/Product')]
        public readonly ?SparteDTO $sparte,
        #[OA\Property(type: 'array', items: new OA\Items(ref: '#/components/schemas/TimelineElement'))]
        #[MapInputName('timeline'), DataCollectionOf(TimelineElementDTO::class)]
        public readonly Collection $timeline_elemente,
        #[OA\Property]
        public readonly string $created_at,
        #[OA\Property]
        public readonly string $faellig_at,
        #[OA\Property(type: 'string', enum: V1VorgangsStatus::class, example: 'offen')]
        public readonly VorgangsStatus $status,
    ) {
    }
}
