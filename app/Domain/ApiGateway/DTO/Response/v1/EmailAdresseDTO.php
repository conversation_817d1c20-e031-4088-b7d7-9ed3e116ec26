<?php

namespace App\Domain\ApiGateway\DTO\Response\v1;

use OpenApi\Attributes as OA;
use Spatie\LaravelData\Data;

#[OA\Schema(schema: 'EmailAdresse', description: 'Enthält die Angaben zu einer E-Mail-Adresse inklusive des zugehörigen Namens.
 Dieses Objekt wird verwendet, um sowohl Absender- als auch Empfängerdaten
 in E-Mail-Kommunikation strukturiert darzustellen – z.B. in Korrespondenzelementen oder beim Versand von Ma<PERSON>ungen.')]
class EmailAdresseDTO extends Data
{
    public function __construct(
        #[OA\Property(example: '<EMAIL>')]
        public string $email,
        #[OA\Property(example: 'Maria Musterfrau')]
        public string $name,
    ) {
    }
}
