<?php

declare(strict_types=1);

namespace App\Domain\ApiGateway\DTO\Response\v1;

use OpenApi\Attributes as OA;
use Spatie\LaravelData\Data;

#[OA\Schema(schema: 'Gesellschaft', description: 'Repräsentiert eine Versicherungsgesellschaft,
wie sie in Professional Works gepflegt ist. Die Gesellschaft besitzt eine eindeutige ID,
einen vollen Namen sowie optional eine gebräuchliche Abkürzung (z.B. „BVA“ für „Beispiel Versicherung AG“).
 Gesellschaften werden u.a. in Verträgen und Vorgängen referenziert.')]
class GesellschaftDTO extends Data
{
    public function __construct(
        #[OA\Property(example: 154)]
        public int $id,
        #[OA\Property(example: 'Beispiel Versicherung AG')]
        public string $name,
        #[OA\Property(example: 'BVA')]
        public string $abkuerzung,
    ) {
    }
}
