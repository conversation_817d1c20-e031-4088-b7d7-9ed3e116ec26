<?php

declare(strict_types=1);

namespace App\Domain\ApiGateway\DTO\Response\v1\TimelineElements;
use App\Domain\ApiGateway\DTO\Response\v1\UserDTO;
use OpenApi\Attributes as OA;

#[OA\Schema(schema: 'VertragAenderungenElement',
    description: 'Dieses Element zeigt an, dass der einem Vorgang zugeordnete Vertrag verändert wurde – z.B.
    durch Austausch oder Ergänzung. Die Vertragsverknüpfung ist optional, wird aber häufig verwendet,
    um Vorgänge mit spezifischen Verträgen aus dem Bestand zu verbinden.')]
readonly class VertragAenderungenElementDTO
{
    public function __construct(
        #[OA\Property(example: 132)]
        public int $id,
        #[OA\Property]
        public UserDTO $author,
    ) {
    }
}
