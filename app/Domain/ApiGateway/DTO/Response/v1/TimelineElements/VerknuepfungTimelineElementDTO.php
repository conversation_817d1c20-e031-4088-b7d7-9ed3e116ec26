<?php

declare(strict_types=1);

namespace App\Domain\ApiGateway\DTO\Response\v1\TimelineElements;

use OpenApi\Attributes as OA;

#[OA\Schema(schema: 'VerknuepfungTimelineElement')]
readonly class VerknuepfungTimelineElementDTO
{
    public function __construct(
        #[OA\Property(type: 'integer')]
        public int $id,
        #[OA\Property(enum: ['erstellt', 'entfernt'])]
        public string $aktion,
        #[OA\Property(type: 'integer')]
        public int $vorgang_id,
    ) {
    }
}
