<?php

declare(strict_types=1);

namespace App\Domain\ApiGateway\DTO\Response\v1\TimelineElements;

use App\Domain\ApiGateway\DTO\Response\v1\UserDTO;
use OpenApi\Attributes as OA;
use Spatie\LaravelData\Data;

#[OA\Schema(schema: 'LoeschKommentarElement',
    description: 'Dieses Element dokumentiert die Löschung eines anderen Timeline-Eintrags.
    Es enthält einen verpflichtenden Kommentar, der den Grund für die Löschung beschreibt, so<PERSON><PERSON> den <PERSON>utzer, der die Löschung durchgeführt hat.')]
class LoeschKommentarElementDTO extends Data
{
    public function __construct(
        #[OA\Property(type: 'integer')]
        public int $id,
        #[OA\Property]
        public UserDTO $user,
    ) {
    }
}
