<?php

declare(strict_types=1);

namespace App\Domain\ApiGateway\DTO\Response\v1\TimelineElements;

use OpenApi\Attributes as OA;

#[OA\Schema(
    schema: 'MahnungElement',
    description: 'Eine Mahnung stellt eine Eskalationsstufe gegenüber einer einfachen Erinnerung dar.
    Sie kann z.B. bei längerer Inaktivität auf einen offenen Vorgang hinweisen und wird explizit als Mahnung deklariert.
    Das Objekt enthält ähnliche Informationen wie das Erinnerungselement, ist jedoch stärker formalisiert.',
)]
class MahnungElementDTO extends KorrespondenzElementDTO
{
}
