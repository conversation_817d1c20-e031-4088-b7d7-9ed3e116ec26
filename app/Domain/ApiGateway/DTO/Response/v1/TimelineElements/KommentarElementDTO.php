<?php

declare(strict_types=1);

namespace App\Domain\ApiGateway\DTO\Response\v1\TimelineElements;

use OpenApi\Attributes as OA;
use Spatie\LaravelData\Data;

#[OA\Schema(schema: 'KommentarElement',
    description: 'Ein einfaches Textelement zur internen Dokumentation innerhalb eines Vorgangs.
    Anders als bei Gesprächsnotizen werden hier keine Metadaten wie Gesprächszeitraum oder Typ gespeichert –
    es handelt sich lediglich um einen freien Kommentartext.')]
class KommentarElementDTO extends Data
{
    public function __construct(
        #[OA\Property(example: 12345)]
        public int $id,
    ) {
    }
}
