<?php

declare(strict_types=1);

namespace App\Domain\ApiGateway\DTO\Response\v1\TimelineElements;

use App\Domain\ApiGateway\Constants\V1Versandart;
use App\Domain\ApiGateway\DTO\Response\v1\BriefAdresseDTO;
use App\Domain\ApiGateway\DTO\Response\v1\EmailAdresseDTO;
use App\Domain\Korrespondenz\Enums\Status;
use OpenApi\Attributes as OA;
use Spatie\LaravelData\Data;

#[OA\Schema(schema: 'KorrespondenzElement',
    description: 'Beschreibt eine vollständige ausgehende Korrespondenz im Rahmen eines Vorgangs,
    entweder per E-Mail oder Brief. Enthalten sind u.a. Betreff, Versandart, Versandzeitpunkt,
    Empfänger- und Absenderinformationen (inkl. CC und BCC),
    sowie der Versandstatus und potenzielle Fehlermeldungen.
    Auch bei Briefen werden Metadaten wie das Versanddatum oder der Typ des Absenders mitgeführt.
    Das KorrespondenzElement ist eines der zentralen Objekte für die externe Kommunikation mit Kunden oder Gesellschaften.')]
class KorrespondenzElementDTO extends Data
{
    public function __construct(
        #[OA\Property(example: 12345)]
        public int $id,
        #[OA\Property(enum: V1Versandart::class, example: V1Versandart::EMAIL->value)]
        public string $versandart,
        #[OA\Property(example: 'Ihre Anfrage vom 15.05.2023')]
        public string $betreff,
        #[OA\Property(example: 'Sehr geehrte Damen und Herren, ... Mit freundlichen Grüßen, ...')]
        public string $inhalt_text,
        #[OA\Property(format: 'date-time', example: '2023-05-20T14:30:00+02:00', nullable: true)]
        public ?string $versendet_at,
        #[OA\Property(items: new OA\Items(ref: '#/components/schemas/EmailAdresse'))]
        public array $empfaenger,
        #[OA\Property(oneOf: [new OA\Schema(ref: '#/components/schemas/EmailAdresse'), new OA\Schema(ref: '#/components/schemas/BriefAdresse')])]
        public EmailAdresseDTO|BriefAdresseDTO|null $absender,
        #[OA\Property(items: new OA\Items(type: 'string'), example: ['<EMAIL>'])]
        public array $cc,
        #[OA\Property(items: new OA\Items(type: 'string'), example: ['<EMAIL>'])]
        public array $bcc,
        #[OA\Property(enum: Status::class, example: Status::ABGESCHLOSSEN->value)]
        public string $status,
        #[OA\Property(example: 'Erfolgreich versendet', nullable: true)]
        public ?string $status_text,
        #[OA\Property(items: new OA\Items(type: 'string'), example: ['<EMAIL>'])]
        public array $cc_actual,
        #[OA\Property(items: new OA\Items(type: 'string'), example: ['<EMAIL>'])]
        public array $bcc_actual,
        #[OA\Property(items: new OA\Items(ref: '#/components/schemas/EmailAdresse'))]
        public array $empfaenger_actual,
        #[OA\Property(oneOf: [new OA\Schema(ref: '#/components/schemas/EmailAdresse'), new OA\Schema(ref: '#/components/schemas/BriefAdresse')])]
        public EmailAdresseDTO|BriefAdresseDTO|null $absender_actual,
        #[OA\Property(format: 'date', example: '2023-05-22', nullable: true)]
        public ?string $brief_datum,
        #[OA\Property(example: 'outgoing', nullable: true)]
        public ?string $email_type,
        #[OA\Property(example: 'gesellschaft', nullable: true)]
        public ?string $brief_sender_type,
        #[OA\Property(example: 'https://example.com/api/v1/vorgaenge/1/korrespondenzen/123/content', nullable: true)]
        public ?string $contentUrl,
    ) {
    }
}
