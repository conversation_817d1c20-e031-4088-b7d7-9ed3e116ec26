<?php

declare(strict_types=1);

namespace App\Domain\ApiGateway\DTO\Response\v1\TimelineElements;

use App\Domain\ApiGateway\DTO\Response\v1\UserDTO;
use OpenApi\Attributes as OA;

#[OA\Schema(schema: 'UserAenderungElement',
    description: 'Dokumentiert Änderungen bei den zugewiesenen Nutzern eines Vorgangs.
    Unterschieden wird zwischen Bearbeitern und Beobachtern – beide Rollen können hinzugefügt oder entfernt werden.
    Das Element speichert zudem den Autor der Änderung sowie den betroffenen Nutzer.')]
readonly class UserAenderungElementDTO
{
    public function __construct(
        #[OA\Property(type: 'integer')]
        public int $id,
        #[OA\Property(enum: ['hinzugefuegt', 'entfernt'], example: 'hinzugefuegt')]
        public string $aktion,
        #[OA\Property(enum: ['bearbeiter', 'beoabachter'], example: 'bearbeiter')]
        public string $task,
        #[OA\Property]
        public UserDTO $author,
        #[OA\Property]
        public UserDTO $assignedUser,
    ) {
    }
}
