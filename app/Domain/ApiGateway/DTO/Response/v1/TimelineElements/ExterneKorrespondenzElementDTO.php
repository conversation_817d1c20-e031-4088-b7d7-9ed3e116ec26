<?php

declare(strict_types=1);

namespace App\Domain\ApiGateway\DTO\Response\v1\TimelineElements;

use OpenApi\Attributes as OA;
use Spatie\LaravelData\Data;

#[OA\Schema(
    schema: 'ExterneKorrespondenzElement',
    description: 'Dieses Element dokumentiert, dass eine externe Korrespondenz (E-Mail als .eml oder .msg) hochgeladen und in den Vorgang importiert wurde.
    Die eigentliche Korrespondenz wird nicht in diesem Element dargestellt, sondern in einem separaten Korrespondenzelement.'
)]
class ExterneKorrespondenzElementDTO extends Data
{
    public function __construct(
        #[OA\Property(example: 12345)]
        public int $id,
        #[OA\Property(example: true, description: 'Gibt an, ob Anhänge aus der E-Mail mit in den Vorgang importiert wurden')]
        public bool $uses_attachments,
    ) {
    }
}
