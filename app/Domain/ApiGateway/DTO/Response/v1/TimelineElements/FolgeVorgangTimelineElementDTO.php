<?php

declare(strict_types=1);

namespace App\Domain\ApiGateway\DTO\Response\v1\TimelineElements;

use OpenApi\Attributes as OA;
use Spatie\LaravelData\Data;

#[OA\Schema(schema: 'FolgeVorgangTimelineElement',
    description: 'Dieses Element verknüpft zwei Vorgänge logisch miteinander:
    einen ursprünglichen Vorgang („Vorgänger“) und einen neu erstellten „Folgevorgang“.
    Es wird genutzt, wenn sich aus einem bestehenden Vorgang ein neuer Kontext ergibt,
    der getrennt weiterverfolgt werden soll – z.B.
    bei der Beantragung eines zusätzlichen Produkts oder einer neuen Sparte.')]
class FolgeVorgangTimelineElementDTO extends Data
{
    public function __construct(
        #[OA\Property(type: 'integer')]
        public int $id,
        #[OA\Property(description: 'ID des vorgaenger Vorgang', type: 'integer')]
        public int $vorgaenger,
        #[OA\Property(description: 'ID des nachfolgenden Vorgang',type: 'integer')]
        public int $nachfolger,
    ) {
    }
}
