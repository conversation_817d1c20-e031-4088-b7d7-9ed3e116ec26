<?php

declare(strict_types=1);

namespace App\Domain\ApiGateway\DTO\Response\v1\TimelineElements;

use App\Domain\Kommentar\Enums\GespraechType;
use Carbon\Carbon;
use OpenApi\Attributes as OA;
use Spatie\LaravelData\Data;

#[OA\Schema(schema: 'GespraechsnotizElement',
    description: 'Erfasst eine interne Gesprächsnotiz innerhalb eines Vorgangs.
    Die Notiz enthält Informationen über Gespräche mit Kunden,
    etwa bei Vor-Ort-Terminen, Telefonaten oder anderen Kontakten.
    Zusätzlich zum Inhalt können Beginn und Ende des Gesprächs sowie der Typ (z.B. telefonisch, vor Ort) dokumentiert werden.
    Die Notizen sind rein intern und für den Kunden nicht sichtbar.')]
class GespraechsnotizElementDTO extends Data
{
    public function __construct(
        #[OA\Property(type: 'integer', example: 12345)]
        public int $id,
        #[OA\Property(enum: GespraechType::class, example: GespraechType::PHONE->value)]
        public string $type,
        #[OA\Property(type: 'string', format: 'date', example: '2023-05-15')]
        public Carbon $date_from,
        #[OA\Property(type: 'string', format: 'date', example: '2023-05-15')]
        public Carbon $date_to,
    ) {
    }
}
