<?php

declare(strict_types=1);

namespace App\Domain\ApiGateway\DTO\Response\v1\TimelineElements;

use App\Enums\VorgangsStatus;
use OpenApi\Attributes as OA;
use Spatie\LaravelData\Data;

#[OA\Schema(schema: 'StatusAenderungenElement',
    description: 'Ein Timeline-Eintrag zur Dokumentation von Statusänderungen innerhalb eines Vorgangs – z.B<PERSON> von „offen“ zu „geschlossen“.
    Jeder Vorgang hat einen Status, der den aktuellen Bearbeitungsstand beschreibt.
    Dieses Element speichert sowohl den alten als auch den neuen Statuswert.')]
class StatusAenderungenElementDTO extends Data
{
    public function __construct(
        #[OA\Property(type: 'integer')]
        public int $id,
        #[OA\Property(enum: VorgangsStatus::class, example: 'offen')]
        public string $vorheriger_wert,
        #[OA\Property(enum: VorgangsStatus::class, example: 'erledigt')]
        public string $neuer_wert,
    ) {
    }
}
