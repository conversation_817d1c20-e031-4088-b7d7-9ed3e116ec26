<?php

declare(strict_types=1);

namespace App\Domain\ApiGateway\DTO\Response\v1\TimelineElements;

use OpenApi\Attributes as OA;

#[OA\Schema(
    schema: 'ErinnerungElement',
    description: 'Dieses Element dokumentiert eine Erinnerung, die im Rahmen eines Vorgangs versendet wurde –
    typischerweise dann, wenn vom Kunden oder einer Gesellschaft keine Rückmeldung erfolgt ist.
    Erinnerungen können manuell durch einen Nutzer ausgelöst werden und erscheinen in der Zeitleiste des Vorgangs.',
)]
class ErinnerungElementDTO extends KorrespondenzElementDTO
{
}
