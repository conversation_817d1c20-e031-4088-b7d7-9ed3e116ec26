<?php

declare(strict_types=1);

namespace App\Domain\ApiGateway\DTO\Response\v1\TimelineElements;

use OpenApi\Attributes as OA;
use Spatie\LaravelData\Data;

#[OA\Schema(schema: 'SystemkommentarElement',
    description: 'Ein automatischer Kommentar, der vom System hinzugefügt wurde – z.B.
    im Zuge einer bestimmten Aktion oder Regel. Diese Kommentare dienen der Nachvollziehbarkeit technischer Prozesse und sind Teil der Vorgangszeitleiste.')]
class SystemkommentarElementDTO extends Data
{
    public function __construct(
        #[OA\Property(type: 'integer')]
        public int $id,
    ) {
    }
}
