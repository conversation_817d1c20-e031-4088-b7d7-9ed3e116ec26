<?php

declare(strict_types=1);

namespace App\Domain\ApiGateway\DTO\Response\v1\TimelineElements;

use OpenApi\Attributes as OA;
use Spatie\LaravelData\Data;

#[OA\Schema(schema: 'FaelligkeitAenderungenElement',
    description: 'Dieses Element zeigt an, dass die Fälligkeit eines Vorgangs geändert wurde.
    Jeder Vorgang besitzt ein Fälligkeitsdatum, das z.B. zur internen Priorisierung dient.
    Das Element enthält den vorherigen sowie den neuen Fälligkeitstermin.
    Die Änderung kann durch Nutzer oder zukünftig durch Agenten vorgenommen werden.')]
class FaelligkeitAenderungenElementDTO extends Data
{
    public function __construct(
        #[OA\Property(type: 'integer', example: 12345)]
        public int $id,
        #[OA\Property(type: 'string', format: 'date-time', example: '2023-06-15T00:00:00+02:00', nullable: true)]
        public ?string $vorheriger_wert,
        #[OA\Property(type: 'string', format: 'date-time', example: '2023-06-30T00:00:00+02:00', nullable: true)]
        public ?string $neuer_wert,
    ) {
    }
}
