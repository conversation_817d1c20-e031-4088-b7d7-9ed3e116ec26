<?php

declare(strict_types=1);

namespace App\Domain\ApiGateway\DTO\Request\v1\Vorgang;

use OpenApi\Attributes as OA;
use Spatie\LaravelData\Attributes\Validation\Required;
use Spatie\LaravelData\Attributes\Validation\StringType;

#[OA\Schema(
    schema: 'DunkelverarbeitetVorgangStoreData',
    description: 'Request zum Erstellen eines neuen Vorgangs mit Typ dunkelverarbeitet',
    required: ['content'],
)]
class DunkelverarbeitetVorgangDTO extends BaseVorgangDTO
{
    public function __construct(
        #[OA\Property]
        #[Required, StringType]
        public string $content,
        string $titel,
        ?string $faellig_at = null,
        ?int $kunde_id = null,
        ?int $gesellschaft_id = null,
        ?int $sparte_id = null,
        ?array $vertrag_ids = null
    ) {
        parent::__construct($titel, $faellig_at, $kunde_id, $gesellschaft_id, $sparte_id, $vertrag_ids);
    }
}
