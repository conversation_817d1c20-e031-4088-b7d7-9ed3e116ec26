<?php

declare(strict_types=1);

namespace App\Domain\ApiGateway\DTO\Request\v1\Vorgang;

use App\Enums\BriefAbsenderType;
use Spatie\LaravelData\Attributes\Validation\Nullable;
use Spatie\LaravelData\Attributes\Validation\Required;
use Spatie\LaravelData\Attributes\Validation\Rule;
use Spatie\LaravelData\Attributes\Validation\StringType;
use OpenApi\Attributes as OA;

#[OA\Schema(
    schema: 'BriefVorgangStoreData',
    description: 'Request zum Erstellen eines neuen Vorgangs mit Typ korrespondenz_brief',
    required: ['empfaenger', 'brief_absender_typ', 'content']
)]
class BriefVorgangDTO extends BaseVorgangDTO
{
    public function __construct(
        #[OA\Property]
        #[Required, StringType]
        public string $content,
        string $titel,
        #[OA\Property(ref: '#/components/schemas/BriefEmpfaengerData')]
        #[Required]
        public BriefEmpfaengerDTO $empfaenger,
        #[OA\Property(type: 'string', enum: BriefAbsenderType::class, example: 'kunde')]
        #[Required, Rule('enum:' . BriefAbsenderType::class)]
        public BriefAbsenderType $brief_absender_typ,
        #[OA\Property]
        #[Nullable, StringType]
        public ?string $brief_datum = null,
        ?string $faellig_at = null,
        ?int $kunde_id = null,
        ?int $gesellschaft_id = null,
        ?int $sparte_id = null,
        ?array $vertrag_ids = null,
    ) {
        parent::__construct(
            titel: $titel,
            faellig_at: $faellig_at,
            kunde_id: $kunde_id,
            gesellschaft_id: $gesellschaft_id,
            sparte_id: $sparte_id,
            vertrag_ids: $vertrag_ids,
        );
    }

    public static function attributes(): array
    {
        return [
            'brief_datum' => 'Briefdatum',
            'brief_absender_typ' => 'Absender',
        ];
    }
}
