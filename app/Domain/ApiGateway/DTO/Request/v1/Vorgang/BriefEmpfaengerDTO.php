<?php

declare(strict_types=1);

namespace App\Domain\ApiGateway\DTO\Request\v1\Vorgang;

use App\Enums\SalutationType;
use Spatie\LaravelData\Attributes\Validation\Nullable;
use Spatie\LaravelData\Attributes\Validation\Required;
use Spatie\LaravelData\Attributes\Validation\Rule;
use Spatie\LaravelData\Attributes\Validation\StringType;
use Spatie\LaravelData\Data;
use OpenApi\Attributes as OA;

#[OA\Schema(
    schema: 'BriefEmpfaengerData',
    required: ['name', 'land', 'plz', 'stadt', 'adresszeile1', 'salutation_type'],
)]
class BriefEmpfaengerDTO extends Data
{
    public function __construct(

        #[OA\Property(example: 'Max Mustermann')]
        #[Required, StringType]
        public string $name,

        #[OA\Property(example: 'Deutschland')]
        #[Required, StringType]
        public string $land,

        #[OA\Property(example: '12345')]
        #[Required, StringType]
        public string $plz,

        #[OA\Property(example: 'Musterstadt')]
        #[Required, StringType]
        public string $stadt,

        #[OA\Property(example: 'Musterstraße 1')]
        #[Required, StringType]
        public string $adresszeile1,

        #[OA\Property(type: 'string', enum: SalutationType::class, example: 'Herr')]
        #[Rule('enum:' . SalutationType::class)]
        public SalutationType $salutation_type,

        #[OA\Property(example: 'Dr.')]
        #[Nullable, StringType]
        public ?string $titel = null,

        #[OA\Property(example: '0123456789')]
        #[Nullable, StringType]
        public ?string $fax = null,

        #[OA\Property]
        #[Nullable, StringType]
        public ?string $adresszeile2 = null,
    ) {
    }

    public static function attributes(): array
    {
        return [
            'name' => 'Name',
            'titel' => 'Titel',
            'salutation_type' => 'Anrede',
            'fax' => 'Fax',
            'adresszeile1' => 'Adresszeile 1',
            'adresszeile2' => 'Adresszeile 2',
            'plz' => 'PLZ',
            'stadt' => 'Stadt',
            'land' => 'Land',
            'brief_datum' => 'Briefdatum',
            'brief_absender_typ' => 'Absender',
        ];
    }
}
