<?php

declare(strict_types=1);

namespace App\Domain\ApiGateway\DTO\Request\v1\Vorgang;

use App\Models\Gesellschaft;
use App\Models\Kunde;
use App\Models\Sparte;
use App\Models\User;
use App\Models\Vertrag;
use App\Models\VorgangTyp;
use Illuminate\Validation\Rule;
use OpenApi\Attributes as OA;
use Spatie\LaravelData\Attributes\MergeValidationRules;
use Spatie\LaravelData\Attributes\Validation\Exists;
use Spatie\LaravelData\Attributes\Validation\IntegerType;
use Spatie\LaravelData\Attributes\Validation\Max;
use Spatie\LaravelData\Attributes\Validation\Min;
use Spatie\LaravelData\Attributes\Validation\Nullable;
use Spatie\LaravelData\Attributes\Validation\StringType;
use Spatie\LaravelData\Data;

#[OA\Parameter(
    parameter: 'kunde_id',
    name: 'kunde_id',
    description: 'Nach Kunden-ID filtern',
    in: 'query',
    required: false,
    schema: new OA\Schema(type: 'integer')
)]
#[OA\Parameter(
    parameter: 'vertrag_ids',
    name: 'vertrag_ids',
    description: 'Nach Vertrags-IDs filtern',
    in: 'query',
    required: false,
    schema: new OA\Schema(type: 'array', items: new OA\Items(type: 'integer'))
)]
#[OA\Parameter(
    parameter: 'gesellschaft_id',
    name: 'gesellschaft_id',
    description: 'Nach Versicherungsgesellschafts-ID filtern',
    in: 'query',
    required: false,
    schema: new OA\Schema(type: 'integer')
)]
#[OA\Parameter(
    parameter: 'sparte_id',
    name: 'sparte_id',
    description: 'Nach Sparten-ID filtern',
    in: 'query',
    required: false,
    schema: new OA\Schema(type: 'integer')
)]
#[OA\Parameter(
    parameter: 'status',
    name: 'status',
    description: 'Nach Status filtern',
    in: 'query',
    required: false,
    schema: new OA\Schema(type: 'string')
)]
#[OA\Parameter(
    parameter: 'vorgangsart',
    name: 'vorgangsart',
    description: 'Nach Vorgangsart filtern',
    in: 'query',
    required: false,
    schema: new OA\Schema(ref: '#/components/schemas/Vorgangsart')
)]
#[OA\Parameter(
    parameter: 'vorgang_typ_id',
    name: 'vorgang_typ_id',
    description: 'Nach Vorgangstyp-ID filtern',
    in: 'query',
    required: false,
    schema: new OA\Schema(type: 'integer')
)]
#[OA\Parameter(
    parameter: 'bearbeiter_id',
    name: 'bearbeiter_id',
    description: 'Nach Bearbeiter-User-ID filtern',
    in: 'query',
    required: false,
    schema: new OA\Schema(type: 'integer')
)]
#[OA\Parameter(
    parameter: 'beobachter_id',
    name: 'beobachter_id',
    description: 'Nach Beobachter-User-ID filtern',
    in: 'query',
    required: false,
    schema: new OA\Schema(type: 'integer')
)]
#[OA\Parameter(
    parameter: 'participant_id',
    name: 'participant_id',
    description: 'Nach Teilnehmer-User-ID (Bearbeiter oder Beobachter) filtern',
    in: 'query',
    required: false,
    schema: new OA\Schema(type: 'integer')
)]
#[OA\Parameter(
    parameter: 'per_page',
    name: 'per_page',
    description: 'Anzahl der Elemente pro Seite',
    in: 'query',
    required: false,
    schema: new OA\Schema(type: 'integer', default: 25, minimum: 1, maximum: 100)
)]
#[OA\Parameter(
    parameter: 'cursor',
    name: 'cursor',
    description: 'Cursor für Paginierung',
    in: 'query',
    required: false,
    schema: new OA\Schema(type: 'string')
)]
#[MergeValidationRules]
class VorgangIndexDTO extends Data
{
    public function __construct(
        #[Nullable, IntegerType, Exists(Kunde::class, 'external_id')]
        public ?int $kunde_id = null,

        //validation is defined in the rules method because we can't validate array items with attributes yet
        public ?array $vertrag_ids = null,

        #[Nullable, IntegerType, Exists(Gesellschaft::class, 'external_id')]
        public ?int $gesellschaft_id = null,

        #[Nullable, IntegerType, Exists(Sparte::class, 'id')]
        public ?int $sparte_id = null,

        #[Nullable, StringType]
        public ?string $status = null,

        #[Nullable, StringType]
        public ?string $vorgangsart = null,

        #[Nullable, IntegerType, Exists(VorgangTyp::class, 'id')]
        public ?int $vorgang_typ_id = null,

        #[Nullable, IntegerType, Exists(User::class, 'external_id')]
        public ?int $bearbeiter_id = null,

        #[Nullable, IntegerType, Exists(User::class, 'external_id')]
        public ?int $beobachter_id = null,

        #[Nullable, IntegerType, Exists(User::class, 'external_id')]
        public ?int $participant_id = null,

        #[Nullable, IntegerType, Min(1), Max(100)]
        public ?int $per_page = null,

        #[Nullable, StringType]
        public ?string $cursor = null,
    ) {
    }

    public static function attributes(): array
    {
        return [
            'kunde_id' => 'Kunde',
            'vertrag_ids' => 'Verträge',
            'vertrag_ids.*' => 'Vertrag',
            'gesellschaft_id' => 'Gesellschaft',
            'sparte_id' => 'Sparte',
            'status' => 'Status',
            'vorgangsart' => 'Vorgangsart',
            'vorgang_typ_id' => 'Vorgangstyp',
            'bearbeiter_id' => 'Bearbeiter',
            'beobachter_id' => 'Beobachter',
            'participant_id' => 'Teilnehmer',
            'per_page' => 'Elemente pro Seite',
            'cursor' => 'Cursor',
        ];
    }

    public static function rules(): array
    {
        return [
            'vertrag_ids' => [
                'nullable',
                'array',
            ],
            'vertrag_ids.*' => [
                'integer',
                Rule::exists(Vertrag::class, 'external_id'),
            ],
        ];
    }
}
