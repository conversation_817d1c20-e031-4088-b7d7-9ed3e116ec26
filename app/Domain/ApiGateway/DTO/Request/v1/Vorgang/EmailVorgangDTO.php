<?php

declare(strict_types=1);

namespace App\Domain\ApiGateway\DTO\Request\v1\Vorgang;

use Spatie\LaravelData\Attributes\DataCollectionOf;
use Spatie\LaravelData\Attributes\Validation\Max;
use Spatie\LaravelData\Attributes\Validation\Required;
use Spatie\LaravelData\Attributes\Validation\StringType;
use Spatie\LaravelData\DataCollection;
use OpenApi\Attributes as OA;

#[OA\Schema(
    schema: 'EmailVorgangStoreData',
    description: 'Request zum Erstellen eines neuen Vorgangs mit Typ korrespondenz_email',
    required: ['empfaenger', 'betreff', 'content']
)]
class EmailVorgangDTO extends BaseVorgangDTO
{
    public function __construct(
        #[OA\Property]
        #[Required, StringType]
        public string $content,

        string $titel,

        #[OA\Property(example: 'Betreff der Email')]
        #[Required, StringType, Max(255)]
        public string $betreff,

        #[OA\Property(ref: '#/components/schemas/EmailEmpfaengerData')]
        #[Required, DataCollectionOf(EmailEmpfaengerDTO::class)]
        public DataCollection $empfaenger,

        #[OA\Property(type: 'array', items: new OA\Items(ref: '#/components/schemas/EmailEmpfaengerData'))]
        #[DataCollectionOf(EmailEmpfaengerDTO::class)]
        public ?DataCollection $cc = null,

        #[OA\Property(type: 'array', items: new OA\Items(ref: '#/components/schemas/EmailEmpfaengerData'))]
        #[DataCollectionOf(EmailEmpfaengerDTO::class)]
        public ?DataCollection $bcc = null,
        ?int $gesellschaft_id = null,
        ?int $sparte_id = null,
        ?array $vertrag_ids = null,
        ?string $faellig_at = null,
        ?int $kunde_id = null,
    ) {
        parent::__construct(
            titel: $titel,
            faellig_at: $faellig_at,
            kunde_id: $kunde_id,
            gesellschaft_id: $gesellschaft_id,
            sparte_id: $sparte_id,
            vertrag_ids: $vertrag_ids,
        );
    }

    public static function attributes(): array
    {
        return [
            'betreff' => 'Betreff',
            'cc' => 'CC',
            'cc.*.name' => 'Name des CC-Empfängers',
            'cc.*.email' => 'E-Mail des CC-Empfängers',
            'bcc' => 'BCC',
            'bcc.*.name' => 'Name des BCC-Empfängers',
            'bcc.*.email' => 'E-Mail des BCC-Empfängers',
            'faellig_at' => 'Fälligkeitsdatum',
            'gesellschaft_id' => 'Gesellschaft',
            'sparte_id' => 'Sparte',
            'vertrag_ids' => 'Verträge',
            'vertrag_ids.*' => 'Vertrag',
        ];
    }
}
