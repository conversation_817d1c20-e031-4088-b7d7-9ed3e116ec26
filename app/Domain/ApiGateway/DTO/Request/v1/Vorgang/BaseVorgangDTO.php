<?php

declare(strict_types=1);

namespace App\Domain\ApiGateway\DTO\Request\v1\Vorgang;

use OpenApi\Attributes as OA;
use Spatie\LaravelData\Attributes\Validation\ArrayType;
use Spatie\LaravelData\Attributes\Validation\Date;
use Spatie\LaravelData\Attributes\Validation\Exists;
use Spatie\LaravelData\Attributes\Validation\IntegerType;
use Spatie\LaravelData\Attributes\Validation\Max;
use Spatie\LaravelData\Attributes\Validation\Nullable;
use Spatie\LaravelData\Attributes\Validation\Required;
use Spatie\LaravelData\Attributes\Validation\StringType;
use Spatie\LaravelData\Data;

#[OA\Schema(
    required: ['titel'],
)]
abstract class BaseVorgangDTO extends Data
{
    public function __construct(
        #[OA\Property(maxLength: 255, example: 'Vorgangstitel')]
        #[Required, StringType, Max(255)]
        public string $titel,

        #[OA\Property(format: 'date', example: '2021-01-01')]
        #[Nullable, Date]
        public ?string $faellig_at = null,

        #[OA\Property]
        #[Nullable, IntegerType, Exists('kunden', 'external_id')]
        public ?int $kunde_id = null,

        #[OA\Property]
        #[Nullable, IntegerType, Exists('gesellschaften', 'external_id')]
        public ?int $gesellschaft_id = null,

        #[OA\Property]
        #[Nullable, IntegerType, Exists('sparten', 'id')]
        public ?int $sparte_id = null,

        #[OA\Property(items: new OA\Items(type: 'integer'), example: [1, 2, 3])]
        #[Nullable, ArrayType]
        public ?array $vertrag_ids = null,
    ) {
    }

    public static function attributes(): array
    {
        return [
            'titel' => 'Titel',
            'kunde_id' => 'Kunde',
            'content' => 'Inhalt',
            'faellig_at' => 'Fälligkeitsdatum',
            'gesellschaft_id' => 'Gesellschaft',
            'sparte_id' => 'Sparte',
            'vertrag_ids' => 'Verträge',
            'vertrag_ids.*' => 'Vertrag',
        ];
    }
}
