<?php

declare(strict_types=1);

namespace App\Domain\ApiGateway\DTO\Request\v1\Vorgang;
use OpenApi\Attributes as OA;
use Spatie\LaravelData\Attributes\Validation\Nullable;

#[OA\Schema(
    schema: 'VorgangsgruppeVorgangStoreData',
    description: 'Request zum Erstellen eines neuen Vorgangs mit Typ vorgangsgruppe',
)]
class VorgangsgruppeVorgangDTO extends BaseVorgangDTO
{
    public function __construct(
        string $titel,
        ?string $faellig_at = null,
    ) {
        parent::__construct(
            titel: $titel,
            faellig_at: $faellig_at,
        );
    }
}
