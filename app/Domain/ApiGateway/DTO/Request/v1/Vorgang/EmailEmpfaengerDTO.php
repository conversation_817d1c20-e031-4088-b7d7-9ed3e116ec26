<?php

declare(strict_types=1);

namespace App\Domain\ApiGateway\DTO\Request\v1\Vorgang;

use Spatie\LaravelData\Attributes\Validation\Email;
use Spatie\LaravelData\Attributes\Validation\Max;
use Spatie\LaravelData\Attributes\Validation\Required;
use Spatie\LaravelData\Attributes\Validation\StringType;
use Spatie\LaravelData\Data;
use OpenApi\Attributes as OA;

#[OA\Schema(
    schema: 'EmailEmpfaengerData',
    required: ['email'],
)]
class EmailEmpfaengerDTO extends Data
{
    public function __construct(
        #[OA\Property(example: '<EMAIL>')]
        #[Required, Email, Max(255)]
        public string $email,

        #[OA\Property(example: 'Max Mustermann')]
        #[StringType, Max(255)]
        public ?string $name = null,
    ) {
    }

    public static function attributes(): array
    {
        return [
            'name' => 'Name des Empfängers',
            'email' => 'E-Mail des Empfängers',
        ];
    }
}
