<?php

declare(strict_types=1);

namespace App\Domain\ApiGateway\DTO\Request\v1\TimelineElements;

use App\Domain\ApiGateway\DTO\Request\v1\Vorgang\EmailEmpfaengerDTO;
use OpenApi\Attributes as OA;
use Spatie\LaravelData\Attributes\DataCollectionOf;
use Spatie\LaravelData\Attributes\MergeValidationRules;
use Spatie\LaravelData\Attributes\Validation\BooleanType;
use Spatie\LaravelData\Attributes\Validation\Max;
use Spatie\LaravelData\Attributes\Validation\Nullable;
use Spatie\LaravelData\Attributes\Validation\Required;
use Spatie\LaravelData\Attributes\Validation\StringType;
use Spatie\LaravelData\Data;
use Spatie\LaravelData\DataCollection;

#[OA\Schema(
    schema: 'EmailKorrespondenzElementStoreData',
    description: "Request zum Erstellen einer neuen E-Mail-Korrespondenz in einem Vorgang",
    required: ['content', 'betreff', 'empfaenger'],
)]
#[MergeValidationRules]
class EmailKorrespondenzElementDTO extends Data
{
    public function __construct(
        #[OA\Property(type: 'string', maxLength: 65500, example: 'Inhalt der E-Mail')]
        #[Required, StringType, Max(65500)]
        public string $content,

        #[OA\Property(type: 'string', maxLength: 255, example: 'Betreff der E-Mail')]
        #[Required, StringType, Max(255)]
        public string $betreff,

        #[OA\Property(type: 'array', items: new OA\Items(ref: '#/components/schemas/EmailEmpfaengerData'))]
        #[DataCollectionOf(EmailEmpfaengerDTO::class)]
        public array $empfaenger,

        #[OA\Property(type: 'array', items: new OA\Items(ref: '#/components/schemas/EmailEmpfaengerData'))]
        #[DataCollectionOf(EmailEmpfaengerDTO::class)]
        public ?array $cc = null,

        #[OA\Property(type: 'array', items: new OA\Items(ref: '#/components/schemas/EmailEmpfaengerData'))]
        #[DataCollectionOf(EmailEmpfaengerDTO::class)]
        public ?array $bcc = null,

        #[OA\Property(
            description: 'Wenn true, wird der komplette Korrespondenzverlauf an die E-Mail angehängt',
            type: 'boolean',
            example: false,
            nullable: true
        )]
        #[Nullable, BooleanType]
        public ?bool $include_full_history = null,
    ) {
    }

    public static function attributes(): array
    {
        return [
            'content' => 'Inhalt',
            'betreff' => 'Betreff',
            'empfaenger' => 'Empfänger',
            'empfaenger.*.name' => 'Name des Empfängers',
            'empfaenger.*.email' => 'E-Mail des Empfängers',
            'cc' => 'CC-Empfänger',
            'cc.*.name' => 'Name des CC-Empfängers',
            'cc.*.email' => 'E-Mail des CC-Empfängers',
            'bcc' => 'BCC-Empfänger',
            'bcc.*.name' => 'Name des BCC-Empfängers',
            'bcc.*.email' => 'E-Mail des BCC-Empfängers',
            'include_full_history' => 'Kompletten Korrespondenzverlauf anhängen',
        ];
    }

    public static function rules(): array
    {
        return [
            'empfaenger' => 'required|array|min:1',
            'empfaenger.*.name' => 'nullable|string',
            'empfaenger.*.email' => 'required|email',
            'cc' => 'nullable|array',
            'cc.*.name' => 'nullable|string',
            'cc.*.email' => 'required_with:cc|email',
            'bcc' => 'nullable|array',
            'bcc.*.name' => 'nullable|string',
            'bcc.*.email' => 'required_with:bcc|email',
        ];
    }
}
