<?php

declare(strict_types=1);

namespace App\Domain\ApiGateway\DTO\Request\v1\TimelineElements;

use App\Domain\Kommentar\Enums\GespraechType;
use Carbon\Carbon;
use OpenApi\Attributes as OA;
use Spatie\LaravelData\Attributes\Validation\AfterOrEqual;
use Spatie\LaravelData\Attributes\Validation\Date;
use Spatie\LaravelData\Attributes\Validation\Enum;
use Spatie\LaravelData\Attributes\Validation\Max;
use Spatie\LaravelData\Attributes\Validation\Required;
use Spatie\LaravelData\Attributes\Validation\StringType;
use Spatie\LaravelData\Data;

#[OA\Schema(
    schema: 'GespraechsnotizElementStoreData',
    required: ['content', 'type', 'date_from', 'date_to']
)]
class GespraechsnotizElementDTO extends Data
{
    public function __construct(
        #[OA\Property(maxLength: 65500, example: 'Inhalt der Notiz')]
        #[Required, StringType, Max(65500)]
        public string $content,
        #[OA\Property(enum: GespraechType::class, example: GespraechType::PHONE->value)]
        #[Required, StringType, Enum(GespraechType::class)]
        public string $type,
        #[OA\Property(type: 'string', format: 'date-time', example: '2023-10-01T12:00:00Z')]
        #[Required, Date]
        public Carbon $date_from,
        #[OA\Property(type: 'string', format: 'date-time', example: '2023-10-01T13:00:00Z')]
        #[Required, Date, AfterOrEqual('date_from')]
        public Carbon $date_to,
    ) {
    }
}
