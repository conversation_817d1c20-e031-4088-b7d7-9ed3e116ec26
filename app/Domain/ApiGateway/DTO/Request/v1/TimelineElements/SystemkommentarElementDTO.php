<?php

declare(strict_types=1);

namespace App\Domain\ApiGateway\DTO\Request\v1\TimelineElements;

use OpenApi\Attributes as OA;
use Spatie\LaravelData\Attributes\Validation\BooleanType;
use Spatie\LaravelData\Attributes\Validation\Max;
use Spatie\LaravelData\Attributes\Validation\Nullable;
use Spatie\LaravelData\Attributes\Validation\Required;
use Spatie\LaravelData\Attributes\Validation\StringType;
use Spatie\LaravelData\Data;

#[OA\Schema(
    schema: 'SystemkommentarElementStoreData',
    required: ['content']
)]
class SystemkommentarElementDTO extends Data
{
    public function __construct(
        #[OA\Property(example: 'Inhalt des Systemkommentars')]
        #[Required, StringType, Max(65500)]
        public string $content,
        #[OA\Property(
            description: 'Standardmäßig ist dies false. Wenn true, wird der Vorgang für alle Bearbeiter/Beobachter als ungelesen markiert.',
            example: true,
            nullable: true,
        )]
        #[Nullable, BooleanType]
        public ?bool $notify_participants = null,
    ) {
    }
}
