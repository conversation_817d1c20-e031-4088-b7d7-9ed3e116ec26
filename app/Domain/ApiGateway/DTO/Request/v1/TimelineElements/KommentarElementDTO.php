<?php

namespace App\Domain\ApiGateway\DTO\Request\v1\TimelineElements;

use OpenApi\Attributes as OA;
use Spatie\LaravelData\Attributes\Validation\Max;
use Spatie\LaravelData\Attributes\Validation\Required;
use Spatie\LaravelData\Attributes\Validation\StringType;
use Spatie\LaravelData\Data;

#[OA\Schema(
    schema: 'KommentarElementStoreData',
    required: ['content']
)]
class KommentarElementDTO extends Data
{
    public function __construct(
        #[OA\Property(maxLength: 65500, example: 'Inhalt des Kommentars')]
        #[Required, StringType, Max(65500)]
        public string $content,
    ) {
    }
}
