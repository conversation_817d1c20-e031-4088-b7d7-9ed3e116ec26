<?php

declare(strict_types=1);

namespace App\Domain\ApiGateway;

use App\Domain\ApiGateway\Converters\ConverterServiceProvider;
use App\Gateway\MailerGateway;
use App\Providers\DomainServiceProvider;
use Demv\SdkFramework\Gateway\GatewayInterface;
use Illuminate\Support\Facades\Route;

use OpenApi\Attributes as OA;

#[OA\Info(
    version: '1.0',
    description: 'API for managing Vorgaenge',
    title: 'Vorgaenge API',
    contact: new OA\Contact(
        name: 'Team Magma',
        email: '<EMAIL>'
    )
)]

#[OA\Server(
    url: 'vorgaenge.professional.works',
)]

#[OA\Tag(
    name: 'Vorgaenge',
)]
class ApiGatewayServiceProvider extends DomainServiceProvider
{
    public function register(): void
    {
        parent::register();

        $this->app->register(ConverterServiceProvider::class);
    }

    public function registerApiGatewayRoutes(): void
    {
        Route::prefix('/v1')
            ->group(function () {
            $this->loadRoutesFrom(__DIR__ . '/routes/v1.php');
        });
    }
}
