### Create a new Vorgang with type korrespondenz_email
# This endpoint creates a new Vorgang with the type "korrespondenz_email"
# Requires authentication token
# Possible responses:
# - 201: Successfully created with Vorgang ID and vorgangsnummer
# - 403: Forbidden - Unauthorized to perform this action
# - 422: Validation Error - The given data was invalid

POST {{url}}/api/v1/vorgaenge/email
Authorization: Bearer {{access_token}}
Content-Type: application/json
Accept: application/json

{
    "titel": "Email Korrespondenz 2",
    "kunde_id": 1,
    "betreff": "Betreff der Email",
    "content": "Inhalt der Email",
    "empfaenger": [
        {
            "name": "<PERSON>",
            "email": "<EMAIL>"
        }
    ],
    "cc": [
        {
            "name": "<PERSON><PERSON>",
            "email": "<EMAIL>"
        }
    ],
    "bcc": [
        {
            "name": "<PERSON>",
            "email": "<EMAIL>"
        }
    ],
    "faellig_at": "2023-12-31",
    "gesellschaft_id": 1,
    "vertrag_ids": [4]
}

### Variables
### Der access_token kann hier manuell eingetragen werden oder sich über die "auth.http" file gezogen werden.
# @access_token
