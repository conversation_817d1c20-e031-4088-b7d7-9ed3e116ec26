### Get a single Vorgang
# This endpoint retrieves a single Vorgang by its ID
# Requires authentication token
# Possible responses:
# - 200: Successful response with Vorgang data
# - 403: Forbidden - Unauthorized to perform this action
# - 404: Not Found - Target entity was not found


GET {{url}}/api/v1/vorgaenge/{{vorgangId}}
Authorization: Bearer {{access_token}}
Accept: application/json


### Get all Vorgaenge
GET {{url}}/api/v1/vorgaenge?
    per_page=1&
    kunde_id=4199&
    sparte_id=202&
    status=offen
Authorization: Bearer {{access_token}}
Accept: application/json


### Variables
@vorgangId = 1
### Der access_token kann hier manuell eingetragen werden oder sich über die "auth.http" file gezogen werden.
# @access_token
