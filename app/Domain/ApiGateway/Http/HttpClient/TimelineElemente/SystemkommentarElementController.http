### Create a new SystemkommentarElement in Vorgang timeline
# This endpoint creates a new KommentarElement
# Requires authentication token
# Possible responses:
# - 201: Successful
# - 403: Forbidden
# - 422: Validation Error

POST {{url}}/api/v1/vorgaenge/{{vorgang}}/systemkommentare
Authorization: Bearer {{access_token}}
Content-Type: application/json
Accept: application/json

{
    "content": "Ein Beispielsystemkommentar",
    "notify_participants": true
}

### Variables
@vorgang = 1
