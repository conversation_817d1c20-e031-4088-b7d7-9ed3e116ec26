### Create a new Vorgang with type aufgabe
# This endpoint creates a new Vorgang with the type "aufgabe"
# Requires authentication token
# Possible responses:
# - 201: Successfully created with Vorgang ID and vorgangsnummer
# - 403: Forbidden - Unauthorized to perform this action
# - 422: Validation Error - The given data was invalid

POST {{url}}/api/v1/vorgaenge/aufgabe
Authorization: Bearer {{access_token}}
Content-Type: application/json
Accept: application/json

{
    "titel": "Aufgabe",
    "content": "Aufgabe 123 Test"
}

### Variables
### Der access_token kann hier manuell eingetragen werden oder sich über die "auth.http" file gezogen werden.
# @access_token
