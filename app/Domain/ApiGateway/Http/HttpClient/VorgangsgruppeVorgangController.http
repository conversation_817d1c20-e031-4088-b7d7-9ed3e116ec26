### Create a new Vorgang with type vorgangsgruppe
# This endpoint creates a new Vorgang with the type "vorgangsgruppe"
# Requires authentication token
# Possible responses:
# - 201: Successfully created with Vorgang ID and vorgangsnummer
# - 403: Forbidden - Unauthorized to perform this action
# - 422: Validation Error - The given data was invalid

POST {{url}}/api/v1/vorgaenge/vorgangsgruppe
Authorization: Bearer {{access_token}}
Content-Type: application/json
Accept: application/json

{
    "titel": "Vorgangsgruppe Vorgang",
    "kunde_id": 1,
    "faellig_at": "2023-12-31",
    "notify_all_participants": true,
    "gesellschaft_id": 1,
    "sparte_id": 1,
    "vertrag_ids": [1, 2]
}

### Variables
### Der access_token kann hier manuell eingetragen werden oder sich über die "auth.http" file gezogen werden.
# @access_token
