### Create a new Vorgang with type dunkelverarbeitet
# This endpoint creates a new Vorgang with the type "dunkelverarbeitet"
# Requires authentication token
# Possible responses:
# - 201: Successfully created with Vorgang ID and vorgangsnummer
# - 403: Forbidden - Unauthorized to perform this action
# - 422: Validation Error - The given data was invalid

POST {{url}}/api/v1/vorgaenge/dunkelverarbeitet
Authorization: Bearer {{access_token}}
Content-Type: application/json
Accept: application/json

{
    "titel": "Dunkelverarbeitet Vorgang",
    "kunde_id": 1,
    "content": "<p style=\"min-height: 1.5em; font-family: Arial, sans-serif; font-size: 12pt; color: #000;\">Inhalt des dunkelverarbeiteten Vorgangs</p>",
    "faellig_at": "2023-12-31",
    "gesellschaft_id": 1,
    "sparte_id": 1,
    "vertrag_ids": [1, 2]
}

### Variables
### Der access_token kann hier manuell eingetragen werden oder sich über die "auth.http" file gezogen werden.
# @access_token
