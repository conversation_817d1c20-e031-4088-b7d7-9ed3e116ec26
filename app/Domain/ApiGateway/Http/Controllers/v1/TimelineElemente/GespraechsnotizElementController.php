<?php

declare(strict_types=1);

namespace App\Domain\ApiGateway\Http\Controllers\v1\TimelineElemente;

use App\Domain\ApiGateway\DTO\Request\v1\TimelineElements\GespraechsnotizElementDTO;
use App\Domain\ApiGateway\Http\Requests\v1\GespraechsnotizElementStoreRequest;
use App\Domain\ApiGateway\Services\TimelineElementCreateService;
use App\Domain\Kommentar\Enums\GespraechType;
use App\Domain\Kommentar\Models\GespraechsnotizElement;
use App\Http\Controllers\Controller;
use App\Models\Vorgang;
use Carbon\Carbon;
use Illuminate\Http\Response;
use OpenApi\Attributes as OA;

class GespraechsnotizElementController extends Controller
{
    #[OA\Post(
        path: '/api/v1/vorgaenge/{vorgang}/gespraechsnotizen',
        operationId: 'storeGespraechsnotizElement',
        description: 'Erstellt eine neue Gesprächsnotiz in einem bestehenden Vorgang',
        requestBody: new OA\RequestBody(
            required: true,
            content: new OA\JsonContent(ref: "#/components/schemas/GespraechsnotizElementStoreData")
        ),
        tags: ['Vorgaenge'],
        parameters: [
            new OA\Parameter(
                name: 'vorgang',
                description: 'ID des Vorgangs, zu dem die Gesprächsnotiz hinzugefügt werden soll',
                in: 'path',
                required: true,
                schema: new OA\Schema(type: 'integer')
            )
        ]
    )]
    #[OA\Response(
        response: 201,
        description: "Successful response",
    )]
    #[OA\Response(
        response: 403,
        description: "Forbidden",
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: "message", type: "string", example: "Forbidden")
            ]
        )
    )]
    #[OA\Response(
        response: 404,
        description: "Not Found",
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: "message", type: "string", example: "Not Found")
            ]
        )
    )]
    #[OA\Response(
        response: 422,
        description: "Validation Error",
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: "message", type: "string", example: "The given data was invalid."),
                new OA\Property(property: "errors", type: "object")
            ]
        )
    )]
    public function store(
        Vorgang $vorgang,
        GespraechsnotizElementDTO $data,
        TimelineElementCreateService $service
    ): Response {
        $this->authorize('update', $vorgang);
        $this->authorize('create', GespraechsnotizElement::class);

        $service->createGespraechsnotizElement(
            vorgang: $vorgang,
            content: $data->content,
            type: GespraechType::from($data->type),
            date_from: Carbon::parse($data->date_from),
            date_to: Carbon::parse($data->date_to)
        );

        return response()->noContent(201);
    }
}
