<?php

declare(strict_types=1);

namespace App\Domain\ApiGateway\Http\Controllers\v1\TimelineElemente;

use App\Domain\ApiGateway\DTO\Request\v1\TimelineElements\EmailKorrespondenzElementDTO;
use App\Domain\ApiGateway\Services\TimelineElementCreateService;
use App\Http\Controllers\Controller;
use App\Models\Vorgang;
use Illuminate\Http\Response;
use OpenApi\Attributes as OA;

class EmailKorrespondenzElementController extends Controller
{
    #[OA\Post(
        path: '/api/v1/vorgaenge/{vorgang}/nachrichten',
        operationId: 'storeEmailElement',
        description: 'Erstellt eine neue Nachricht (E-Mail) in einem bestehenden Vorgang',
        requestBody: new OA\RequestBody(
            required: true,
            content: new OA\JsonContent(ref: "#/components/schemas/EmailKorrespondenzElementStoreData")
        ),
        tags: ['Vorgaenge'],
        parameters: [
            new OA\Parameter(
                name: 'vorgang',
                description: 'ID des Vorgangs, zu dem die Nachricht hinzugefügt werden soll',
                in: 'path',
                required: true,
                schema: new OA\Schema(type: 'integer')
            )
        ]
    )]
    #[OA\Response(
        response: 201,
        description: "Successful response",
    )]
    #[OA\Response(
        response: 403,
        description: "Forbidden",
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: "message", type: "string", example: "Forbidden")
            ]
        )
    )]
    #[OA\Response(
        response: 404,
        description: "Not Found",
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: "message", type: "string", example: "Not Found")
            ]
        )
    )]
    #[OA\Response(
        response: 422,
        description: "Validation Error",
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: "message", type: "string", example: "The given data was invalid."),
                new OA\Property(property: "errors", type: "object")
            ]
        )
    )]
    public function store(
        Vorgang $vorgang,
        EmailKorrespondenzElementDTO $data,
        TimelineElementCreateService $service
    ): Response {
        $this->authorize('update', $vorgang);

        $service->createEmailKorrespondenzElement(
            vorgang: $vorgang,
            betreff: $data->betreff,
            content: $data->content,
            empfaenger: $data->empfaenger,
            cc: $data->cc,
            bcc: $data->bcc,
            includeFullHistory: $data->include_full_history,
        );

        return response()->noContent(201);
    }
}
