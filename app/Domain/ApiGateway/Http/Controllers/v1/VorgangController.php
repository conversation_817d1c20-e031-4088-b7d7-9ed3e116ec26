<?php

declare(strict_types=1);

namespace App\Domain\ApiGateway\Http\Controllers\v1;

use App\Domain\ApiGateway\DTO\Response\v1\VorgangDTO;
use App\Domain\ApiGateway\Filters\VorgangFilter;
use App\Domain\ApiGateway\DTO\Request\v1\Vorgang\VorgangIndexDTO;
use App\Domain\ApiGateway\Repositories\VorgangRepository;
use App\Domain\Korrespondenz\Models\ErinnerungElement;
use App\Domain\Korrespondenz\Models\KorrespondenzElement;
use App\Domain\Korrespondenz\Models\MahnungElement;
use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Vorgang;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Http\JsonResponse;
use Illuminate\Pagination\AbstractCursorPaginator;
use Illuminate\Support\Facades\Auth;
use OpenApi\Attributes as OA;

class VorgangController extends Controller
{
    public function __construct(
        private readonly VorgangRepository $vorgangRepository
    ) {
        $this->authorizeResource(Vorgang::class, 'vorgang');
    }

    #[OA\Get(
        path: '/api/v1/vorgaenge',
        operationId: 'listVorgaenge',
        description: 'Listet alle Vorgaenge mit Filtern und Paginierung',
        tags: ['Vorgaenge']
    )]
    #[OA\Parameter(ref: '#/components/parameters/kunde_id')]
    #[OA\Parameter(ref: '#/components/parameters/vertrag_ids')]
    #[OA\Parameter(ref: '#/components/parameters/gesellschaft_id')]
    #[OA\Parameter(ref: '#/components/parameters/sparte_id')]
    #[OA\Parameter(ref: '#/components/parameters/status')]
    #[OA\Parameter(ref: '#/components/parameters/vorgangsart')]
    #[OA\Parameter(ref: '#/components/parameters/vorgang_typ_id')]
    #[OA\Parameter(ref: '#/components/parameters/bearbeiter_id')]
    #[OA\Parameter(ref: '#/components/parameters/beobachter_id')]
    #[OA\Parameter(ref: '#/components/parameters/participant_id')]
    #[OA\Parameter(ref: '#/components/parameters/per_page')]
    #[OA\Parameter(ref: '#/components/parameters/cursor')]
    #[OA\Response(
        response: 200,
        description: 'Erfolgreiche Antwort',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(
                    property: 'data',
                    type: 'array',
                    items: new OA\Items(ref: '#/components/schemas/Vorgang')
                ),
                new OA\Property(
                    property: 'meta',
                    properties: [
                        new OA\Property(property: 'next_cursor', type: 'string', nullable: true),
                        new OA\Property(property: 'prev_cursor', type: 'string', nullable: true),
                        new OA\Property(property: 'per_page', type: 'integer'),
                        new OA\Property(property: 'total', type: 'integer')
                    ],
                    type: 'object'
                )
            ]
        )
    )]
    #[OA\Response(
        response: 403,
        description: 'Verboten - Nicht autorisiert, diese Aktion auszuführen',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'message', type: 'string', example: 'This action is unauthorized.')
            ]
        )
    )]
    public function index(VorgangIndexDTO $data): JsonResponse
    {
        $perPage = $data->per_page ?? 25;
        $cursor = $data->cursor;

        /** @var User $user */
        $user = Auth::user();

        $filter = new VorgangFilter(
            kundeId: $data->kunde_id,
            vertragIds: $data->vertrag_ids ?? [],
            gesellschaftId: $data->gesellschaft_id,
            sparteId: $data->sparte_id,
            status: $data->status,
            vorgangsart: $data->vorgangsart,
            vorgangTypId: $data->vorgang_typ_id,
            bearbeiterId: $data->bearbeiter_id,
            beobachterId: $data->beobachter_id,
            participantId: $data->participant_id,
        );
        $query = $this->vorgangRepository->buildFilteredQuery($user, $filter);

        $total = $query->count();
        /** @var AbstractCursorPaginator $paginator */
        $paginator = $query->cursorPaginate($perPage, cursor: $cursor);

        $vorgaenge = VorgangDTO::collect($paginator->items());

        return response()->json([
            'data' => $vorgaenge,
            'meta' => [
                'next_cursor' => $paginator->nextCursor()?->encode(),
                'prev_cursor' => $paginator->previousCursor()?->encode(),
                'per_page' => $perPage,
                'total' => $total,
            ]
        ]);
    }

    #[OA\Get(
        path: '/api/v1/vorgaenge/{id}',
        operationId: 'showVorgang',
        summary: 'Zeigt einen Vorgang',
        description: 'Gibt die Details eines einzelnen Vorgangs zurück, einschließlich aller zugehörigen Timeline-Elemente und Verknüpfungen.',
        tags: ['Vorgaenge']
    )]
    #[OA\Parameter(
        name: 'id',
        in: 'path',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Response(
        response: 200,
        description: 'Successful response',
        content: new OA\JsonContent(ref: '#/components/schemas/Vorgang')
    )]
    #[OA\Response(
        response: 403,
        description: 'Forbidden - Unauthorized to perform this action',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'message', type: 'string', example: 'This action is unauthorized.')
            ]
        )
    )]
    #[OA\Response(
        response: 404,
        description: 'Not Found - Target entity was not found',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'message', type: 'string', example: 'Record not found.')
            ]
        )
    )]
    public function show(Vorgang $vorgang): JsonResponse
    {
        $vorgang->load([
            'vorgangTyp',
            'vertraege',
            'vertraege.gesellschaft',
            'vertraege.bafinGesellschaft',
            'vertraege.sparte',
            'gesellschaft',
            'kunde',
            'sparte',
            'timeline',
            'timeline.element' => function (MorphTo $query): void {
                $query->constrain([
                    KorrespondenzElement::class => function (Builder $query): void {
                        $query->scopes('withContentText');
                    },
                    ErinnerungElement::class => function (Builder $query): void {
                        $query->scopes('withContentText');
                    },
                    MahnungElement::class => function (Builder $query): void {
                        $query->scopes('withContentText');
                    },
                ]);
            },
            'vertriebsweg',
        ]);

        $dto = VorgangDTO::from($vorgang);

        return response()->json(['data' => $dto]);
    }
}
