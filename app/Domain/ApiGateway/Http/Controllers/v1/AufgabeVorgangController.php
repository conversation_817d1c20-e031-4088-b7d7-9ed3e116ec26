<?php

namespace App\Domain\ApiGateway\Http\Controllers\v1;

use App\Domain\ApiGateway\DTO\Request\v1\Vorgang\AufgabeVorgangDTO;
use App\Domain\ApiGateway\Services\CreateVorgangService;
use App\Http\Controllers\Controller;
use App\Models\Vorgang;
use Illuminate\Http\JsonResponse;
use OpenApi\Attributes as OA;

class AufgabeVorgangController extends Controller
{
    public function __construct(
        private readonly CreateVorgangService $createVorgangService
    ) {
        $this->authorizeResource(Vorgang::class, 'vorgang');
    }

    #[OA\Post(
        path: '/api/v1/vorgaenge/aufgabe',
        operationId: 'storeAufgabeVorgang',
        description: 'Erstellt einen neuen Vorgang mit der Vorgangsart aufgabe und den angegebenen Daten',
        summary: 'Erstelle einen neuen Vorgang mit der Vorgangsart aufgabe',
        requestBody: new OA\RequestBody(
            required: true,
            content: new OA\JsonContent(ref: '#/components/schemas/AufgabeVorgangStoreData')
        ),
        tags: ['Vorgaenge']
    )]
    #[OA\Response(
        response: 201,
        description: 'Successful response',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'data', properties: [
                    new OA\Property(property: 'id', type: 'integer', example: 1),
                    new OA\Property(property: 'vorgangsnummer', type: 'string', example: 'V-12345'),
                ], type: 'object')
            ]
        )
    )]
    #[OA\Response(
        response: 422,
        description: 'Validation Error',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'message', type: 'string', example: 'The given data was invalid.'),
                new OA\Property(property: 'errors', type: 'object')
            ]
        )
    )]
    public function store(AufgabeVorgangDTO $data): JsonResponse
    {
        $vorgang = $this->createVorgangService->createAufgabeVorgang($data->toArray());

        return response()->json([
            'data' => [
                'id' => $vorgang->id,
                'vorgangsnummer' => $vorgang->vorgangsnummer,
            ]
        ], 201);
    }
}
