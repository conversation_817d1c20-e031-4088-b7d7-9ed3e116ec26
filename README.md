# Vorgangsmanager

Die neuen Vorgänge.

[![Powered by - Coffee](https://img.shields.io/badge/Powered_by-Coffee-2ea44f)](https://www.youtube.com/watch?v=dQw4w9WgXcQ)

## Erstes Setup

### 1. Traefik

Falls noch nicht geschehen, [local-docker-network](https://github.com/demvsystems/local-docker-network)
einrichten (siehe [Traefik Setup](https://github.com/demvsystems/local-docker-network#setup)).

In der Hosts-Datei die Einträge:

```
127.0.0.1 vorgaenge.demv.internal
127.0.0.1 vorgaenge.testing
127.0.0.1 openapi.vorgaenge.demv.internal
127.0.0.1 s3.vorgaenge.demv.internal
```

hinzufügen.

### 2. Environment vorbereiten

```
cp .env.example .env
cp .env.auth.example .env.auth
```
In der `.env` die Environment Variablen definieren.

#### User und Group Ids

```
id -u
id -g
```

<PERSON><PERSON><PERSON><PERSON>, dass `UID` (`id -u`) und `GID` (`id -g`) in der `.env` korrekt sind.

#### Token

Für die Kommunikation zwischen Vorgänge und PW muss ein Token erzeugt werden unter

http://professionalworks.demv.internal/auth/token/admin

Nutze dabei folgende Werte: Name `Vorgaenge`, Service Id `vorgaenge`, User `DemvSystem`.

In der relevanten `.env` den Token unter `PW_API_TOKEN` speichern.

#### Professional Works Public Key

Damit die Authentifizierung funktioniert, muss der Public-Key von Professional works übernommen werden. Dieser ist in AUTH_PUBLIC_KEY in
der `.env.auth` in der lokalen PW-instanz zu finden.

Dieser PublicKey muss in der `.env.auth` unter `PW_PUBLIC_KEY` eingetragen werden.

#### GITHUB_TOKEN

In `.env` muss unter GITHUB_TOKEN ein Github Access Token hinterlegt werden.

### 3. Container bauen
```
just setup
```

Die Vorgänge sollten jetzt lokal erreichbar sein unter:

http://vorgaenge.demv.internal

Falls ein File-Permission Fehler kommt muss das System File Handle Limit erhöht werden:
```
echo fs.inotify.max_user_watches=256000 | sudo tee -a /etc/sysctl.conf
sudo sysctl -p
```

### 4. Xdebug

Siehe: [Xdebug Einrichten](./docs/xdebug.md)

### 5. phpcs (Code Sniffer)

Siehe: [phpcs Einrichten](./docs/phpcs.md)

## Container starten / stoppen

Die Vorgänge können mittels docker compose gestartet werden:

    docker compose up -d
    docker compose stop

## Port für Frontend anpassen

Der Standardport ist 5173. Dieser kann in der `.env` unter `VITE_SERVER_PORT` angepasst werden.
Danach müssen die Container neugebaut werden.

## Testing

### Vitest (Unit)

Siehe: [Unit-Test Readme](./resources/js/tests/readme.md)

### Cypress (End to End)

#### Setup

1. `vorgaenge.testing` in `/etc/hosts` eintragen.

Die End to End Tests laufen auf dem Host-System und erfordern, dass yarn installiert ist.
- `just cypress-run` => Führt die Tests im Hintergrund aus
- `just cypress-open` => Öffnet die Testoberfläche

#### Datenbank Seeding erzwingen

Wenn eine neue Migration dazugekommen ist, sollte die Datei `/database/snapshots/cypress-dump.sql` gelöscht werden.
Hierzu kann der Befehl `just cypress-delete-db-dump` genutzt werden.
Beim nächsten Testdurchlauf wird diese automatisch neu erstellt und beinhaltet das aktualisierte Datenbankschema.

#### Mehr

Siehe: [E2E Readme](./cypress/readme.md)

### Design System

Um lokale Änderungen des Design-Systems in den Vorgängen zu testen,
ist im `node`-Container `yalc` vorinstalliert.

1. `yalc` lokal installieren
   ```
   mkdir ~/docker/yalc
   yarn global add yalc
   ```
2. Änderungen im DS mit `yalc` publishen (aus dem Ordner `design/packages/components`)
   ```
   yarn build
   yalc publish --store-folder ~/docker/yalc
   ```
3. Lokale Version des DS nutzen
   ```
   make yarn-link-local-ds
   ```
   Die Vorgänge nutzen nun die lokale Version des DS.
4. Um wieder zurück zur Version aus der `package.json` zu wechseln
   ```
   make yarn-unlink-local-ds
   ```

### Telescope (Logs)
Access Logs, Error Logs, SQL-Logs, ...

[vorgaenge.demv.internal/telescope/](http://vorgaenge.demv.internal/telescope/)
