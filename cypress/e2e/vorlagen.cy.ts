import { VorgangResource, VorlageResource } from '@/store/resources/types';
import { Document } from '@/types/jsonapi';

describe('vorlage', () => {
  it('creates a vorlagen', () => {
    // create vorlagen
    cy.fixture<VorgangResource>('vorlagen/brief')
      .then((vorlage) => {
        cy.request<Document<VorlageResource>>('POST', '/api/vorlagen', { data: vorlage })
          .then((response) => {
            expect(response.status).to.eq(201);
            expect(response).to.have.property('body');
          });
      });
    cy.fixture<VorgangResource>('vorlagen/mail')
      .then((vorlage) => {
        cy.request<Document<VorlageResource>>('POST', '/api/vorlagen', { data: vorlage })
          .then((response) => {
            expect(response.status).to.eq(201);
            expect(response).to.have.property('body');
          });
      });
    cy.fixture<VorgangResource>('vorlagen/kampagneMail')
      .then((vorlage) => {
        cy.request<Document<VorlageResource>>('POST', '/api/vorlagen', { data: vorlage })
          .then((response) => {
            expect(response.status).to.eq(201);
            expect(response).to.have.property('body');
          });
      });
    cy.fixture<VorgangResource>('vorlagen/kampagneBrief')
      .then((vorlage) => {
        cy.request<Document<VorlageResource>>('POST', '/api/vorlagen', { data: vorlage })
          .then((response) => {
            expect(response.status).to.eq(201);
            expect(response).to.have.property('body');
          });
      });

    // Vorlage aus Vorgang
    cy.visit('/');
    cy.get('button').contains('Vorgang anlegen').first().click();

    cy.selectFromDsSelect('basis-info__vorgangstyp__select', 0);

    cy.getBySel('step1__vorlagenselect')
      .should('contain', 'Mailvorlage');

    cy.getBySel('basis-info__vorgangstitel')
      .clear()
      .type('When I grow up, I want to be a Vorlage');

    cy.addNewItemToDsMultiselect('step1__empfaenger__form', '<EMAIL>');

    cy.getBySel('vorgang-anlegen__nachricht__betreff')
      .clear()
      .type('Es gibt keine dummen Fragen');

    cy.getBySel('vorgang-anlegen__nachricht__inhalt')
      .clear()
      .type('Heißen Vorlagen Vorlagen, weil Sie vor lagen?');

    cy.getBySel('vorgang-anlegen__save-as-vorlage').first().click();

    cy.getBySel('vorlage-aus-vorgang__vorlagenname')
      .find('input')
      .should('have.value', 'Mailvorlage');

    cy.getBySel('vorlage-aus-vorgang__vorlagenname')
      .clear()
      .type('I am a big Vorlage');

    cy.getBySel('vorlage-aus-vorgang__empfaenger-an').contains('Kunde');
    cy.getBySel('vorlage-aus-vorgang__empfaenger-bcc').contains('Versender');

    cy.getBySel('vorlage-aus-vorgang__submit').first().click();

    cy.get('button').contains('Ja, Vorlage laden').first().click();

    cy.get('button').contains('Abbrechen').first().click();

    cy.visit('/vorlagen');

    // check vorlagennavigation
    cy.getBySel('vorlagenliste')
      .within(() => {
        cy.getBySel('vorlagenliste__item')
          .should('have.length', 5);

        cy.getBySel('vorlagenliste__item')
          .first()
          .within(() => {
            cy.getBySel('vorlagenliste__item__name')
              .should('have.text', 'Briefvorlage');

            cy.getBySel('vorlagenliste__item__origin')
              .should('have.text', 'Eigene');

            cy.getBySel('vorlagenliste__item__titel')
              .should('have.text', 'Allgemeine Anfrage');
          });

        cy.getBySel('vorlagenliste__item')
          .eq(1)
          .within(() => {
            cy.getBySel('vorlagenliste__item__name')
              .should('have.text', 'Briefvorlage für Kampagne');

            cy.getBySel('vorlagenliste__item__origin')
              .should('have.text', 'Eigene');

            cy.getBySel('vorlagenliste__item__titel')
              .should('have.text', 'Kampagnenvorlage');
          });

        cy.getBySel('vorlagenliste__item')
          .eq(2)
          .within(() => {
            cy.getBySel('vorlagenliste__item__name')
              .should('have.text', 'I am a big Vorlage');

            cy.getBySel('vorlagenliste__item__origin')
              .should('have.text', 'Eigene');

            cy.getBySel('vorlagenliste__item__titel')
              .should('have.text', 'Ablauf (M-K)');
          });

        cy.getBySel('vorlagenliste__item')
          .eq(3)
          .within(() => {
            cy.getBySel('vorlagenliste__item__name')
              .should('have.text', 'Mailvorlage');

            cy.getBySel('vorlagenliste__item__origin')
              .should('have.text', 'Eigene');

            cy.getBySel('vorlagenliste__item__titel')
              .should('have.text', 'Ablauf (M-K)');
          });

        cy.getBySel('vorlagenliste__item')
          .last()
          .within(() => {
            cy.getBySel('vorlagenliste__item__name')
              .should('have.text', 'Mailvorlage für Kampagne');

            cy.getBySel('vorlagenliste__item__origin')
              .should('have.text', 'Eigene');

            cy.getBySel('vorlagenliste__item__titel')
              .should('have.text', 'Kampagnenvorlage');
          });
      });

    // check search functionality
    cy.getBySel('vorlagenliste__results')
      .should('contain', '5 Ergebnisse');

    cy.getBySel('vorlagenliste__search-input')
      .type('Ergebnislose Suche')
      .type('{enter}');

    cy.getBySel('vorlagenliste')
      .should('contain', 'Keine Vorlagen für "Ergebnislose Suche" gefunden.');

    cy.getBySel('vorlagenliste__results')
      .should('contain', '0 Ergebnisse');

    cy.getBySel('vorlagenliste__search-input')
      .find('input')
      .clear()
      .type('Brief')
      .type('{enter}');

    cy.getBySel('vorlagenliste__results')
      .should('contain', '2 Ergebnisse');

    cy.getBySel('vorlagenliste')
      .within(() => {
        cy.getBySel('vorlagenliste__item')
          .should('have.length', 2)
          .first()
          .within(() => {
            cy.getBySel('vorlagenliste__item__name')
              .should('have.text', 'Briefvorlage');
          });
      });

    cy.getBySel('vorlagenliste__search-input')
      .find('input')
      .clear()
      .type('{enter}');

    // submit empty
    cy.get('[data-test="vorlage-form__footer__submit"]:first').click();

    // check errors for mandatory fields
    cy.contains('Vorlagenname muss ausgefüllt werden.').should('exist');
    cy.contains('Vorgangstyp muss ausgefüllt werden.').should('exist');
    cy.contains('Empfänger muss ausgefüllt werden.').should('exist');
    cy.contains('Betreff (Siezen) muss ausgefüllt werden.').should('exist');
    cy.contains('Inhalt (Siezen) muss ausgefüllt werden.').should('exist');

    // error toast
    cy.getBySel('toast-list').should('contain.text', 'Vorlage konnte nicht erstellt werden.');

    // switch Versandart to Brief
    cy.getBySel('vorlage__basic-info__versandart__brief').click();

    cy.getBySel('mail-vorlage-form__empfaenger').should('not.exist');
    cy.getBySel('vorlage__duzen-und-siezen__nachricht__formal').within(() => {
      cy.getBySel('vorlage__nachricht__subject').should('not.exist');
    });

    // switch back to Mail
    cy.getBySel('vorlage__basic-info__versandart__mail').click();

    // Vorlage for Firma
    // fill basic info
    cy.getBySel('vorlage__basic-info__name')
      .type('Email Vorlage for editing purposes');
    cy.getBySel('vorlage__basic-info__description')
      .type('Gotta go. London. It’s 7 AM in the Old Empire.');

    // vorgangTyp
    cy.selectFromDsSelect('vorlage__basic-info__vorgangstyp__select', 0);

    // empfaenger
    cy.selectFromDsMultiselectByLabel('mail-vorlage-form__empfaenger', 'Kunde');

    cy.getBySel('vorlage__duzen-und-siezen__nachricht__formal').within(() => {
      cy.getBySel('vorlage__nachricht__subject')
        .type('Dad, why is the American government the best government? #');
    });
    cy.get('[data-tippy-root]')
      .should('have.length', 1)
      .contains('button', 'datum')
      .should('exist')
      .click();

    cy.getBySel('vorlage__duzen-und-siezen__nachricht__formal').within(() => {
      cy.getBySel('vorlage__nachricht__content')
        .type('Because of our endless appeals system. #');
    });
    cy.get('[data-tippy-root]')
      .should('have.length', 1)
      .contains('button', 'datum')
      .should('exist')
      .click();

    // submit
    cy.get('[data-test="vorlage-form__footer__submit"]:first').click();

    // success toast
    cy.getBySel('toast-list').should('contain.text', 'Vorlage erfolgreich erstellt.');

    // Edit vorlage
    // vorgangTyp
    cy.selectFromDsSelect('vorlage__basic-info__vorgangstyp__select', 1);

    const editedName = 'Email Vorlage - edited';
    const editedDescription = 'Description';
    const editedEmpfaenger = 'Gesellschaft';
    const editedSubject = 'Subject';
    const editedContent = 'Content';

    // change name
    cy.getBySel('vorlage__basic-info__name')
      .find('input')
      .clear()
      .type(editedName);

    // change description
    cy.getBySel('vorlage__basic-info__description')
      .find('input')
      .clear()
      .type(editedDescription);

    // change empfaenger
    cy.selectFromDsMultiselectByLabel('mail-vorlage-form__empfaenger', 'Kunde');
    cy.selectFromDsMultiselectByLabel('mail-vorlage-form__empfaenger', editedEmpfaenger);

    // change subject and content
    cy.getBySel('vorlage__duzen-und-siezen__nachricht__formal').within(() => {
      cy.getBySel('vorlage__nachricht__subject')
        .clear();
      cy.getBySel('vorlage__nachricht__subject')
        .type(editedSubject);
      cy.getBySel('vorlage__nachricht__content')
        .clear();
      cy.getBySel('vorlage__nachricht__content')
        .type(editedContent);
    });

    // submit
    cy.get('[data-test="vorlage-form__footer__speichern"]:first').click();

    // success toast
    cy.getBySel('toast-list').should('contain.text', 'Änderungen erfolgreich gespeichert.');

    // reload to fetch changes from Backend
    cy.reload();

    // check if changes have been saved correctly
    // check name
    cy.getBySel('vorlage__basic-info__name')
      .find('input')
      .should('have.value', editedName);

    // check description
    cy.getBySel('vorlage__basic-info__description')
      .find('input')
      .should('have.value', editedDescription);

    // check empfaenger
    cy.getBySel('mail-vorlage-form__empfaenger')
      .should('not.have.text', 'Kunde');
    cy.getBySel('mail-vorlage-form__empfaenger')
      .should('have.text', editedEmpfaenger);

    //check subject and content
    cy.getBySel('vorlage__duzen-und-siezen__nachricht__formal').within(() => {
      cy.getBySel('vorlage__nachricht__subject')
        .should('contain.text', editedSubject);
      cy.getBySel('vorlage__nachricht__content')
        .should('contain.text', editedContent);
    });

    // check if changes are visibile in vorgangsnavigation
    cy.getBySel('vorlagenliste')
      .within(() => {
        cy.getBySel('vorlagenliste__item')
          .should('have.length', 6);

        cy.getBySel('vorlagenliste__item')
          .eq(2)
          .within(() => {
            cy.getBySel('vorlagenliste__item__name')
              .should('have.text', editedName);

            cy.getBySel('vorlagenliste__item__titel')
              .should('have.text', 'Ablehnung Kündigung durch VU (M-K)');
          });
      });

    // change name
    cy.getBySel('vorlage__basic-info__name')
      .find('input')
      .clear();
    cy.getBySel('vorlage__basic-info__name')
      .type('Email Vorlage for Firma');

    // change vorgangTyp
    cy.selectFromDsSelect('vorlage__basic-info__vorgangstyp__select', 0);

    // Sichtbarkeit
    cy.getBySel('vorlage__basic-info__sichtbarkeit__shared').click();

    // submit as new
    cy.get('[data-test="vorlage-form__footer__as-new"]:first').click();

    // success toast
    cy.getBySel('toast-list').should('contain.text', 'Vorlage erfolgreich erstellt.');

    cy.visit('/vorlagen');

    // Vorlage for me
    // fill basic info
    cy.getBySel('vorlage__basic-info__name')
      .type('Email Vorlage for me');

    cy.getBySel('vorlage__basic-info__description')
      .type('It’s just the greatest thing that humans ever made…it’s the Vespa!');

    //vorgangTyp
    cy.selectFromDsSelect('vorlage__basic-info__vorgangstyp__select', 0);

    cy.get('[data-test="empfaenger__cc-bcc__switch"]').click();

    // test all 4 empfaenger are selectable
    const empfaengers = ['Kunde', 'Gesellschaft', 'DEMV', 'Versender', 'Zuständiger Vermittler'];

    cy.selectFromDsMultiselectByLabel('mail-vorlage-form__empfaenger', ...empfaengers);
    cy.selectFromDsMultiselectByLabel('mail-vorlage-form__cc', ...empfaengers);
    cy.selectFromDsMultiselectByLabel('mail-vorlage-form__bcc', ...empfaengers);

    // nachricht siezen
    cy.getBySel('vorlage__duzen-und-siezen__nachricht__formal').within(() => {
      cy.getBySel('vorlage__nachricht__subject')
        .type('Your rein of terror is coming to an end!!');
      cy.getBySel('vorlage__nachricht__content')
        .type('I’m not the kind of guy you want for this. I’m more of an idea man. #');
    });
    cy.get('[data-tippy-root]')
      .should('have.length', 1)
      .contains('button', 'datum')
      .should('exist')
      .click();
    cy.get('[data-test="vorlage__duzen-und-siezen__nachricht__formal"] .ProseMirror')
      .contains('[data-name=datum]', 'datum')
      .should('exist'); // date tag
    cy.getBySel('vorlage__duzen-und-siezen__informal-switch').click();

    // nachricht duzen
    cy.getBySel('vorlage__duzen-und-siezen__nachricht__informal').within(() => {
      cy.getBySel('vorlage__nachricht__subject')
        .type('Let me tell you they did not have motors, just a sweaty land monster with a paddle');
      cy.getBySel('vorlage__nachricht__content')
        .type('Thank you Giulia for showing us the boring thing that takes us to the terrible place. #');
    });

    cy.get('[data-tippy-root]')
      .should('have.length', 1)
      .contains('button', 'datum')
      .should('exist')
      .click();
    cy.get('[data-test="vorlage__duzen-und-siezen__nachricht__informal"] .ProseMirror')
      .contains('[data-name=datum]', 'datum')
      .should('exist'); // date tag

    // Vorschau E-Mail Siezen
    cy.get('[data-test="vorlage-form__footer__preview"]:first').click();

    cy.getBySel('mail__details-preview__absender').contains('Versender').should('exist');

    cy.getBySel('mail__details-preview__empfaenger').contains('Kunde').should('exist');
    cy.getBySel('mail__details-preview__empfaenger').contains('Gesellschaft').should('exist');
    cy.getBySel('mail__details-preview__empfaenger').contains('DEMV').should('exist');
    cy.getBySel('mail__details-preview__empfaenger').contains('Versender').should('exist');

    cy.getBySel('mail__details-preview__cc').contains('Kunde').should('exist');
    cy.getBySel('mail__details-preview__cc').contains('Gesellschaft').should('exist');
    cy.getBySel('mail__details-preview__cc').contains('DEMV').should('exist');
    cy.getBySel('mail__details-preview__cc').contains('Versender').should('exist');

    cy.getBySel('mail__details-preview__bcc').contains('Kunde').should('exist');
    cy.getBySel('mail__details-preview__bcc').contains('Gesellschaft').should('exist');
    cy.getBySel('mail__details-preview__bcc').contains('DEMV').should('exist');
    cy.getBySel('mail__details-preview__bcc').contains('Versender').should('exist');

    cy.getBySel('mail__details-preview__subject')
      .should('contain.text', 'Your rein of terror is coming to an end!!');

    cy.getBySel('mail__details-preview__content')
      .should('contain.text', 'I’m not the kind of guy you want for this. I’m more of an idea man.');

    cy.get('[data-test="vorlage-vorschau-modal__close"]:first').click();

    cy.getBySel('vorlage__basic-info__versandart__brief').click();

    cy.get('[data-test="vorlage-form__footer__preview"]:first').click();

    cy.getBySel('details-view__pdf-reader').should('exist');

    cy.get('[data-test="vorlage-vorschau-modal__close"]:first').click();

    cy.getBySel('vorlage__basic-info__versandart__mail').click();

    // Vorschau E-Mail Duzen
    cy.get('[data-test="vorlage-form__footer__preview"]:last').click();

    cy.getBySel('mail__details-preview__absender').contains('Versender').should('exist');

    cy.getBySel('mail__details-preview__empfaenger').contains('Kunde').should('exist');
    cy.getBySel('mail__details-preview__empfaenger').contains('Gesellschaft').should('exist');
    cy.getBySel('mail__details-preview__empfaenger').contains('DEMV').should('exist');
    cy.getBySel('mail__details-preview__empfaenger').contains('Versender').should('exist');

    cy.getBySel('mail__details-preview__cc').contains('Kunde').should('exist');
    cy.getBySel('mail__details-preview__cc').contains('Gesellschaft').should('exist');
    cy.getBySel('mail__details-preview__cc').contains('DEMV').should('exist');
    cy.getBySel('mail__details-preview__cc').contains('Versender').should('exist');

    cy.getBySel('mail__details-preview__bcc').contains('Kunde').should('exist');
    cy.getBySel('mail__details-preview__bcc').contains('Gesellschaft').should('exist');
    cy.getBySel('mail__details-preview__bcc').contains('DEMV').should('exist');
    cy.getBySel('mail__details-preview__bcc').contains('Versender').should('exist');

    cy.getBySel('mail__details-preview__subject')
      .should('contain.text', 'Let me tell you they did not have motors, just a sweaty land monster with a paddle');

    cy.getBySel('mail__details-preview__content')
      .should('contain.text', 'Thank you Giulia for showing us the boring thing that takes us to the terrible place.');

    cy.get('[data-test="vorlage-vorschau-modal__close"]:first').click();

    cy.getBySel('vorlage__basic-info__versandart__brief').click();

    cy.get('[data-test="vorlage-form__footer__preview"]:last').click();

    cy.getBySel('details-view__pdf-reader').should('exist');

    cy.get('[data-test="vorlage-vorschau-modal__close"]:last').click();

    cy.getBySel('vorlage__basic-info__versandart__mail').click();

    //automatischer dateianhang
    cy.selectFromDsMultiselectByLabel(
      'mail-vorlage-form__attachments',
      'Maklervollmacht - Maklervertrag',
      'Maklervollmacht',
      'Personalisierter Maklerauftrag (Blanko)',
      'Reisepass / Personalausweis',
    );

    // submit
    cy.get('[data-test="vorlage-form__footer__submit"]:first').click();

    cy.visit('/');

    cy.get('button').contains('Vorgang anlegen').first().click();

    // vorgangstyp
    cy.selectFromDsSelect('basis-info__vorgangstyp__select', 0);

    // gesellschaft
    cy.selectFromDsSelect('basis-info__gesellschaft__select', 0);

    cy.request({
      url: 'api/kunden/4/documents?dokumentTyp=Reisepass',
      failOnStatusCode: false,
    }).its('status').should('eq', 404);

    cy.request({
      url: 'api/kunden/4/documents?dokumentTyp=Personalausweis',
      failOnStatusCode: false,
    }).its('status').should('eq', 404);

    cy.contains('Abbrechen').should('exist').click();

    cy.visit('/vorlagen');

    cy.contains('Email Vorlage for me').should('exist').click();

    cy.selectFromDsMultiselectByLabel(
      'mail-vorlage-form__attachments',
      'Reisepass / Personalausweis',
    );

    cy.contains('Vorlage speichern').should('exist').click();

    // test footer
    cy.contains('Vorlage löschen').should('exist');
    cy.getBySel('vorlage-form__footer__delete').should('be.visible');
    cy.contains('Als neue Vorlage').should('exist');
    cy.getBySel('vorlage-form__footer__as-new').should('be.visible');
    cy.contains('Vorlage speichern').should('exist');
    cy.getBySel('vorlage-form__footer__speichern').should('be.visible');

    // check for correct amount of Vorlagen in Vorlagenavigation
    cy.getBySel('vorlagenliste__results')
      .should('contain', '8 Ergebnisse');

    cy.getBySel('vorlagenliste__item')
      .should('have.length', 8);

    cy.visit('/vorlagen');

    // Vorlage for me
    // fill basic info
    cy.getBySel('vorlage__basic-info__name')
      .type('Mailvorlage Kampagne for me');

    cy.getBySel('vorlage__basic-info__description')
      .type('Mankind -- that word should have new meaning for all of us today.');

    // versandart kampagne
    cy.getBySel('vorlage__basic-info__vorlagenart__kampagne').click();

    // nachricht siezen
    cy.getBySel('vorlage__duzen-und-siezen__nachricht__formal').within(() => {
      cy.getBySel('vorlage__nachricht__subject')
        .type('We can’t be consumed by our petty differences anymore.');
      cy.getBySel('vorlage__nachricht__content')
        .type('We will be united in our common interests. #');
    });
    cy.get('[data-tippy-root]')
      .should('have.length', 1)
      .contains('button', 'datum')
      .should('exist')
      .click();
    cy.get('[data-test="vorlage__duzen-und-siezen__nachricht__formal"] .ProseMirror')
      .contains('[data-name=datum]', 'datum')
      .should('exist'); // date tag
    cy.getBySel('vorlage__duzen-und-siezen__informal-switch').click();

    // nachricht duzen
    cy.getBySel('vorlage__duzen-und-siezen__nachricht__informal').within(() => {
      cy.getBySel('vorlage__nachricht__subject')
        .type('We’re fighting for our right to live, to exist.');
      cy.getBySel('vorlage__nachricht__content')
        .type('And should we win the day, the 4th of July will no longer be known as an American holiday, but as the day when the world declared in one voice: #');
    });

    cy.get('[data-tippy-root]')
      .should('have.length', 1)
      .contains('button', 'datum')
      .should('exist')
      .click();
    cy.get('[data-test="vorlage__duzen-und-siezen__nachricht__informal"] .ProseMirror')
      .contains('[data-name=datum]', 'datum')
      .should('exist'); // date tag

    // Vorschau Kampagne Siezen
    cy.get('[data-test="vorlage-form__footer__preview"]:first').click();

    cy.getBySel('mail__details-preview__absender').contains('Versender').should('exist');

    cy.getBySel('mail__details-preview__subject')
      .should('contain.text', 'We can’t be consumed by our petty differences anymore.');

    cy.getBySel('mail__details-preview__content')
      .should('contain.text', 'We will be united in our common interests.');

    cy.get('[data-test="vorlage-vorschau-modal__close"]:first').click();

    // Vorschau Kampagne Duzen
    cy.get('[data-test="vorlage-form__footer__preview"]:last').click();

    cy.getBySel('mail__details-preview__absender').contains('Versender').should('exist');

    cy.getBySel('mail__details-preview__subject')
      .should('contain.text', 'We’re fighting for our right to live, to exist.');

    cy.getBySel('mail__details-preview__content')
      .should('contain.text', 'And should we win the day, the 4th of July will no longer be known as an American holiday, but as the day when the world declared in one voice:');

    cy.get('[data-test="vorlage-vorschau-modal__close"]:first').click();

    //automatischer dateianhang
    cy.selectFromDsMultiselectByLabel(
      'kampagne-mail-vorlage-form__attachments',
      'Maklervollmacht - Maklervertrag',
      'Maklervollmacht',
      'Personalisierter Maklerauftrag (Blanko)',
    );

    // submit
    cy.get('[data-test="vorlage-form__footer__submit"]:first').click();

    // test footer
    cy.contains('Vorlage löschen').should('exist');
    cy.getBySel('vorlage-form__footer__delete').should('be.visible');
    cy.contains('Als neue Vorlage').should('exist');
    cy.getBySel('vorlage-form__footer__as-new').should('be.visible');
    cy.contains('Vorlage speichern').should('exist');
    cy.getBySel('vorlage-form__footer__speichern').should('be.visible');

    // check for correct amount of Vorlagen in Vorlagenavigation
    cy.getBySel('vorlagenliste__results')
      .should('contain', '9 Ergebnisse');

    cy.getBySel('vorlagenliste__item')
      .should('have.length', 9);

    cy.visit('/vorlagen');

    // Vorlage for me
    // fill basic info
    cy.getBySel('vorlage__basic-info__name')
      .type('Briefvorlage Kampagne for me');

    cy.getBySel('vorlage__basic-info__description')
      .type('I know. It’s all wrong. By rights we shouldn’t even be here.');

    // vorlagenart kampagne
    cy.getBySel('vorlage__basic-info__vorlagenart__kampagne').click();

    cy.getBySel('vorlage__basic-info__versandart__brief').click();

    // unwanted fields
    cy.getBySel('vorlage__basic-info__vorgangstyp__select').should('not.exist');
    cy.getBySel('mail-vorlage-form__empfaenger').should('not.exist');
    cy.getBySel('vorlage__duzen-und-siezen__nachricht__formal').within(() => {
      cy.getBySel('vorlage__nachricht__subject').should('not.exist');
    });

    // nachricht siezen
    cy.getBySel('vorlage__duzen-und-siezen__nachricht__formal').within(() => {
      cy.getBySel('vorlage__nachricht__content')
        .type('But we are. It’s like in the great stories, Mr. Frodo. #');
    });
    cy.get('[data-tippy-root]')
      .should('have.length', 1)
      .contains('button', 'datum')
      .should('exist')
      .click();
    cy.get('[data-test="vorlage__duzen-und-siezen__nachricht__formal"] .ProseMirror')
      .contains('[data-name=datum]', 'datum')
      .should('exist'); // date tag
    cy.getBySel('vorlage__duzen-und-siezen__informal-switch').click();

    // nachricht duzen
    cy.getBySel('vorlage__duzen-und-siezen__nachricht__informal').within(() => {
      cy.getBySel('vorlage__nachricht__content')
        .type('Because how could the end be happy? How could the world go back to the way it was when so much bad had happened? #');
    });

    cy.get('[data-tippy-root]')
      .should('have.length', 1)
      .contains('button', 'datum')
      .should('exist')
      .click();
    cy.get('[data-test="vorlage__duzen-und-siezen__nachricht__informal"] .ProseMirror')
      .contains('[data-name=datum]', 'datum')
      .should('exist'); // date tag

    // vorschau
    cy.get('[data-test="vorlage-form__footer__preview"]:last').click();
    cy.getBySel('details-view__pdf-reader').should('exist');
    cy.get('[data-test="vorlage-vorschau-modal__close"]:last').click();

    // submit
    cy.get('[data-test="vorlage-form__footer__submit"]:first').click();
    // test footer
    cy.contains('Vorlage löschen').should('exist');
    cy.getBySel('vorlage-form__footer__delete').should('be.visible');
    cy.contains('Als neue Vorlage').should('exist');
    cy.getBySel('vorlage-form__footer__as-new').should('be.visible');
    cy.contains('Vorlage speichern').should('exist');
    cy.getBySel('vorlage-form__footer__speichern').should('be.visible');

    // check for correct amount of Vorlagen in Vorlagenavigation
    cy.getBySel('vorlagenliste__results')
      .should('contain', '10 Ergebnisse');

    cy.getBySel('vorlagenliste__item')
      .should('have.length', 10);

    // fills vorgang anlegen.
    // go to vorlage view
    cy.visit('/');

    // opens modal
    cy.get('button').contains('Vorgang anlegen').first().click();

    // vorgangstyp
    cy.selectFromDsSelect('basis-info__vorgangstyp__select', 0);

    // gesellschaft
    cy.selectFromDsSelect('basis-info__gesellschaft__select', 0);

    // formal kunde
    cy.selectFromDsSelectBySearch('basis-info__kunde__select', 'Mustafa Hinrichs');

    // formal
    cy.getBySel('step1__empfaenger__form')
      .should('contain', '<EMAIL>') // kunde
      .should('contain', '<EMAIL>') // versender / zustaendiger vermittler
      .should('contain', '<EMAIL>') // ansprechpartner
      .should('contain', '<EMAIL>'); // demv
    cy.getBySel('step1__cc__form')
      .should('contain', '<EMAIL>')
      .should('contain', '<EMAIL>')
      .should('contain', '<EMAIL>')
      .should('contain', '<EMAIL>');
    cy.getBySel('step1__bcc__form')
      .should('contain', '<EMAIL>')
      .should('contain', '<EMAIL>')
      .should('contain', '<EMAIL>')
      .should('contain', '<EMAIL>');

    cy.get('[data-test="vorgang-anlegen__nachricht__betreff"] .ProseMirror').should('contain', 'Your rein of terror is coming to an end!!');
    cy.get('[data-test="vorgang-anlegen__nachricht__inhalt"] .ProseMirror').should('contain', 'I’m not the kind of guy you want for this. I’m more of an idea man. #datum');

    // informal kunde
    cy.selectFromDsSelectBySearch('basis-info__kunde__select', 'Maria Musterfrau');

    cy.getBySel('step1__empfaenger__form')
      .should('contain', '<EMAIL>')
      .should('contain', '<EMAIL>')
      .should('contain', '<EMAIL>')
      .should('contain', '<EMAIL>');
    cy.getBySel('step1__cc__form')
      .should('contain', '<EMAIL>')
      .should('contain', '<EMAIL>')
      .should('contain', '<EMAIL>')
      .should('contain', '<EMAIL>');
    cy.getBySel('step1__bcc__form')
      .should('contain', '<EMAIL>')
      .should('contain', '<EMAIL>')
      .should('contain', '<EMAIL>')
      .should('contain', '<EMAIL>');

    cy.get('[data-test="vorgang-anlegen__nachricht__betreff"] .ProseMirror').should('contain', 'Let me tell you they did not have motors, just a sweaty land monster with a paddle');
    cy.get('[data-test="vorgang-anlegen__nachricht__inhalt"] .ProseMirror').should('contain', 'Thank you Giulia for showing us the boring thing that takes us to the terrible place. #datum');
  });
});
