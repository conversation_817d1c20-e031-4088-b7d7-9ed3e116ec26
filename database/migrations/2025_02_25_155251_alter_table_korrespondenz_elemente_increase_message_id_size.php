<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        // see https://datatracker.ietf.org/doc/html/rfc2822#section-2.1.1
        // 998 is too long to be indexable by innodb so we limit it to 768
        Schema::table('korrespondenz_elemente', function (Blueprint $table) {
            $table->string('message_id', 768)->nullable()->change();
        });
    }

    public function down(): void
    {
        Schema::table('korrespondenz_elemente', function (Blueprint $table) {
            $table->string('message_id')->nullable()->change();
        });
    }
};
