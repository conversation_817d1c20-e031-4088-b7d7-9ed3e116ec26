<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    private const TABLE_VORGAENGE = 'vorgaenge';

    public function up(): void
    {
        Schema::table(self::TABLE_VORGAENGE, function (Blueprint $table) {
            $table->unsignedBigInteger('gesellschaft_external_id')->nullable()->after('gesellschaft_id');
            $table->unsignedBigInteger('vertriebsweg_external_id')->nullable()->after('vertriebsweg_id');
        });
    }

    public function down(): void
    {
        Schema::table(self::TABLE_VORGAENGE, function (Blueprint $table) {
            $table->dropColumn(['gesellschaft_external_id', 'vertriebsweg_external_id']);
        });
    }
};
