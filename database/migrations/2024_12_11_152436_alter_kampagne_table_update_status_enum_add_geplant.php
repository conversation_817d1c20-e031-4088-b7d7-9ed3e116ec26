<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    private const TABLE = 'kampagnen';
    private const COLUMN = 'status';

    public function up(): void
    {
        DB::statement(sprintf(
            "ALTER TABLE %s
                    MODIFY COLUMN %s
                    ENUM('entwurf', 'in_bearbeitung', 'abgeschlossen', 'geplant') Default 'entwurf'",
            self::TABLE,
            self::COLUMN,
        ));
    }

    public function down(): void
    {
        DB::statement("
            UPDATE kampagnen
            SET status = 'entwurf', geplant_at = null
            WHERE status = 'geplant'
            AND geplant_at IS NOT NULL
        ");

        DB::statement(sprintf(
            "ALTER TABLE %s
                    MODIFY COLUMN %s
                    ENUM('entwurf', 'in_bearbeitung', 'abgeschlossen') Default 'entwurf'",
            self::TABLE,
            self::COLUMN,
        ));
    }
};
