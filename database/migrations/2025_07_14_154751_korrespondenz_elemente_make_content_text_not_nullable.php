<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    const TABLE_NAMES = [
        'korrespondenz_elemente',
        'erinnerung_elemente',
        'mahnung_elemente',
    ];

    const COLUMN_NAME = 'content_text';

    public function up(): void
    {
        foreach (self::TABLE_NAMES as $tableName) {
            DB::table($tableName)
                ->whereNull(self::COLUMN_NAME)
                ->update([self::COLUMN_NAME => '']);

            Schema::table($tableName, function (Blueprint $table) {
                $table->text(self::COLUMN_NAME)->nullable(false)->change();
            });
        }
    }

    public function down(): void
    {
        foreach (self::TABLE_NAMES as $tableName) {
            Schema::table($tableName, function (Blueprint $table) {
                $table->text(self::COLUMN_NAME)->nullable()->change();
            });
        }
    }
};
