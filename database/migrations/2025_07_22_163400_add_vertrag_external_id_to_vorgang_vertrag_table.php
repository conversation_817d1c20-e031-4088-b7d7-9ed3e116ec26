<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    private const TABLE_VORGANG_VERTRAG = 'vorgang_vertrag';

    public function up(): void
    {
        Schema::table(self::TABLE_VORGANG_VERTRAG, function (Blueprint $table) {
            $table->unsignedBigInteger('vertrag_external_id')->nullable()->after('vertrag_id');
        });
    }

    public function down(): void
    {
        Schema::table(self::TABLE_VORGANG_VERTRAG, function (Blueprint $table) {
            $table->dropColumn('vertrag_external_id');
        });
    }
};
