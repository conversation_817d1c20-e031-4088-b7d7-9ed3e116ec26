<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    private const VORGANGSTYP_TITEL_OLD = 'Risikodatenerfassung';
    private const VORGANGSTYP_TITEL_NEW = 'Versicherungsangaben';

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::table('vorgang_typen')
            ->where('titel', self::VORGANGSTYP_TITEL_OLD)
            ->update(['titel' => self::VORGANGSTYP_TITEL_NEW]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::table('vorgang_typen')
            ->where('titel', self::VORGANGSTYP_TITEL_NEW)
            ->update(['titel' => self::VORGANGSTYP_TITEL_OLD]);
    }
};
