{"name": "demvsystems/vorgaenge", "description": "DEMV Vorgaenge Project", "license": "proprietary", "type": "project", "require": {"php": "^8.2", "ext-bcmath": "*", "ext-dom": "*", "ext-fileinfo": "*", "ext-redis": "*", "ext-zip": "*", "convertapi/convertapi-php": "^3.0", "demv/professionalworks-sdk": "^5.7.0", "doctrine/dbal": "^3.5", "fakerphp/faker": "^1.23", "guzzlehttp/guzzle": "^7.8", "imangazaliev/didom": "^2.0", "lab404/laravel-impersonate": "^1.7.4", "laravel-lang/lang": "^12.18", "laravel/framework": "^11.37", "laravel/telescope": "^5.0", "laravel/tinker": "^2.9", "laravel/vapor-core": "^2.36", "league/flysystem-aws-s3-v3": "^3.8.0", "mxl/laravel-job": "^1.6", "sentry/sentry-laravel": "^4.3", "spatie/laravel-data": "^4.17", "spatie/laravel-permission": "^6.2", "staudenmeir/belongs-to-through": "^2.16", "staudenmeir/laravel-cte": "^1.11", "zbateson/mail-mime-parser": "^2.2", "zircote/swagger-php": "^5.1"}, "require-dev": {"dg/bypass-finals": "*", "ergebnis/composer-normalize": "^2.6", "infection/infection": "^0.29.5", "kitloong/laravel-migrations-generator": "^7.0", "laravel/vapor-cli": "^1.63", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.0", "nunomaduro/larastan": "^2.8", "pheromone/phpcs-security-audit": "^2.0", "php-parallel-lint/php-parallel-lint": "^1.3", "phpstan/phpstan": "^1.10", "phpstan/phpstan-deprecation-rules": "^1.1.3", "phpstan/phpstan-mockery": "^1.1.1", "phpstan/phpstan-phpunit": "^1.3.11", "phpstan/phpstan-strict-rules": "^1.5.1", "phpunit/phpunit": "^11.0.1", "qossmic/deptrac-shim": "^1.0.2", "roave/security-advisories": "dev-master", "slevomat/coding-standard": "^8.13", "spaceemotion/php-coding-standard": "dev-wip/v1", "spatie/laravel-db-snapshots": "^2.6", "spatie/laravel-ray": "^1.36", "squizlabs/php_codesniffer": "^3.7.2", "symplify/easy-coding-standard": "^12.5"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-update-cmd": ["@php artisan telescope:publish --ansi"], "post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": ["laravel/telescope"]}}, "repositories": [{"type": "git", "url": "**************:demvsystems/professionalworks-sdk.git", "no-api": true}, {"type": "git", "url": "**************:demvsystems/sdk-framework.git", "no-api": true}], "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"ergebnis/composer-normalize": true, "dealerdirect/phpcodesniffer-composer-installer": true, "composer/package-versions-deprecated": true, "infection/extension-installer": true, "php-http/discovery": true}}, "minimum-stability": "dev", "prefer-stable": true}