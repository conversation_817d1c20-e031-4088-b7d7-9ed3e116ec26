# Install dependencies
[group('Yarn')]
yarn-install:
    @just exec {{node_service}} yarn install

# Run ESLint
[group('Linting')]
yarn-lint:
    @just exec {{node_service}} yarn run lint

# Run ESLint with auto-fix
[group('Linting')]
yarn-lint-fix:
    @just exec {{node_service}} yarn run lint:fix

# Run Vitest tests
[group('Testing')]
yarn-test *ARGS:
    @just exec {{node_service}} yarn run test {{ARGS}}

# Run TypeScript type checking
[group('Yarn')]
yarn-tsc:
    @just exec {{node_service}} yarn run tsc

# Build for production
[group('Yarn')]
yarn-production:
    @just exec {{node_service}} yarn run production

# Watch node container logs
[group('Yarn')]
yarn-watch:
    @just compose logs -f --tail=5 {{node_service}}

# Run any yarn command
[group('Yarn')]
yarn *ARGS:
    @just exec {{node_service}} yarn {{ARGS}}
