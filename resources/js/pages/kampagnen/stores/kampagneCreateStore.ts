import { watchDebounced } from '@vueuse/core';
import { defineStore } from 'pinia';
import { computed, ref, shallowRef, triggerRef, watch } from 'vue';

import { FIFTEEN_MB_IN_B, useFiles } from '@/components/form/useFiles';
import { useLoadKampagneCreate } from '@/pages/kampagnen/composables/kampagneCreate/useLoadKampagneCreate';
import { useSaveKampagne } from '@/pages/kampagnen/composables/kampagneCreate/useSaveKampagne';
import { useKundenTable } from '@/pages/kampagnen/composables/kunden/useKundenTable';
import { ImNamenVon, KampagneStatus, Step, Versandart } from '@/pages/kampagnen/types';
import { Attachment, KampagneIdeeResource, KundeResource } from '@/store/resources/types';

const FOUR_MB_IN_B = 4_194_304;

export const useKampagneCreateStore = defineStore('kampagneCreate', () => {
  const { submit, sendKampagne, isSubmitting } = useSaveKampagne();
  const { load, loadEmpfaengers, isLoading, isLoadingEmpfaengers } = useLoadKampagneCreate();
  const { loadMissingAddressKundenIds } = useKundenTable();
  const {
    totalFileSize,
    uploadedFiles,
    saveFiles,
    setInitialUploadedFiles,
  } = useFiles({ preview: true });

  const isInitialized = ref(false);
  const errors = ref<Record<string, string[]>>({});

  const id = ref<string>();

  const versandart = ref<Versandart>(Versandart.Mail);
  const titel = ref<string | null>(null);
  const vorgaengeAnlegen = ref<boolean>(true);
  const imNamenVon = ref<ImNamenVon>(ImNamenVon.Mir);
  const senderExternalId = ref<string | null>(null);

  const senderLabel = ref<string | null>(null);

  const empfaengers = shallowRef(new Map<string, KundeResource>());
  const isIteratingEmpfaengers = ref(false);

  const formalBetreff = ref<string | null>(null);
  const formalContent = ref<string | null>(null);
  const informalBetreff = ref<string | null>(null);
  const informalContent = ref<string | null>(null);
  const attachments = ref<Attachment[]>([]);
  const status = ref<KampagneStatus | null>();
  const geplantAt = ref<null | Date>(null);

  const kampagneIdee = ref<KampagneIdeeResource | null>(null);
  const kampagneIdeeId = computed(() => kampagneIdee.value?.id);

  const isContentTooLarge = computed(() => {
    return (formalContent.value?.length ?? 0) + (informalContent.value?.length ?? 0) > FOUR_MB_IN_B;
  });

  const hasImagesAndIsBrief = computed(() => {
    const content = (formalContent.value ?? '') + (informalContent.value ?? '');

    return versandart.value === Versandart.Brief && content.includes('<img');
  });

  function isNotSubmittable(step?: Step): boolean {
    return !isInitialized.value
      || isLoadingEmpfaengers.value
      || isIteratingEmpfaengers.value
      || (isSubmitting.value && id.value === undefined)
      || (isContentTooLarge.value && step === 3)
      || (totalFileSize.value > FIFTEEN_MB_IN_B && step === 3)
      || (hasImagesAndIsBrief.value && step === 3);
  }

  const unwatches = ref<(() => void)[]>([]);

  function reset(): void {
    isInitialized.value = false;
    unwatches.value.forEach((unwatch) => {
      unwatch();
    });
    unwatches.value = [];

    id.value = undefined;
    versandart.value = Versandart.Mail;
    titel.value = null;
    vorgaengeAnlegen.value = false;
    imNamenVon.value = ImNamenVon.Mir;
    senderExternalId.value = null;
    senderLabel.value = null;
    empfaengers.value.clear();
    geplantAt.value = null;
    resetInhaltForm();
  }

  function resetInhaltForm() {
    formalBetreff.value = null;
    formalContent.value = null;
    informalBetreff.value = null;
    informalContent.value = null;
    attachments.value = [];
  }

  async function addKundenToEmpfaengers(kunden: KundeResource[]) {
    isIteratingEmpfaengers.value = true;
    kunden.forEach((kunde) => empfaengers.value.set(kunde.id, kunde));
    triggerRef(empfaengers);
    isIteratingEmpfaengers.value = false;

    void loadMissingAddressKundenIds(kunden);

    if (isInitialized.value) {
      await submit();
    }
  }

  async function removeEmpfaengers(empfaengerIds: string[]) {
    isIteratingEmpfaengers.value = true;
    empfaengerIds.forEach((empfaengerId) => empfaengers.value.delete(empfaengerId));
    triggerRef(empfaengers);
    isIteratingEmpfaengers.value = false;

    await submit();
  }

  async function setEmpfaengersInformal(empfaengerIds: string[], informalValue: boolean) {
    empfaengerIds.forEach((empfaengerId) => {
      const empfaenger = empfaengers.value.get(empfaengerId);

      if (empfaenger === undefined) {
        return;
      }

      empfaenger.attributes.informal = informalValue;
    });

    triggerRef(empfaengers);

    await submit();
  }

  function registerAutosaveWatcher() {
    const watchedValues = computed(() => ({
      versandart: versandart.value,
      titel: titel.value,
      vorgaengeAnlegen: vorgaengeAnlegen.value,
      imNamenVon: imNamenVon.value,
      senderExternalId: senderExternalId.value,
      informalBetreff: informalBetreff.value,
      informalContent: informalContent.value,
      formalBetreff: formalBetreff.value,
      formalContent: formalContent.value,
      attachments: attachments.value,
    }));

    unwatches.value.push(watchDebounced(watchedValues, async (newVal, oldVal) => {
      if (isNotSubmittable()) {
        return;
      }

      if (!await submit()) {
        return;
      }

      if (empfaengers.value.size === 0 || id.value === undefined) {
        return;
      }

      const hasSenderChanged = newVal.imNamenVon !== oldVal.imNamenVon
        || newVal.senderExternalId !== oldVal.senderExternalId;

      if (hasSenderChanged) {
        await loadEmpfaengers(id.value);
      }
    }, {
      debounce: 500,
    }));

    unwatches.value.push(watchDebounced(uploadedFiles, async (newFiles, oldFiles) => {
      if (newFiles.length === oldFiles.length) {
        return;
      }

      if (id.value === undefined) {
        throw Error('Kampagne ID is missing');
      }

      await saveFiles(id.value, 'kampagnen');
    }, {
      debounce: 500,
    }));
  }

  watch(imNamenVon, (newImNamenVon) => {
    if ([ImNamenVon.Mir, ImNamenVon.ZustaendigerVermittler].includes(newImNamenVon)) {
      senderExternalId.value = null;
    }
  });

  watch(() => isInitialized.value, () => {
    if (isInitialized.value) {
      registerAutosaveWatcher();
    }
  }, { immediate: true });

  return {
    errors,
    id,

    versandart,
    titel,
    vorgaengeAnlegen,
    imNamenVon,
    senderExternalId,
    senderLabel,
    empfaengers,
    formalBetreff,
    formalContent,
    informalBetreff,
    informalContent,
    uploadedFiles,
    attachments,
    status,
    geplantAt,
    kampagneIdee,
    kampagneIdeeId,

    totalFileSize,
    setInitialUploadedFiles,
    reset,
    resetInhaltForm,
    sendKampagne,
    submit,
    load,
    isLoading,
    isNotSubmittable,
    isInitialized,
    addKundenToEmpfaengers,
    removeEmpfaengers,
    setEmpfaengersInformal,
    isContentTooLarge,
    hasImagesAndIsBrief,
  };
});
