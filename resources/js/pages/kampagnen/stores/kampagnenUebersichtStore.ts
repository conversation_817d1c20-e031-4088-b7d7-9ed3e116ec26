import { defineStore } from 'pinia';
import { ref } from 'vue';

import { useLoadKampagnenList } from '@/pages/kampagnen/composables/useLoadKampagnenList';
import { KampagneResource } from '@/store/resources/types';

export const useKampagnenStore = defineStore('kampagnenList', () => {
  const {
    loadKampagnen,
    isLoading,
  } = useLoadKampagnenList();

  const kampagnenList = ref<KampagneResource[]>([]);

  return {
    kampagnenList,

    loadKampagnen,
    isLoading,
  };
});
