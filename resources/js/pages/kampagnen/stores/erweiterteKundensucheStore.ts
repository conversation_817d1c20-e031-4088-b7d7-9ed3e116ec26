import { defineStore } from 'pinia';
import { computed, reactive, ref, shallowRef, watch } from 'vue';

import {
  useLoadErweiterteKundensucheForm,
} from '@/pages/kampagnen/composables/kunden/useLoadErweiterteKundensucheForm';
import {
  useSubmitErweiterteKundensuche,
} from '@/pages/kampagnen/composables/kunden/useSubmitErweiterteKundensuche';
import { useKampagneCreateStore } from '@/pages/kampagnen/stores/kampagneCreateStore';
import {
  Berufsstatus,
  ErweiterteKundensuchePayload,
  Kundenstatus,
  RaucherRadioOptions,
  Versandart,
  Vertragsstatus,
  Vertragstyp,
  VonBis,
  Zielgruppe,
} from '@/pages/kampagnen/types';
import { DokumenttypRessource, KundeResource } from '@/store/resources/types';

// initial values that are not null|undefined|false|[]
const INITIAL_VALUES = {
  kundenstatus: [
    Kundenstatus.KundeStorniert,
    Kundenstatus.KundeVerstorben,
  ],
  smoker: RaucherRadioOptions.NichtBeachten,
  statusAusschliessen: true,
};

const BRIEF_EMPFAENGER_LIMIT = 500;
const EMAIL_EMPFAENGER_LIMIT = 10000;

export const useErweiterteKundensucheStore = defineStore('erweiterteKundensuche', () => {
  const {
    initDokumenttypen,
  } = useLoadErweiterteKundensucheForm();

  const kampagneCreateStore = useKampagneCreateStore();

  const {
    submit,
    isLoading,
  } = useSubmitErweiterteKundensuche();

  const errors = ref<Record<string, string[]>>({});

  const foundKunden = shallowRef<KundeResource[]>([]);
  const empfaengerLimit = computed(() =>
    kampagneCreateStore.versandart === Versandart.Mail
      ? EMAIL_EMPFAENGER_LIMIT
      : BRIEF_EMPFAENGER_LIMIT,
  );
  const isEmpfaengerLimitReached = computed(
    () => foundKunden.value.length > empfaengerLimit.value,
  );

  const initialValues = computed(() => ({
    ...INITIAL_VALUES,
    hasEmailAddress: kampagneCreateStore.versandart === Versandart.Mail ? true : null,
  }));

  const accordionIsOpen = reactive<Record<string, boolean>>({
    grunddaten: true,
    kommunikation: false,
    berufUndEinkommen: false,
    risiken: false,
    beziehungen: false,
  });
  /** Maklerfilter */
  const initialMaklerExternalId = ref<string | null>(null); // set by kampagne sender id
  const maklerExternalId = ref<string | null>(null);
  const empfohlenVonKundeId = ref<string | null>(null);

  /** Grunddaten */
  const kundennummerIntern = ref<string | null>(null);
  const ort = ref<string | null>(null);
  const plz = ref<VonBis<string | null>>({ von: null, bis: null });
  const kundenAlter = ref<VonBis<number | null>>({ von: null, bis: null });
  const geburtsmonat = ref<VonBis<string | null>>({ von: null, bis: null });
  const geburtstag = ref<VonBis<string | null>>({ von: null, bis: null });

  const berufsstatus = ref<Berufsstatus[]>([]);
  const kundenstatus = ref<Kundenstatus[]>(initialValues.value.kundenstatus);
  const zielgruppe = ref<Zielgruppe[]>([]);
  const berufsstatusAusschliessen = ref<boolean>(false);
  const statusAusschliessen = ref<boolean>(initialValues.value.statusAusschliessen);
  const zielgruppeAusschliessen = ref<boolean>(false);

  /** Kommunikation */
  const finanzmanagerAccess = ref<boolean | null>(null);
  const hasEmailAddress = ref<boolean | null>(initialValues.value.hasEmailAddress);

  /** Beruf und Einkommen */
  const beruf = ref<string | null>(null);
  const kaufmaennischTaetig = ref<VonBis<number | null>>({ von: null, bis: null });
  const koerperlichTaetig = ref<VonBis<number | null>>({ von: null, bis: null });
  const nettoeinkommen = ref<VonBis<number | null>>({ von: null, bis: null });
  const bruttoeinkommen = ref<VonBis<number | null>>({ von: null, bis: null });

  /** Risiken */
  const negativeCreditRating = ref<boolean | null>(null);
  const boat = ref<boolean | null>(null);
  const motorcycle = ref<boolean | null>(null);
  const car = ref<boolean | null>(null);
  const smoker = ref<RaucherRadioOptions>(initialValues.value.smoker);
  const petOwner = ref<boolean | null>(null);
  const dangerousHobbies = ref<boolean | null>(null);
  const seriousIllness = ref<boolean | null>(null);

  /** Beziehungen */
  const kinder = ref<boolean | null>(null);
  const kinderAlter = ref<VonBis<number | null>>({ von: null, bis: null });

  /** Vertragsdaten */
  const vertragsstatus = ref<Vertragsstatus[]>([]);
  const vertragsgesellschaften = ref<string[]>([]);
  const vertragssparten = ref<string[]>([]);
  const vertragstyp = ref<Vertragstyp | null>(null);
  const bruttobeitrag = ref<VonBis<number | null>>({ von: null, bis: null });
  const nettobeitrag = ref<VonBis<number | null>>({ von: null, bis: null });
  const verwahrstelle = ref<string | null>(null);
  const vertragsstatusAusschliessen = ref<boolean>(false);
  const vertragsgesellschaftenAusschliessen = ref<boolean>(false);
  const vertragsspartenAusschliessen = ref<boolean>(false);

  /** Dokumente */
  const dokumenttyp = ref<string | null>(null);
  const dokumenttypen = ref<DokumenttypRessource[]>([]);
  const hochgeladen = ref<VonBis<string | null>>({ von: null, bis: null });
  const dokumenttypAusschliessen = ref<boolean>(false);

  /** functions */
  const reset = (initWith: Partial<ErweiterteKundensuchePayload> = {}): void => {
    accordionIsOpen.grunddaten = true;
    accordionIsOpen.kommunikation = false;
    accordionIsOpen.berufUndEinkommen = false;
    accordionIsOpen.risiken = false;
    accordionIsOpen.beziehungen = false;

    errors.value = {};
    foundKunden.value = [];
    maklerExternalId.value = initWith.maklerExternalId ?? initialMaklerExternalId.value;
    empfohlenVonKundeId.value = initWith.empfohlenVonKundeId ?? null;
    beruf.value = initWith.beruf ?? null;
    kaufmaennischTaetig.value.von = initWith.kaufmaennischTaetig?.von ?? null;
    kaufmaennischTaetig.value.bis = initWith.kaufmaennischTaetig?.bis ?? null;
    koerperlichTaetig.value.von = initWith.koerperlichTaetig?.von ?? null;
    koerperlichTaetig.value.bis = initWith.koerperlichTaetig?.bis ?? null;
    nettoeinkommen.value.von = initWith.nettoeinkommen?.von ?? null;
    nettoeinkommen.value.bis = initWith.nettoeinkommen?.bis ?? null;
    bruttoeinkommen.value.von = initWith.bruttobeitrag?.von ?? null;
    bruttoeinkommen.value.bis = initWith.bruttobeitrag?.bis ?? null;
    negativeCreditRating.value = initWith.negativeCreditRating ?? null;
    boat.value = initWith.boat ?? null;
    motorcycle.value = initWith.motorcycle ?? null;
    car.value = initWith.car ?? null;
    smoker.value = initWith.smoker ?? initialValues.value.smoker;
    petOwner.value = initWith.petOwner ?? null;
    dangerousHobbies.value = initWith.dangerousHobbies ?? null;
    seriousIllness.value = initWith.seriousIllness ?? null;
    kinder.value = initWith.kinder ?? null;
    kinderAlter.value.von = initWith.kinderAlter?.von ?? null;
    kinderAlter.value.bis = initWith.kinderAlter?.bis ?? null;
    kundennummerIntern.value = initWith.kundennummerIntern ?? null;
    ort.value = initWith.ort ?? null;
    plz.value.von = initWith.plz?.von ?? null;
    plz.value.bis = initWith.plz?.bis ?? null;
    kundenAlter.value.von = initWith.kundenAlter?.von ?? null;
    kundenAlter.value.bis = initWith.kundenAlter?.bis ?? null;
    geburtsmonat.value.von = initWith.geburtsmonat?.von ?? null;
    geburtsmonat.value.bis = initWith.geburtsmonat?.bis ?? null;
    geburtstag.value.von = initWith.geburtstag?.von ?? null;
    geburtstag.value.bis = initWith.geburtstag?.bis ?? null;
    berufsstatus.value = initWith.berufsstatus ?? [];
    kundenstatus.value = initWith.kundenstatus ?? initialValues.value.kundenstatus;
    zielgruppe.value = initWith.zielgruppe ?? [];
    berufsstatusAusschliessen.value = initWith.berufsstatusAusschliessen ?? false;
    statusAusschliessen.value = initWith.statusAusschliessen
      ?? initialValues.value.statusAusschliessen;
    zielgruppeAusschliessen.value = initWith.zielgruppeAusschliessen ?? false;
    finanzmanagerAccess.value = initWith.finanzmanagerAccess ?? null;
    hasEmailAddress.value = initWith.hasEmailAddress ?? initialValues.value.hasEmailAddress;
    vertragsstatus.value = initWith.vertragsstatus ?? [];
    vertragsgesellschaften.value = initWith.vertragsgesellschaften ?? [];
    vertragssparten.value = initWith.vertragssparten ?? [];
    vertragstyp.value = initWith.vertragstyp ?? null;
    bruttobeitrag.value.von = initWith.bruttobeitrag?.von ?? null;
    bruttobeitrag.value.bis = initWith.bruttobeitrag?.bis ?? null;
    nettobeitrag.value.von = initWith.nettobeitrag?.von ?? null;
    nettobeitrag.value.bis = initWith.nettobeitrag?.bis ?? null;
    verwahrstelle.value = initWith.verwahrstelle ?? null;
    vertragsstatusAusschliessen.value = initWith.vertragsstatusAusschliessen ?? false;
    vertragsgesellschaftenAusschliessen.value = initWith.vertragsgesellschaftenAusschliessen
      ?? false;
    vertragsspartenAusschliessen.value = initWith.vertragsspartenAusschliessen ?? false;
    dokumenttyp.value = initWith.dokumenttyp ?? null;
    dokumenttypAusschliessen.value = initWith.dokumenttypAusschliessen ?? false;
    hochgeladen.value.von = initWith.hochgeladen?.von ?? null;
    hochgeladen.value.bis = initWith.hochgeladen?.bis ?? null;
  };

  watch(initialMaklerExternalId, () => {
    maklerExternalId.value = initialMaklerExternalId.value;
  });

  watch([finanzmanagerAccess, hasEmailAddress], () => {
    accordionIsOpen.kommunikation = true;
  });

  watch([
    beruf,
    kaufmaennischTaetig,
    koerperlichTaetig,
    nettoeinkommen,
    bruttoeinkommen,
  ], () => {
    accordionIsOpen.berufUndEinkommen = true;
  }, { deep: true });

  watch([
    negativeCreditRating,
    boat,
    motorcycle,
    car,
    smoker,
    petOwner,
    dangerousHobbies,
    seriousIllness,
  ], () => {
    accordionIsOpen.risiken = true;
  });

  watch([kinder, kinderAlter], () => {
    accordionIsOpen.beziehungen = true;
  });

  return {
    errors,
    foundKunden,
    empfaengerLimit,
    isEmpfaengerLimitReached,
    accordionIsOpen,
    initialMaklerExternalId,
    maklerExternalId,
    empfohlenVonKundeId,
    kundennummerIntern,
    ort,
    plz,
    kundenAlter,
    geburtsmonat,
    geburtstag,
    berufsstatus,
    kundenstatus,
    zielgruppe,
    berufsstatusAusschliessen,
    statusAusschliessen,
    zielgruppeAusschliessen,
    finanzmanagerAccess,
    hasEmailAddress,
    beruf,
    kaufmaennischTaetig,
    koerperlichTaetig,
    nettoeinkommen,
    bruttoeinkommen,
    negativeCreditRating,
    boat,
    motorcycle,
    car,
    smoker,
    petOwner,
    dangerousHobbies,
    seriousIllness,
    kinder,
    kinderAlter,
    vertragsstatus,
    vertragsgesellschaften,
    vertragssparten,
    vertragstyp,
    bruttobeitrag,
    nettobeitrag,
    verwahrstelle,
    vertragsstatusAusschliessen,
    vertragsgesellschaftenAusschliessen,
    vertragsspartenAusschliessen,
    dokumenttyp,
    hochgeladen,
    dokumenttypAusschliessen,
    dokumenttypen,

    reset,
    initDokumenttypen,
    submit,
    isLoading,
  };
});
