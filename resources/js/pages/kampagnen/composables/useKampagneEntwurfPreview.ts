import axios from 'axios';
import { ref } from 'vue';

import { isAxiosError } from '@/api';
import { eventBus } from '@/store/resources/store';
import { EmailElement } from '@/store/resources/types';

// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types
export function useKampagneEntwurfPreview() {
  const viewVorschauModal = ref(false);
  const previewPdf = ref<string>();
  const previewContent = ref<string>();
  const previewBetreff = ref<string>();
  const previewEmpfaenger = ref<EmailElement[]>();
  const previewAbsender = ref<EmailElement[]>();
  const isLoading = ref<boolean>(false);

  async function fetchPreview<T>(url: string, data: object, onSuccess: (response: T) => void) {
    isLoading.value = true;
    try {
      const response = await axios.post<T>(url, { data });
      if (response.status === 200) {
        viewVorschauModal.value = true;
        onSuccess(response.data);
      }
    } catch (e) {
      if (isAxiosError(e) && e.response?.status === 422) {
        eventBus.emit(
          'kampagneEntwurfPreview',
          'Kein Empfänger für die gewählte Anrede gefunden.',
        );

        return;
      }

      eventBus.emit('error', 'Vorschau konnte nicht geladen werden.');
      throw new Error('Vorschau der Kampagne konnte nicht geladen werden.');
    } finally {
      isLoading.value = false;
    }
  }

  async function getPdf(kampagneId: string, content: string, informal: boolean) {
    await fetchPreview<string>(
      `/api/kampagnen/${kampagneId}/preview/pdf`,
      {
        type: 'previewPdf',
        attributes: {
          content,
          informal,
        },
      }, (response) => {
        previewPdf.value = response;
      });
  }

  async function getHtml(kampagneId: string, betreff: string, content: string, informal: boolean) {
    await fetchPreview<{
      subject: string,
      content: string,
      absender: EmailElement[],
      empfaenger: EmailElement[],
    }>(
      `/api/kampagnen/${kampagneId}/preview/html`,
      {
        type: 'previewHtml',
        attributes: {
          subject: betreff,
          content,
          informal,
        },
      },
      (response) => {
        previewContent.value = response.content;
        previewBetreff.value = response.subject;
        previewAbsender.value = response.absender;
        previewEmpfaenger.value = response.empfaenger;
      },
    );
  }

  return {
    viewVorschauModal,
    previewPdf,
    previewContent,
    previewBetreff,
    previewEmpfaenger,
    previewAbsender,
    isLoading,
    getPdf,
    getHtml,
  };
}
