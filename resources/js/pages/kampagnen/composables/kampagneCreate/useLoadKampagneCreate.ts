import { Ref, ref } from 'vue';

import { get } from '@/api';
import { useKampagneCreateStore } from '@/pages/kampagnen/stores/kampagneCreateStore';
import { eventBus } from '@/store/resources/store';
import {
  FileResource,
  KampagneIdeeResource,
  KampagneResource,
  KundeResource,
} from '@/store/resources/types';

export const useLoadKampagneCreate = (): {
  load: (id: string) => Promise<boolean>
  loadEmpfaengers: (id: string) => Promise<boolean>
  isLoading: Ref<boolean>,
  isLoadingEmpfaengers: Ref<boolean>,
} => {
  const kampagneCreateStore =  useKampagneCreateStore();

  const isLoading = ref(false);
  const isLoadingEmpfaengers = ref(false);

  function fillFromResource(kampagne: KampagneResource) {
    kampagneCreateStore.id = kampagne.id;
    kampagneCreateStore.versandart = kampagne.attributes.versandart;
    kampagneCreateStore.titel = kampagne.attributes.titel ?? null;
    kampagneCreateStore.imNamenVon = kampagne.attributes.imNamenVon;
    kampagneCreateStore.senderExternalId = kampagne.attributes.senderExternalId ?? null;
    kampagneCreateStore.senderLabel = kampagne.attributes.senderLabel ?? null;
    kampagneCreateStore.vorgaengeAnlegen = kampagne.attributes.vorgaengeAnlegen;
    kampagneCreateStore.formalBetreff = kampagne.attributes.formalBetreff ?? null;
    kampagneCreateStore.formalContent = kampagne.attributes.formalContent ?? null;
    kampagneCreateStore.informalBetreff = kampagne.attributes.informalBetreff ?? null;
    kampagneCreateStore.informalContent = kampagne.attributes.informalContent ?? null;
    kampagneCreateStore.attachments = kampagne.attributes.attachments ?? [];
    kampagneCreateStore.status = kampagne.attributes.status ?? null;
  }

  async function loadEmpfaengers(id: string) {
    isLoadingEmpfaengers.value = true;

    try {
      const response = await get<KundeResource[]>(`kampagnen/${id}/empfaengers`, {
        fields: {
          kunden: ['name', 'firstName', 'lastName', 'email', 'externalId', 'informal'],
        },
      });

      await kampagneCreateStore.addKundenToEmpfaengers(response.data?.data ?? []);

      return response?.status === 200;
    } catch {
      return false;
    } finally {
      isLoadingEmpfaengers.value = false;
    }
  }

  async function load(id: string) {
    isLoading.value = true;
    try {
      const response = await get<KampagneResource>(`kampagnen/${id}`, {
        include: ['files', 'kampagneIdee'],
      });
      if (response.data?.data !== undefined) {
        fillFromResource(response.data.data);
        kampagneCreateStore.setInitialUploadedFiles(
          (response.data.included?.filter((elem) => elem.type === 'files') ?? []) as FileResource[],
        );
      }

      await loadEmpfaengers(id);

      kampagneCreateStore.kampagneIdee = (response.data?.included?.find(
        (resource) => resource.type === 'kampagne_ideen',
      ) as KampagneIdeeResource | undefined) ?? null;

      return response?.status === 200;
    } catch {
      eventBus.emit('error');

      return false;
    } finally {
      isLoading.value = false;
    }
  }

  return {
    load,
    loadEmpfaengers,
    isLoading,
    isLoadingEmpfaengers,
  };
};
