import { ref, Ref } from 'vue';

import { del } from '@/api';
import { eventBus } from '@/store/resources/store';
import { KampagneResource } from '@/store/resources/types';

export const useDeleteKampagne = (): {
  deleteKampagne: (id: string) => Promise<void>,
  isLoading: Ref<boolean>,
} => {
  const isLoading = ref(false);

  async function deleteKampagne(id: string) {
    isLoading.value = true;
    try {
      await del<KampagneResource>(`kampagnen/${id}`);
    } catch (e) {
      eventBus.emit('error');
    } finally {
      isLoading.value = false;
    }
  }

  return {
    deleteKampagne,
    isLoading,
  };
};
