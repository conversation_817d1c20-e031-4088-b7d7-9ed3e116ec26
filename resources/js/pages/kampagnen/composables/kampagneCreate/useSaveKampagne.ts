import axios, { AxiosRequestConfig, AxiosResponse, isCancel } from 'axios';
import { Ref, ref } from 'vue';

import { extractErrors, isAxiosError, post, put } from '@/api';
import { emptyHtmlToEmptyString } from '@/components/form/utils/emptyHtmlToEmptyString';
import { useKampagneCreateStore } from '@/pages/kampagnen/stores/kampagneCreateStore';
import { ImNamenVon, Step } from '@/pages/kampagnen/types';
import { eventBus } from '@/store/resources/store';
import { KampagneResource } from '@/store/resources/types';
import { Document } from '@/types/jsonapi';

export const useSaveKampagne = (): {
  submit: (step?: Step) => Promise<boolean>,
  sendKampagne: () => Promise<boolean>,
  isSubmitting: Ref<boolean>,
} => {
  const kampagneCreateStore =  useKampagneCreateStore();
  const isSubmitting = ref(false);
  let submitAbortController = new AbortController();

  function buildRequestData(step?: Step): {
    data: Partial<KampagneResource> & { attributes: { step?: Step } },
  } {
    return {
      data: {
        attributes: {
          titel: kampagneCreateStore.titel,
          versandart: kampagneCreateStore.versandart,
          imNamenVon: kampagneCreateStore.imNamenVon,
          vorgaengeAnlegen: kampagneCreateStore.vorgaengeAnlegen,
          ...(kampagneCreateStore.imNamenVon === ImNamenVon.Benutzer ? {
            senderExternalId: kampagneCreateStore.senderExternalId,
          } : {}),
          empfaengers: kampagneCreateStore.empfaengers.size > 0
            ? [...kampagneCreateStore.empfaengers.values()].map(
              (empfaenger) => ({
                id: parseInt(empfaenger.attributes.externalId),
                informal: empfaenger.attributes.informal ? 1 : 0,
              }),
            )
            : null,
          formalBetreff: emptyHtmlToEmptyString(kampagneCreateStore.formalBetreff),
          formalContent: emptyHtmlToEmptyString(kampagneCreateStore.formalContent),
          informalBetreff: emptyHtmlToEmptyString(kampagneCreateStore.informalBetreff),
          informalContent: emptyHtmlToEmptyString(kampagneCreateStore.informalContent),
          attachments: kampagneCreateStore.attachments,
          kampagneIdeeId: kampagneCreateStore.kampagneIdeeId,
          ...(step ? { step } : {}),
        },
      },
    };
  }

  function fillFromResource(kampagne: KampagneResource) {
    kampagneCreateStore.senderLabel = kampagne.attributes.senderLabel ?? null;
  }

  async function handleRequest(
    request: () => Promise<AxiosResponse<Document<KampagneResource>>>,
  ): Promise<AxiosResponse<Document<KampagneResource>> | undefined> {
    isSubmitting.value = true;
    try {
      const response = await request();
      kampagneCreateStore.errors = {};

      if (response.data.data === undefined) {
        eventBus.emit('error');

        return undefined;
      }

      fillFromResource(response.data.data);

      return response;
    } catch (e) {
      if (isCancel(e)) {
        return undefined;
      }

      if (!isAxiosError(e) || e.response === undefined || e.response.status >= 500) {
        eventBus.emit('error');
        throw e;
      }

      kampagneCreateStore.errors = extractErrors(
        e.response.data.errors ?? [],
      );

      return undefined;
    } finally {
      isSubmitting.value = false;
    }
  }

  async function submit(step?: Step): Promise<boolean> {
    const isAutosave = step === undefined;

    submitAbortController.abort();
    submitAbortController = new AbortController();
    // only autosaves should be cancellable
    const config: AxiosRequestConfig = isAutosave ? { signal: submitAbortController.signal } : {};

    const body = buildRequestData(step);

    const response = await handleRequest(
      kampagneCreateStore.id === undefined
        ? () => post<KampagneResource>('kampagnen/', body, undefined, config)
        : () => put<KampagneResource>(`kampagnen/${kampagneCreateStore.id}`, body, undefined, config),
    );

    kampagneCreateStore.id ??= response?.data?.data?.id;

    return response?.status === 201 || response?.status === 200;
  }

  async function sendKampagne(): Promise<boolean> {
    isSubmitting.value = true;
    try {
      const response = await axios.post(
        `/api/kampagnen/${kampagneCreateStore.id}/send`,
        {
          data: {
            attributes: {
              geplantAt: kampagneCreateStore.geplantAt === null
                ? null
                : kampagneCreateStore.geplantAt.toLocaleString('de-DE'),
            },
          },
        },
      );
      kampagneCreateStore.errors = {};

      return response?.status === 204;
    } catch (e) {
      if (!isAxiosError(e) || e.response === undefined || e.response.status >= 500) {
        eventBus.emit('error', 'Beim Versand der Kampagne ist ein Fehler aufgetreten');
        throw e;
      }

      kampagneCreateStore.errors = extractErrors(
        e.response.data.errors ?? [],
      );

      return false;
    } finally {
      isSubmitting.value = false;
    }
  }

  return {
    submit,
    sendKampagne,
    isSubmitting,
  };
};
