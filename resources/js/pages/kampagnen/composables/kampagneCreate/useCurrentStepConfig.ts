import { computed, ComputedRef } from 'vue';
import { useRouter } from 'vue-router';

import { Step } from '@/pages/kampagnen/types';

export type StepConfig = { label: string, icon: string, routeName: string, step: Step };

export const STEP_CONFIG = <StepConfig[]>[
  {
    label: 'Basis-Konfiguration',
    icon: 'sliders',
    routeName: 'kampagnen.edit.basis',
    step: 1,
  },
  {
    label: 'Empfänger',
    icon: 'users',
    routeName: 'kampagnen.edit.recipients',
    step: 2,
  },
  {
    label: 'Inhalt',
    icon: 'pen',
    routeName: 'kampagnen.edit.content',
    step: 3,
  },
  {
    label: 'Zusammenfassung',
    icon: 'list-check',
    routeName: 'kampagnen.edit.summary',
    step: 4,
  },
];

export function useCurrentStepConfig(): ComputedRef<StepConfig | undefined> {
  const router = useRouter();

  return computed(() => STEP_CONFIG.find(
    (config) => config.routeName === router.currentRoute.value.name,
  ));
}
