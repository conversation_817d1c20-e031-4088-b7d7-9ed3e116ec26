import axios from 'axios';
import { Ref, ref } from 'vue';

import { extractErrors, isAxiosError } from '@/api';
import { useKundenTable } from '@/pages/kampagnen/composables/kunden/useKundenTable';
import { useErweiterteKundensucheStore } from '@/pages/kampagnen/stores/erweiterteKundensucheStore';
import { eventBus } from '@/store/resources/store';
import { KundeResource } from '@/store/resources/types';
import { Document } from '@/types/jsonapi';

import {
  ErweiterteKundensuchePayload,
} from '../../types';

export const useSubmitErweiterteKundensuche = (): {
  submit: () => Promise<boolean>,
  isLoading: Ref<boolean>,
} => {
  const erweiterteKundensucheStore =  useErweiterteKundensucheStore();
  const isLoading = ref(false);
  const { loadMissingAddressKundenIds } = useKundenTable();

  function buildRequestData(): ErweiterteKundensuchePayload {
    return {
      maklerExternalId: erweiterteKundensucheStore.maklerExternalId,
      empfohlenVonKundeId: erweiterteKundensucheStore.empfohlenVonKundeId,
      kundennummerIntern: erweiterteKundensucheStore.kundennummerIntern,
      ort: erweiterteKundensucheStore.ort,
      plz: erweiterteKundensucheStore.plz,
      kundenAlter: erweiterteKundensucheStore.kundenAlter,
      geburtsmonat: erweiterteKundensucheStore.geburtsmonat,
      geburtstag: erweiterteKundensucheStore.geburtstag,
      berufsstatus: erweiterteKundensucheStore.berufsstatus,
      kundenstatus: erweiterteKundensucheStore.kundenstatus,
      zielgruppe: erweiterteKundensucheStore.zielgruppe,
      berufsstatusAusschliessen: erweiterteKundensucheStore.berufsstatusAusschliessen,
      statusAusschliessen: erweiterteKundensucheStore.statusAusschliessen,
      zielgruppeAusschliessen: erweiterteKundensucheStore.zielgruppeAusschliessen,
      finanzmanagerAccess: erweiterteKundensucheStore.finanzmanagerAccess,
      hasEmailAddress: erweiterteKundensucheStore.hasEmailAddress,
      beruf: erweiterteKundensucheStore.beruf,
      kaufmaennischTaetig: erweiterteKundensucheStore.kaufmaennischTaetig,
      koerperlichTaetig: erweiterteKundensucheStore.koerperlichTaetig,
      nettoeinkommen: erweiterteKundensucheStore.nettoeinkommen,
      bruttoeinkommen: erweiterteKundensucheStore.bruttoeinkommen,
      negativeCreditRating: erweiterteKundensucheStore.negativeCreditRating,
      boat: erweiterteKundensucheStore.boat,
      motorcycle: erweiterteKundensucheStore.motorcycle,
      car: erweiterteKundensucheStore.car,
      smoker: erweiterteKundensucheStore.smoker,
      petOwner: erweiterteKundensucheStore.petOwner,
      dangerousHobbies: erweiterteKundensucheStore.dangerousHobbies,
      seriousIllness: erweiterteKundensucheStore.seriousIllness,
      kinder: erweiterteKundensucheStore.kinder,
      kinderAlter: erweiterteKundensucheStore.kinderAlter,
      vertragsstatus: erweiterteKundensucheStore.vertragsstatus,
      vertragsgesellschaften: erweiterteKundensucheStore.vertragsgesellschaften,
      vertragssparten: erweiterteKundensucheStore.vertragssparten,
      vertragstyp: erweiterteKundensucheStore.vertragstyp,
      bruttobeitrag: erweiterteKundensucheStore.bruttobeitrag,
      nettobeitrag: erweiterteKundensucheStore.nettobeitrag,
      verwahrstelle: erweiterteKundensucheStore.verwahrstelle,
      vertragsstatusAusschliessen: erweiterteKundensucheStore.vertragsstatusAusschliessen,
      vertragsgesellschaftenAusschliessen:
        erweiterteKundensucheStore.vertragsgesellschaftenAusschliessen,
      vertragsspartenAusschliessen: erweiterteKundensucheStore.vertragsspartenAusschliessen,
      dokumenttyp: erweiterteKundensucheStore.dokumenttyp,
      dokumenttypAusschliessen: erweiterteKundensucheStore.dokumenttypAusschliessen,
      hochgeladen: erweiterteKundensucheStore.hochgeladen,
    };
  }

  async function submit(): Promise<boolean> {
    const body = buildRequestData();

    isLoading.value = true;
    try {
      const response = await axios.post<Document<KundeResource[]>>('/api/kunden/suche', body);
      erweiterteKundensucheStore.errors = {};

      if (response.data.data === undefined) {
        eventBus.emit('error');

        return false;
      }

      erweiterteKundensucheStore.foundKunden = response.data.data;

      void loadMissingAddressKundenIds(erweiterteKundensucheStore.foundKunden);

      return response?.status === 201 || response?.status === 200;
    } catch (e) {
      if (!isAxiosError(e) || e.response === undefined || e.response.status >= 500) {
        eventBus.emit('error');
        throw e;
      }

      erweiterteKundensucheStore.errors = extractErrors(
        e.response.data.errors ?? [],
      );

      return false;
    } finally {
      isLoading.value = false;
    }
  }

  return {
    submit,
    isLoading,
  };
};
