import { useEventListener } from '@vueuse/core';
import { reactive } from 'vue';

import { get } from '@/api';
import { eventBus } from '@/store/resources/store';
import { KundeResource } from '@/store/resources/types';

type ReturnType = {
  refreshKundeAfterOpeningTabAgain: (id: number) => void,
  isRefreshingKundeLookup: Set<number>,
};

export function useRefreshingKunde(
  refreshedKundeHandler: (refreshedKunde: KundeResource) => void,
): ReturnType {
  const isRefreshingKundeLookup = reactive(new Set<number>());

  function refreshKundeAfterOpeningTabAgain(id: number) {
    const removeListener = useEventListener(document, 'visibilitychange', () => {
      if (document.visibilityState === 'hidden') {
        return;
      }

      removeListener();
      isRefreshingKundeLookup.add(id);

      get<KundeResource>(`kunden/${id}`, undefined, {
        params: { refresh: true },
      }).then(({ data }) => {
        if (data.data) {
          refreshedKundeHandler(data.data);
        }
      }).catch(() => {
        eventBus.emit('error', 'Kunde konnte nicht aktualisiert werden');
      }).finally(() => {
        isRefreshingKundeLookup.delete(id);
      });
    });
  }

  return {
    refreshKundeAfterOpeningTabAgain,
    isRefreshingKundeLookup,
  };
}
