import { Column, createSearchFilter } from '@demvsystems/design-components';
import axios from 'axios';
import {
  computed,
  ComputedRef,
  Ref,
  ref,
  shallowRef,
  triggerRef,
} from 'vue';

import { useKampagneCreateStore } from '@/pages/kampagnen/stores/kampagneCreateStore';
import { Versandart } from '@/pages/kampagnen/types';
import { createBooleanFilter } from '@/pages/kampagnen/utils/createBooleanFilter';
import { eventBus } from '@/store/resources/store';
import { KundeResource } from '@/store/resources/types';

type Row = {
  id: string,
  name: string,
  email: string | null,
  hasAddress: boolean | undefined,
  informal: boolean,
  url?: string,
};

const missingAddressKundenIds = shallowRef(new Set<string>());
const hasLoadingFailed = ref(false);
const isLoading = ref(false);

export const useKundenTable = (kunden?: Ref<KundeResource[]>, options?: { withFilters: boolean }): {
  columns: ComputedRef<Record<string, Column>>,
  rows: ComputedRef<Row[]>,
  loadMissingAddressKundenIds: (kunden: KundeResource[]) => Promise<void>,
  isLoading: Ref<boolean>,
  missingAddressKundenIds: Ref<Set<string>>,
} => {
  const { withFilters = false } = options ?? {};

  const kampagneCreateStore = useKampagneCreateStore();

  function getEmailColumn(): Column {
    return {
      label: 'E-Mail',
      ...(withFilters ? {
        format: {
          filter: createBooleanFilter({
            label: 'E-Mail vorhanden',
            filter: (row: Record<string, unknown>) => row.email !== null && row.email !== '',
          }, {
            label: 'E-Mail fehlt / unvollständig',
            filter: (row: Record<string, unknown>) => row.email === null || row.email === '',
          }),
        },
      } : {}),
    };
  }

  function getAddressColumn(): Column {
    return {
      label: 'Adresse',
      ...(withFilters ? {
        format: {
          filter: createBooleanFilter({
            label: 'Adresse vorhanden',
            filter: (row: Record<string, unknown>) => row.hasAddress === true,
          }, {
            label: 'Adresse fehlt / unvollständig',
            filter: (row: Record<string, unknown>) => row.hasAddress === false,
          }),
        },
      } : {}),
    };
  }

  function getNameColumn(): Column {
    return {
      label: 'Kunde',
      format: {
        sort(a: {
          firstName: string;
          lastName: string;
        }, b: {
          firstName: string;
          lastName: string;
        }) {
          return a.lastName.localeCompare(b.lastName) || a.firstName.localeCompare(b.firstName);
        },
      },
      ...(withFilters ? {
        filter: createSearchFilter(['name']),
      } : {}),
    };
  }

  const columns = computed<Record<string, Column>>(() => {
    const versandartColumn: Record<string, Column> = (
      kampagneCreateStore.versandart === Versandart.Mail
        ? { email: getEmailColumn() }
        : { hasAddress: getAddressColumn() }
    );

    return {
      name: getNameColumn(),
      ...versandartColumn,
    };
  });

  function getRow(kunde: KundeResource) {
    return {
      id: kunde.id,
      name: kunde.attributes.name,
      firstName: kunde.attributes.firstName,
      lastName: kunde.attributes.lastName,
      email: kunde.attributes.email,
      hasAddress: hasLoadingFailed.value
        ? undefined
        : !(missingAddressKundenIds.value.has(kunde.attributes.externalId)),
      informal: kunde.attributes.informal,
      url: kunde.links?.external,
    };
  }

  async function loadMissingAddressKundenIds(kundenToUpdate: KundeResource[]) {
    if (kundenToUpdate.length === 0) {
      return;
    }

    const kundenExternalIds = kundenToUpdate.map((kunde) => kunde.attributes.externalId);
    kundenExternalIds.forEach((kundeExternalId) => (
      missingAddressKundenIds.value.delete(kundeExternalId)
    ));

    try {
      isLoading.value = true;
      const response = await axios.post<{ data: string[] }>('/api/kunden/missing-address', { data: kundenExternalIds });

      response.data.data.forEach((kundeExternalId) => (
        missingAddressKundenIds.value.add(kundeExternalId)
      ));

      triggerRef(missingAddressKundenIds);
    } catch (e) {
      eventBus.emit('error', 'Bei dem Laden der Adressen ist ein Fehler aufgetreten');

      hasLoadingFailed.value = true;

      throw e;
    } finally {
      isLoading.value = false;
    }
  }

  // if no kunden are given, we do not return any rows
  const rows = computed(() => (kunden?.value ?? []).map(getRow));

  return {
    columns,
    rows,
    loadMissingAddressKundenIds,
    isLoading,
    missingAddressKundenIds,
  };
};
