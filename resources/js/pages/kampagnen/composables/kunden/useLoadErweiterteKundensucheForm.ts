import { get } from '@/api';
import { useErweiterteKundensucheStore } from '@/pages/kampagnen/stores/erweiterteKundensucheStore';
import { DokumenttypRessource } from '@/store/resources/types';

export function useLoadErweiterteKundensucheForm(): {
  initDokumenttypen: () => Promise<void>,
} {
  const erweiterteKundensuche = useErweiterteKundensucheStore();
  async function initDokumenttypen() {
    try {
      const response = await get<DokumenttypRessource[]>('/dokumenttypen', {
        sort: [{
          name: 'name',
        }],
      });

      if (response.data?.data === undefined) {
        return;
      }

      erweiterteKundensuche.dokumenttypen = response.data.data;
    } catch {
      return;
    }
  }

  return {
    initDokumenttypen,
  };
}
