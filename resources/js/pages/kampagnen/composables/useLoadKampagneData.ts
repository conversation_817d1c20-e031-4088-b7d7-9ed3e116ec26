import { onUnmounted, Ref, ref } from 'vue';

import { get } from '@/api';
import { KampagneStatus } from '@/pages/kampagnen/types';
import { eventBus } from '@/store/resources/store';
import {
  EmailMetaResource,
  KampagneMessageResource,
  KampagneResource,
} from '@/store/resources/types';

const FETCH_INTERVAL = 3000;

export function useLoadKampagneData(): {
  kampagneId: Ref<string>,
  messageMap: Ref<Map<string, KampagneMessageResource>>,
  emailMap: Ref<Map<string, EmailMetaResource>>,
  isLoadingMessages: Ref<boolean>,
  fetchMessages: () => Promise<void>,
  getPdfDownloadUrl: (messageId?: string) => string,
  messagesCount: Ref<number>,
  sentMessages: Ref<number>,
} {
  const kampagneId = ref<string>('');
  const messageMap = ref(new Map<string, KampagneMessageResource>());
  const emailMap = ref(new Map<string, EmailMetaResource>());
  const lastMessageDate = ref<string | undefined>(undefined);
  const isLoadingMessages = ref(false);
  const stopFetching = ref(false);

  const messagesCount = ref<number>(0);
  const sentMessages = ref<number>(0);

  function updateMessageMap(newMessages: KampagneMessageResource[]): void {
    return newMessages.forEach((newMessage) => {
      messageMap.value.set(newMessage.id, newMessage);
    });
  }

  function updateEmailMap(newEmails: EmailMetaResource[]): void {
    return newEmails.forEach((newEmail) => {
      emailMap.value.set(newEmail.id, newEmail);
    });
  }

  async function fetchMessages(): Promise<void> {
    isLoadingMessages.value = true;

    try {
      const response = await get<KampagneMessageResource[]>(
        `kampagnen/${kampagneId.value}/messages`,
        {
          include: ['kampagne'],
        },
        {
          params: {
            since: lastMessageDate.value,
          },
        },
      );

      const kampagne = response.data.included?.find(
        (item) => item.type === 'kampagnen',
      ) as null | KampagneResource;
      const newEmails = (response.data.included?.filter(
        (item) => item.type === 'emailMetas',
      ) ?? []) as EmailMetaResource[];
      const newMessages = response.data.data;

      sentMessages.value = kampagne?.attributes.sentMessages ?? 0;
      messagesCount.value = kampagne?.attributes.messagesCount ?? 0;
      if (newMessages && newMessages.length > 0) {
        updateMessageMap(newMessages);
        lastMessageDate.value = newMessages[newMessages.length - 1]?.attributes.updatedAt;
      }

      if (newEmails && newEmails.length > 0) {
        updateEmailMap(newEmails);
      }

      // if we get no new messages, we do not get an updated status
      // so we have to keep polling
      if (!stopFetching.value && (
        newMessages?.length === 0
        || kampagne?.attributes.status !== KampagneStatus.Abgeschlossen
      )) {
        setTimeout(() => {
          void fetchMessages();
        }, FETCH_INTERVAL);
      } else {
        isLoadingMessages.value = false;
      }
    } catch (e) {
      eventBus.emit('error', 'Beim Laden der Nachrichten ist ein Fehler aufgetreten.');

      isLoadingMessages.value = false;
    }
  }

  onUnmounted(() => {
    stopFetching.value = true;
  });

  function getPdfDownloadUrl(messageId?: string): string {
    return `/api/kampagnen/${kampagneId.value}/pdf/${messageId ?? ''}`;
  }

  return {
    kampagneId,
    messageMap,
    emailMap,
    isLoadingMessages,
    fetchMessages,
    getPdfDownloadUrl,
    messagesCount,
    sentMessages,
  };
}
