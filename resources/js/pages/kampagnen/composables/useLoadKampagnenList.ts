import { Ref, ref } from 'vue';

import { get } from '@/api';
import { useKampagnenStore } from '@/pages/kampagnen/stores/kampagnenUebersichtStore';
import { eventBus } from '@/store/resources/store';
import { KampagneResource } from '@/store/resources/types';

export function useLoadKampagnenList(): {
  loadKampagnen: () => Promise<void>;
  isLoading: Ref<boolean>;
} {
  const kampagneListStore = useKampagnenStore();
  const isLoading = ref(false);
  async function loadKampagnen() {
    isLoading.value = true;

    try {
      const response = await get<KampagneResource[]>('/kampagnen', {
        include: ['ersteller', 'sender'],
        sort: [{
          name: 'updatedAt',
          order: 'desc',
        }],
      });

      if (response.data?.data === undefined) {
        return;
      }

      kampagneListStore.kampagnenList = response.data.data;
    } catch {
      eventBus.emit('error', 'Bei dem Laden der Kampagnen ist ein Fehler aufgetreten');
    }

    isLoading.value = false;
  }

  return {
    loadKampagnen,
    isLoading,
  };
}
