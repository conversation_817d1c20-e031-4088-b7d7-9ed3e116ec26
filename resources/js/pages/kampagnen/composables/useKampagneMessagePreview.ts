import axios from 'axios';
import { ref } from 'vue';

import { useTagStore } from '@/components/extensions/tags/tagStore';
import { BriefAbsender, BriefEmpfaenger } from '@/pages/vorgangAnlegen/types';
import { eventBus } from '@/store/resources/store';

// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types
export function useKampagneMessagePreview() {
  const viewVorschauModal = ref(false);
  const previewPdf = ref<string | undefined>(undefined);
  const previewContent = ref<string | undefined>(undefined);
  const previewBetreff = ref<string | undefined>(undefined);
  const isLoading = ref<boolean>(false);

  const tagStore = useTagStore();

  async function fetchPreview<T>(url: string, data: object, onSuccess: (response: T) => void) {
    isLoading.value = true;
    try {
      const response = await axios.get<T>(url, { data });
      if (response.status === 200) {
        viewVorschauModal.value = true;
        onSuccess(response.data);
      }
    } catch (e) {
      eventBus.emit('error', 'Nachricht konnte nicht geladen werden.');
      throw new Error('Nachricht konnte nicht geladen werden.');
    } finally {
      isLoading.value = false;
    }
  }

  async function getPdf(kampagneId: string, messageId: string) {
    await fetchPreview<string>(`/api/kampagnen/${kampagneId}/preview/${messageId}/pdf`, {
      type: 'previewPdf',
      attributes: {
        absender: BriefAbsender.Makler,
        empfaenger: BriefEmpfaenger.Kunde,
        tags: tagStore.tags,
      },
    }, (response) => {
      previewPdf.value = response;
    });
  }

  async function getHtml(kampagneId: string, messageId: string) {
    await fetchPreview<{ subject: string; content: string }>(
      `/api/kampagnen/${kampagneId}/preview/${messageId}/html`,
      {
        type: 'previewHtml',
        attributes: {
          tags: tagStore.tags,
        },
      },
      (response) => {
        previewContent.value = response.content;
        previewBetreff.value = response.subject;
      },
    );
  }

  return {
    viewVorschauModal,
    previewPdf,
    previewContent,
    previewBetreff,
    isLoading,
    getPdf,
    getHtml,
  };
}
