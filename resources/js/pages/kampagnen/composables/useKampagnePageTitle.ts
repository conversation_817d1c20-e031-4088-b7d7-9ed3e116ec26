import { Ref, ref, watch } from 'vue';
import { useRouter } from 'vue-router';

import {
  useCurrentStepConfig,
} from '@/pages/kampagnen/composables/kampagneCreate/useCurrentStepConfig';

const kampagneTitle = ref('');

export function useKampagnePageTitle(): {
  kampagneTitle: Ref<string>,
  setPageTitle: (title: string) => void
} {
  const currentStepConfig = useCurrentStepConfig();
  const router = useRouter();

  function setPageTitle(title: string) {
    kampagneTitle.value = currentStepConfig.value !== undefined
      ? `${title} - ${currentStepConfig.value.label}`
      : title;

    document.title = `${kampagneTitle.value} | Vorgangsmanager`;
  }

  watch(router.currentRoute, () => {
    kampagneTitle.value = router.currentRoute.value.meta.title.toString();
  }, { immediate: true });

  return {
    kampagneTitle,
    setPageTitle,
  };
}
