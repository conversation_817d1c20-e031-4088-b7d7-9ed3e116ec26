import { BadgeType } from '@demvsystems/design-components';

export enum Versandart {
  Brief = 'brief',
  Mail = 'email',
}

export enum KampagneMessageStatus {
  // EmailMetaStatus
  Versandbereit = 'versandbereit',
  InVersand = 'in_versand',
  Abgeschlossen = 'abgeschlossen',
  Fehler = 'fehler',
  // FileStatus
  Pending = 'pending',
  Uploaded = 'uploaded',
  Error = 'error',
}

export enum KampagneStatus {
  Entwurf = 'entwurf',
  InBearbeitung = 'in_bearbeitung',
  Abgeschlossen = 'abgeschlossen',
  Geplant = 'geplant',
}

export enum ImNamenVon {
  Mir = 'mir',
  Benutzer = 'benutzer',
  ZustaendigerVermittler = 'vermittler',
}

export enum Kundenstatus {
  Kunde = '1',
  Interessent = '2',
  KundeA = '3',
  KundeB = '4',
  KundeC = '5',
  KundeD = '8',
  KundeStorniert = '6',
  KundeVerstorben = '7',
  InteressentA = '9',
  InteressentB = '10',
  InteressentC = '11',
  InteressentD = '12',
}

export enum Berufsstatus {
  Angestellte = '2',
  Minijob = '22',
  AngestellteOeffentlicherDienst = '10',
  Arbeitssuchend = '20',
  BeamterAufLebenszeit = '5',
  BeamterAufZeitWiderrufProbe = '8',
  Bundeswehr = '26',
  ErwerbsunfaehigNichtErwerbstaetig = '24',
  GeschaeftsfuehrenderGesellschafter = '11',
  GeschaeftsfuehrenderGesellschafterOhneEntgeltfortzahlung = '25',
  HausfrauHausmann = '17',
  PersonInBerufsausbildung = '14',
  PersonInElternzeit = '18',
  PersonInSchulausbildungPraktikumFsj = '13',
  PersonInStudium = '15',
  RentnerRuhestaendler = '19',
  SelbststaendigerFreiberufler = '3',
  Sonstige = '23',
  Vorstand = '12',
}

export enum Zielgruppe {
  Angestellte = '2',
  AngestellteOeffentlicherDienst = '14',
  Aerzte = '12',
  Auszubildende = '10',
  Beamte = '4',
  Existenzgruender = '7',
  Familie = '16',
  FreieBerufe = '15',
  GesellschafterGeschaeftsfuehrerGmbH = '11',
  HaeusleBauer = '20',
  Hausverwalter = '28',
  Humanmediziner = '25',
  Immobilienbesitzer = '24',
  Kinder = '19',
  KuenstlerUndPublizisten = '8',
  Landwirte = '23',
  Mediziner = '21',
  Piloten = '6',
  Ruhestaendler = '17',
  Schueler = '22',
  SelbststaendigeHandwerker = '3',
  SelbststaendigeFreiberufler = '1',
  SoldatenPolizistenFeuerwehrleute = '5',
  Studenten = '9',
  Veterinaermediziner = '27',
  Zahnaerzte = '26',
}

export enum KundensucheRadioOptions {
  Ja = 'ja',
  Nein = 'nein',
  NichtBeachten = 'nicht_beachten',
}

export enum RaucherRadioOptions {
  Ja = '1',
  Nein = '2',
  Gelegentlich = '3',
  NichtBeachten = '',
}

export enum Vertragsstatus {
  Aktiv = '1',
  Storniert = '2',
  Beitragsfrei = '3',
  Sonstige = '4',
  Ruhend = '5',
}

export enum Vertragstyp {
  Eigenvertrag = '1',
  Fremdvertrag = '2',
  Korrespondenzmakler = '3',
  Antrag = '5',
  Voranfrage = '6',
}

export type Empfaenger = {
  id: number,
  informal: 0 | 1,
};

export type Step = 1 | 2 | 3 | 4;

export type EmpfaengerAction = {
  text: string;
  handler: () => void;
};

export type VonBis<T> = {
  von: T,
  bis: T,
};

export type KampagneMessageAdresse = {
  plz: string,
  ort: string,
  strasse: string,
};

export type StatusColumn = {
  label: string,
  variant: BadgeType,
};

export type ErweiterteKundensuchePayload = {
  maklerExternalId: string | null,
  empfohlenVonKundeId: string | null,
  kundennummerIntern: string | null,
  ort: string | null,
  plz: VonBis<string | null>,
  kundenAlter: VonBis<number | null>,
  geburtsmonat: VonBis<string | null>,
  geburtstag: VonBis<string | null>,
  berufsstatus: Berufsstatus[],
  kundenstatus: Kundenstatus[],
  zielgruppe: Zielgruppe[],
  berufsstatusAusschliessen: boolean,
  statusAusschliessen: boolean,
  zielgruppeAusschliessen: boolean,
  finanzmanagerAccess: boolean | null,
  hasEmailAddress: boolean | null,
  beruf: string | null,
  kaufmaennischTaetig: VonBis<number | null>,
  koerperlichTaetig: VonBis<number | null>,
  nettoeinkommen: VonBis<number | null>,
  bruttoeinkommen: VonBis<number | null>,
  negativeCreditRating: boolean | null,
  boat: boolean | null,
  motorcycle: boolean | null,
  car: boolean | null,
  smoker: RaucherRadioOptions,
  petOwner: boolean | null,
  dangerousHobbies: boolean | null,
  seriousIllness: boolean | null,
  kinder: boolean | null,
  kinderAlter: VonBis<number | null>,
  vertragsstatus: Vertragsstatus[] | null,
  vertragsgesellschaften: string[] | null,
  vertragssparten: string[] | null,
  vertragstyp: Vertragstyp | null,
  bruttobeitrag: VonBis<number | null>,
  nettobeitrag: VonBis<number | null>,
  verwahrstelle: string | null,
  vertragsstatusAusschliessen: boolean,
  vertragsgesellschaftenAusschliessen: boolean,
  vertragsspartenAusschliessen: boolean,
  dokumenttyp: string | null,
  dokumenttypAusschliessen: boolean,
  hochgeladen: VonBis<string | null>,
};
