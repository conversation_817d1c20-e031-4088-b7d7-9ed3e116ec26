// this file can be replaced with DSYS-282 once implemented
import { createEnumFilter } from '@demvsystems/design-components';

type BooleanFilterValue = {
  label: string,
  filter: (row: Record<string, unknown>) => boolean,
};

// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types
export function createBooleanFilter(trueValue: BooleanFilterValue, falseValue: BooleanFilterValue) {
  return {
    ...createEnumFilter([trueValue.label, falseValue.label]),
    createFilter(props: Record<string, string[]>): (row: Record<string, unknown>) => boolean {
      return (row: Record<string, unknown>) => {
        if (props.selected?.length === 0) {
          return false;
        }

        if (props.selected?.length === 2) {
          return true;
        }

        return props.selected?.[0] === trueValue.label
          ? trueValue.filter(row)
          : falseValue.filter(row);
      };
    },
  };
}
