<template>
  <DsModal
    ref="abortPlanungModal"
    icon="question"
    size="sm"
    title="Planung abbrechen?"
    :variant="ModalVariant.Info"
    cancel-label="Nein"
    confirm-label="Ja, Planung abbrechen"
  >
    <div class="space-y-2">
      <p>
        Möchten Sie die Kampagne bearbeiten?
        Dadurch wird der geplante Versand abgebrochen und Sie müssen diesen erneut einstellen.
      </p>
    </div>
  </DsModal>
</template>

<script setup lang="ts">
import { DsModal, ModalInstance, ModalVariant } from '@demvsystems/design-components';
import axios from 'axios';
import { ref } from 'vue';

import { eventBus } from '@/store/resources/store';

const abortPlanungModal = ref<ModalInstance | null>(null);
const emit = defineEmits<{
  (event: 'navigate', id: string): void,
}>();

function confirmModal(id: string) {
  void abortPlanungModal.value?.open({
    confirmed: () => abortPlanung(id),
  });
}

async function abortPlanung(id: string) {
  const success = ref<boolean>(false);
  try {
    const response = await axios.post(`/api/kampagnen/${id}/abortPlanung`, {
      headers: {
        'Content-Type': 'application/json',
      },
    });

    success.value = response?.status === 204;
  } catch (e) {
    eventBus.emit('error', 'Beim Abbrechen der Versandplanung ist ein Fehler aufgetreten');
    throw e;
  }

  if (success.value) {
    emit('navigate', id);
  }
}

defineExpose({ confirmModal });
</script>
