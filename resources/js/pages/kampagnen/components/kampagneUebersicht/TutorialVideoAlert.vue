<template>
  <DsAlert
    label="Sie möchten mehr über die Möglichkeiten der Kampagnen erfahren?"
    type="info"
  >
    <div
      class="flex flex-row gap-x-4"
    >
      <DsButton
        size="sm"
        href="https://www.youtube.com/watch?v=X0o2iSrtTPA"
        target="_blank"
        rel="noopener noreferrer"
        variant="outline"
        icon="play"
      >
        Erklärvideo ansehen
      </DsButton>
      <DsButton
        size="sm"
        variant="secondary"
        icon="close"
        @click="emit('hideVideoAlert')"
      >
        Hinweis ausblenden
      </DsButton>
    </div>
  </DsAlert>
</template>

<script setup lang="ts">
import { DsAlert, DsButton } from '@demvsystems/design-components';

const emit = defineEmits(['hideVideoAlert']);
</script>
