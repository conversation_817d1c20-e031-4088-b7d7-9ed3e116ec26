<template>
  <div class="space-y-3">
    <DsTable
      class="overflow-hidden rounded-md border border-gray-200"
      :style="`max-height: ${maxHeight}`"
      :data="kampagneRows"
      :columns="columns"
      :item-height="48"
      :no-data-text="noDataText ?? ''"
      virtualized
    >
      <template #entry-versandart="{value}">
        <DsIcon
          :name="value"
          :title="value === 'at' ? 'E-Mail' : 'Brief'"
        />
      </template>
      <template #entry-titel="{value}">
        <span
          class="truncate"
          :title="value"
        >
          {{ value }}
        </span>
      </template>
      <template #entry-absender="{value}">
        <span
          class="truncate"
          :title="value"
        >
          {{ value }}
        </span>
      </template>
      <template #entry-versendetAt="{value}">
        <TimeAgoText
          :value="value"
          only-date
        />
      </template>
      <template #entry-status="{entry}">
        <DsBadge
          :type="entry.status.variant"
        >
          {{ entry.status.label }}
        </DsBadge>
      </template>
      <template #entry-empfaenger="{entry}">
        <div class="flex items-center space-x-1">
          <div>
            {{ entry.empfaenger }}
          </div>
          <div
            v-if="entry.failedMessages > 0"
            class="shrink-0 border-orange-500 leading-none text-orange-700"
            :title="`Bei ${entry.failedMessages === 1 ? 'einem Empfänger' : 'mehreren Empfängern'} ist ein Fehler aufgetreten`"
          >
            <DsIcon
              name="exclamation-triangle"
              size="lg"
              fixed-width
            />
          </div>
        </div>
      </template>
      <template #entry-aktionen="{entry}">
        <div class="space-x-2">
          <DsButton
            icon="arrow-right"
            title="Zur Kampagne"
            variant="clear"
            size="sm"
            @click="navigate(entry.id)"
          />
        </div>
      </template>
    </DsTable>
  </div>
</template>

<script setup lang="ts">
import {
  Column,
  DsBadge,
  DsButton,
  DsIcon,
  DsTable,
} from '@demvsystems/design-components';
import { computed } from 'vue';
import { useRouter } from 'vue-router';

import TimeAgoText from '@/components/tags/TimeAgoText.vue';
import { KampagneStatus, StatusColumn, Versandart } from '@/pages/kampagnen/types';
import { KampagneResource } from '@/store/resources/types';

const props = defineProps<{
  kampagnen: KampagneResource[],
  noDataText?: string,
  maxHeight?: string,
}>();

const columns = computed<Record<string, Column>>(() => {
  return {
    versandart: { label: 'Typ', width: 60 },
    titel: { label: 'Titel', width: 160 },
    absender: { label: 'Absender' },
    versendetAt: { label: 'Versendet am' },
    status: {
      label: 'Status',
      width: 165,
      format: {
        sort(a: Record<string, StatusColumn>, b: Record<string, StatusColumn>): number {
          return a.status?.label.localeCompare(b.status?.label ?? '') ?? 0;
        },
      },
    },
    empfaenger: {
      width: 120,
      label: 'Empfänger',
    },
    aktionen: {
      label: 'Aktionen',
      width: 100,
      notSortable: true,
      format: {
        classes: ['justify-end'],
      },
    },
  };
});

const router = useRouter();

function badgeKampagneStatus(status?: KampagneStatus) {
  switch (status) {
    case KampagneStatus.Entwurf:
      return { label: 'Entwurf', variant: 'primary' };
    case KampagneStatus.InBearbeitung:
      return { label: 'In Bearbeitung', variant: 'default' };
    case KampagneStatus.Abgeschlossen:
      return { label: 'Abgeschlossen', variant: 'success' };
    default:
      return { label: 'Unbekannt', variant: 'default' };
  }
}

const kampagneRows = computed(() => {
  return props.kampagnen.map((kampagne: KampagneResource) => ({
    id: kampagne.id,
    versandart: kampagne.attributes.versandart === Versandart.Mail
      ? 'at'
      : 'envelope',
    titel: kampagne.attributes.titel ?? 'Kein Titel',
    absender: kampagne.attributes.senderLabel ?? 'Fehler',
    versendetAt: kampagne.attributes.versendetAt,
    empfaenger: kampagne.attributes.messagesCount ?? 'Keine Empfänger',
    failedMessages: kampagne.attributes.failedMessages,
    status: badgeKampagneStatus(kampagne.attributes.status),
  }));
});

async function navigate(id: string) {
  await router.push({
    name: 'kampagnen.show',
    params: { id },
  });
}
</script>
