<template>
  <div class="space-y-3">
    <DsTable
      class="overflow-hidden rounded-md border border-gray-200"
      :style="`max-height: ${maxHeight}`"
      :data="kampagneRows"
      :columns="columns"
      :item-height="48"
      :no-data-text="noDataText ?? ''"
      :sort-settings="sortSettings"
      virtualized
    >
      <template #entry-versandart="{value}">
        <DsIcon
          :name="value"
          :title="value === 'at' ? 'E-Mail' : 'Brief'"
        />
      </template>
      <template #entry-titel="{value}">
        <span
          class="truncate"
          :title="value"
        >
          {{ value }}
        </span>
      </template>
      <template #entry-absender="{value}">
        <span
          class="truncate"
          :title="value"
        >
          {{ value }}
        </span>
      </template>
      <template #entry-updatedAt="{value}">
        <TimeAgoText :value="value" />
      </template>
      <template #entry-geplantAt="{value}">
        <TimeAgoText :value="value" />
      </template>
      <template #entry-aktionen="{entry}">
        <div class="space-x-2">
          <KampagneLoeschenButton
            :id="entry.id"
          />
          <DsButton
            data-test="kampagne-uebersicht__edit-button"
            icon="edit"
            title="Kampagne bearbeiten"
            variant="clear"
            size="sm"
            @click="status === KampagneStatus.Entwurf ? navigate(entry.id) : confirmModal(entry.id)"
          />
        </div>
      </template>
    </DsTable>
    <KampagneAbortModal
      v-if="props.status === KampagneStatus.Geplant"
      ref="abortPlanungModal"
      @navigate="navigate"
    />
  </div>
</template>

<script setup lang="ts">
import {
  Column,
  DsButton,
  DsIcon,
  DsTable,
  SortOrder,
} from '@demvsystems/design-components';
import { computed, ref } from 'vue';
import { useRouter } from 'vue-router';

import TimeAgoText from '@/components/tags/TimeAgoText.vue';
import KampagneAbortModal from '@/pages/kampagnen/components/KampagneAbortModal.vue';
import KampagneLoeschenButton from '@/pages/kampagnen/components/KampagneLoeschenButton.vue';
import { KampagneStatus, Versandart } from '@/pages/kampagnen/types';
import { KampagneResource } from '@/store/resources/types';

const props = defineProps<{
  kampagnen: KampagneResource[],
  noDataText?: string,
  status: KampagneStatus,
  maxHeight?: string,
}>();

const sortSettings = computed(() => (
  props.status === KampagneStatus.Geplant
    ? { name: 'geplantAt', order: SortOrder.Ascending }
    : { name: 'updatedAt', order: SortOrder.Descending }
));

const columns = computed<Record<string, Column>>(() => {
  return {
    versandart: { label: 'Typ', width: 60 },
    titel: { label: 'Titel' },
    absender: { label: 'Absender' },
    ...(props.status === KampagneStatus.Entwurf ? {

      updatedAt: {
        label: 'Letzte Änderung',
      },
    } : {
      geplantAt: {
        label: 'Geplant am',
      },
    }),
    empfaenger: {
      label: 'Empfänger',
      width: 120,
    },
    aktionen: {
      label: 'Aktionen',
      width: 100,
      notSortable: true,
      format: {
        classes: ['justify-end'],
      },
    },
  };
});

const router = useRouter();

const kampagneRows = computed(() => {
  return props.kampagnen.map((kampagne: KampagneResource) => ({
    id: kampagne.id,
    versandart: kampagne.attributes.versandart === Versandart.Mail
      ? 'at'
      : 'envelope',
    titel: kampagne.attributes.titel ?? 'Kein Titel',
    absender: kampagne.attributes.senderLabel ?? 'Fehler',
    updatedAt: kampagne.attributes.updatedAt,
    geplantAt: kampagne.attributes.geplantAt,
    empfaenger: kampagne.attributes.empfaengers?.length ?? 'Keine Empfänger',
  }));
});

const abortPlanungModal = ref<InstanceType<typeof KampagneAbortModal> | null>(null);
function confirmModal(id: string) {
  if (props.status === KampagneStatus.Geplant) {
    abortPlanungModal.value?.confirmModal(id);
  }
}

async function navigate(id: string) {
  await router.push({
    name: 'kampagnen.edit.basis',
    params: { id },
  });
}
</script>
