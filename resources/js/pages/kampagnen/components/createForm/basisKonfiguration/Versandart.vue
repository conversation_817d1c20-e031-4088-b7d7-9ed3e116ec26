<template>
  <DsFormGroup
    label="Versandart"
    class="w-fit"
    data-test="kampagne-create__step1__versandart"
  >
    <DsRadioGroup
      v-model="kampagneCreateStore.versandart"
      variant="button"
    >
      <DsRadioButton
        :value="Versandart.Mail"
        icon="at"
        data-test="kampagne-create__step1__versandart__email"
      >
        E-Mail
      </DsRadioButton>
      <DsRadioButton
        :value="Versandart.Brief"
        icon="envelope"
        data-test="kampagne-create__step1__versandart__brief"
      >
        Brief
      </DsRadioButton>
    </DsRadioGroup>
  </DsFormGroup>
</template>

<script setup lang="ts">
import { DsFormGroup, DsRadioButton, DsRadioGroup } from '@demvsystems/design-components';

import { useKampagneCreateStore } from '@/pages/kampagnen/stores/kampagneCreateStore';
import { Versandart } from '@/pages/kampagnen/types';

const kampagneCreateStore = useKampagneCreateStore();
</script>
