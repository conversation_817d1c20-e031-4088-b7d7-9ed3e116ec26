<template>
  <DsFormGroup
    label="Kampagnentitel"
    class="grow"
    data-test="kampagne-create__step1__titel"
    validation-name="attributes.titel"
    required
  >
    <DsInput
      v-model="kampagneCreateStore.titel"
      required
    />
  </DsFormGroup>
</template>

<script setup lang="ts">
import { DsFormGroup, DsInput } from '@demvsystems/design-components';

import { useKampagneCreateStore } from '@/pages/kampagnen/stores/kampagneCreateStore';

const kampagneCreateStore = useKampagneCreateStore();
</script>
