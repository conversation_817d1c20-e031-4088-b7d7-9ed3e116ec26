<template>
  <DsFormGroup
    data-test="kampagne-create__step1__vorgaenge-anlegen"
  >
    <DsSwitch
      v-model="kampagneCreateStore.vorgaengeAnlegen"
    >
      Vorgänge anlegen?
    </DsSwitch>
  </DsFormGroup>
</template>

<script setup lang="ts">
import { DsFormGroup, DsSwitch } from '@demvsystems/design-components';

import { useKampagneCreateStore } from '@/pages/kampagnen/stores/kampagneCreateStore';

const kampagneCreateStore = useKampagneCreateStore();
</script>
