<template>
  <DsModal
    ref="kampagnenIdeenModal"
    title="Kampagnenideen"
    hide-buttons
    action-required
    size="md"
    :show="modalOpen"
  >
    <div
      class="-mx-6 flex flex-col overflow-y-auto p-6"
      style="height: max(calc(100vh - 40rem), 28rem)"
    >
      <div
        v-if="isLoading"
        class="mb-6 space-y-3"
      >
        <DsSkeleton class="h-20 w-full" />
        <DsSkeleton class="h-20 w-full opacity-75" />
        <DsSkeleton class="h-20 w-full opacity-50" />
        <DsSkeleton class="h-20 w-full opacity-25" />
      </div>
      <DsRadioGroup
        v-if="!isLoading"
        v-model="selectedKampagneIdeeId"
        variant="card"
      >
        <DsRadioButton
          v-for="idee in sortedIdeen"
          :key="idee.id"
          :icon="idee.attributes.isHighlighted ? 'star' : undefined"
          :value="idee.id"
          :label="idee.attributes.name"
          :class="idee.attributes.isHighlighted ? 'text-blue-700' : ''"
        >
          {{ idee.attributes.description }}
        </DsRadioButton>
      </DsRadioGroup>
    </div>

    <div class="-mx-6 flex justify-end space-x-3 border-t px-6 pt-6">
      <DsButton
        size="lg"
        variant="secondary"
        @click="modalOpen = false"
      >
        Ohne Idee fortfahren
      </DsButton>
      <DsButton
        :disabled="selectedKampagneIdeeId === undefined"
        size="lg"
        variant="primary"
        @click="fillStoreFromIdee"
      >
        Mit Idee starten
      </DsButton>
    </div>
  </DsModal>
</template>

<script setup lang="ts">
import {
  DsButton,
  DsModal,
  DsRadioButton,
  DsRadioGroup,
  DsSkeleton,
  ModalInstance,
} from '@demvsystems/design-components';
import { computed, onMounted, ref } from 'vue';

import { get } from '@/api';
import { useKampagneCreateStore } from '@/pages/kampagnen/stores/kampagneCreateStore';
import { Versandart } from '@/pages/kampagnen/types';
import { eventBus } from '@/store/resources/store';
import { KampagneIdeeResource } from '@/store/resources/types';

const kampagneCreateStore = useKampagneCreateStore();
const kampagneIdeen = ref<KampagneIdeeResource[]>([]);
const selectedKampagneIdeeId = ref<string>();
const kampagnenIdeenModal = ref<ModalInstance | null>(null);
const modalOpen = ref<boolean>(true);
const isLoading = ref<boolean>(false);

async function loadKampagneIdeen() {
  isLoading.value = true;
  try {
    const response = await get<KampagneIdeeResource[]>('/kampagneIdeen');

    if (response.data?.data === undefined) {
      return;
    }

    kampagneIdeen.value = response.data.data;
  } catch {
    eventBus.emit('error', 'Bei dem Laden der Kampagnenideen ist ein Fehler aufgetreten');
  } finally {
    isLoading.value = false;
  }
}

onMounted(loadKampagneIdeen);

const sortedIdeen = computed(() => {
  return [...kampagneIdeen.value].sort((a, b) => {
    return a.attributes.isHighlighted === b.attributes.isHighlighted
      ? a.attributes.name.localeCompare(b.attributes.name)
      : a.attributes.isHighlighted
        ? -1
        : 1;
  });
});

const selectedKampagneIdee = computed(() => {
  return kampagneIdeen.value.find((idee) => idee.id === selectedKampagneIdeeId.value);
});

function fillStoreFromIdee() {
  kampagneCreateStore.titel = selectedKampagneIdee.value?.attributes.kampagneTitel ?? '';
  if (selectedKampagneIdee.value?.attributes.mailVorlageId === undefined) {
    kampagneCreateStore.versandart = Versandart.Brief;
  }

  kampagneCreateStore.kampagneIdee = selectedKampagneIdee.value ?? null;
  modalOpen.value = false;
}
</script>
