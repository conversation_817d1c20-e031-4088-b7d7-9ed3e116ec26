<template>
  <div class="flex grow flex-col gap-x-4 gap-y-3">
    <DsFormGroup
      label="Versenden im Namen von"
      class="w-fit"
      data-test="kampagne-create__step1__im-namen-von"
    >
      <DsRadioGroup
        v-model="kampagneCreateStore.imNamenVon"
        variant="button"
      >
        <DsRadioButton
          :value="ImNamenVon.Mir"
          data-test="kampagne-create__step1__im-namen-von__mir"
        >
          Mir (ich)
        </DsRadioButton>
        <DsRadioButton
          :value="ImNamenVon.ZustaendigerVermittler"
          data-test="kampagne-create__step1__im-namen-von__vermittler"
        >
          <PERSON><PERSON><PERSON>ndi<PERSON> Vermittler
        </DsRadioButton>
        <DsRadioButton
          :value="ImNamenVon.Benutzer"
          data-test="kampagne-create__step1__im-namen-von__benutzer"
        >
          <PERSON><PERSON><PERSON>
        </DsRadioButton>
      </DsRadioGroup>
    </DsFormGroup>
    <DsFormGroup
      v-if="kampagneCreateStore.imNamenVon === ImNamenVon.Benutzer"
      class="w-full"
      label="Absender"
      validation-name="attributes.senderExternalId"
      required
    >
      <DsSelect
        v-model="kampagneCreateStore.senderExternalId"
        :data="underlingsWithoutMe"
        :search-keys="['attributes.name', 'attributes.email']"
        value-key="attributes.externalId"
        data-test="kampagne-create__step1__im-namen-von__owner"
      >
        <template #entry="{entry}">
          {{ entry.attributes?.name }}
        </template>
      </DsSelect>
    </DsFormGroup>
  </div>
  <DsAlert
    v-if="kampagneCreateStore.empfaengers.size > 0"
    label="Durch die Änderung des Absenders werden etwaige Änderungen an den Empfänger-Anreden zurückgesetzt."
    type="warning"
    icon="exclamation-triangle"
  />
</template>

<script setup lang="ts">
import {
  DsAlert,
  DsFormGroup,
  DsRadioButton,
  DsRadioGroup,
  DsSelect,
} from '@demvsystems/design-components';
import { computed, onMounted } from 'vue';

import useCurrentUser from '@/components/users/useCurrentUser';
import useHierarchy from '@/components/users/useHierarchy';
import { useKampagneCreateStore } from '@/pages/kampagnen/stores/kampagneCreateStore';
import { ImNamenVon } from '@/pages/kampagnen/types';

const { underlings, loadUnderlingsAndParents } = useHierarchy();
const { user: currentUser } = useCurrentUser();

onMounted(async () => {
  await loadUnderlingsAndParents();
});

const kampagneCreateStore = useKampagneCreateStore();

const underlingsWithoutMe = computed(() => (
  underlings.value.filter((user) => user.id !== currentUser.value?.id)
));
</script>
