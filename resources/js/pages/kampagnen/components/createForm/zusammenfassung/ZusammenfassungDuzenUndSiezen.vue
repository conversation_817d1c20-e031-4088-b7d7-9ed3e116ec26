<template>
  <div class="space-y-4 py-5">
    <ZusammenfassungZeile
      v-if="kampagnenCreateStore.versandart !== Versandart.Brief"
      title="Betreff (Siezen)"
      data-test="kampagne-create__step4__formal-betreff"
      :on-click="onClickFormalBetreff"
    >
      <span
        class="w-full"
        v-html="sanitizedFormalBetreff"
      />
    </ZusammenfassungZeile>

    <ZusammenfassungZeile
      title="Inhalt (Siezen)"
      data-test="kampagne-create__step4__formal-content"
      :on-click="onClickFormalContent"
    >
      <div class="flex flex-col space-y-7">
        <div
          v-if="kampagnenCreateStore.formalContent !== null"
          class="w-full"
          v-html="sanitizedFormalContent"
        />
        <div class="space-x-2">
          <KampagneEntwurfPreviewModal
            :kampagne-id="kampagnenCreateStore.id"
            :content="kampagnenCreateStore.formalContent"
            :betreff="kampagnenCreateStore.formalBetreff"
            :versandart="kampagnenCreateStore.versandart"
            suppress-errors-and-retry
          />
        </div>
      </div>
    </ZusammenfassungZeile>
  </div>
  <div
    v-if="
      kampagnenCreateStore.informalContent !== null
        && kampagnenCreateStore.informalContent !== ''
    "
    class="space-y-5 py-5"
  >
    <ZusammenfassungZeile
      v-if="kampagnenCreateStore.versandart === Versandart.Mail"
      title="Betreff (Duzen)"
      data-test="kampagne-create__step4__informal-betreff"
      :on-click="onClickInformalBetreff"
    >
      <span
        class="w-full"
        v-html="sanitizedInformalBetreff"
      />
    </ZusammenfassungZeile>

    <ZusammenfassungZeile
      title="Inhalt (Duzen)"
      data-test="kampagne-create__step4__informal-content"
      :on-click="onClickInformalContent"
    >
      <div class="flex flex-col space-y-7">
        <div
          class="w-full"
          v-html="sanitizedInformalContent"
        />
        <div class="space-x-2">
          <KampagneEntwurfPreviewModal
            :kampagne-id="kampagnenCreateStore.id"
            :content="kampagnenCreateStore.informalContent"
            :betreff="kampagnenCreateStore.informalBetreff"
            :versandart="kampagnenCreateStore.versandart"
            informal
            suppress-errors-and-retry
          />
        </div>
      </div>
    </ZusammenfassungZeile>
  </div>
</template>

<script lang="ts" setup>
import DOMPurify from 'dompurify';
import { computed } from 'vue';
import { useRouter } from 'vue-router';

import { useKampagneCreateStore } from '@/pages/kampagnen/stores/kampagneCreateStore';
import { Versandart } from '@/pages/kampagnen/types';

import KampagneEntwurfPreviewModal from '../inhalt/KampagneEntwurfPreviewModal.vue';

import ZusammenfassungZeile from './ZusammenfassungZeile.vue';

const kampagnenCreateStore = useKampagneCreateStore();
const router = useRouter();

function sanitizeContent(content: string | null) {
  return computed(() =>
    DOMPurify.sanitize(content ?? '', { FORBID_TAGS: ['style'] }),
  );
}

const sanitizedFormalBetreff = sanitizeContent(kampagnenCreateStore.formalBetreff);
const sanitizedFormalContent = sanitizeContent(kampagnenCreateStore.formalContent);
const sanitizedInformalBetreff = sanitizeContent(kampagnenCreateStore.informalBetreff);
const sanitizedInformalContent = sanitizeContent(kampagnenCreateStore.informalContent);

async function navigateToInhalt(tab: 'duzen' | 'siezen' = 'siezen') {
  await router.push({
    name: 'kampagnen.edit.content',
    params: { id: kampagnenCreateStore.id },
    query: { tab },
  });
}

const onClickFormalBetreff = computed(() =>
  kampagnenCreateStore.versandart === Versandart.Mail
    ? () => navigateToInhalt()
    : undefined,
);

const onClickFormalContent = computed(() =>
  kampagnenCreateStore.versandart === Versandart.Brief
    ? () => navigateToInhalt()
    : undefined,
);

const onClickInformalBetreff = computed(() =>
  kampagnenCreateStore.versandart === Versandart.Mail
    ? () => navigateToInhalt('duzen')
    : undefined,
);

const onClickInformalContent = computed(() =>
  kampagnenCreateStore.versandart === Versandart.Brief
    ? () => navigateToInhalt('duzen')
    : undefined,
);
</script>
