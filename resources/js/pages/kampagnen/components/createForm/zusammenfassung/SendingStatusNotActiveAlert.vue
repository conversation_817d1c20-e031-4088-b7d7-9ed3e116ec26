<template>
  <DsAlert
    v-if="showError"
    type="error"
    icon="at"
    label="SMTP-Daten fehlen"
  >
    Um E-Mail-Kampagnen zu versenden, müssen Sie zunächst
    <a
      class="text-blue-700 underline"
      :href="userSettingsStore.pwMailSettingsUrl ?? undefined"
    >hier</a>
    Ihre SMTP-Daten hinterlegen
    oder sich mit Ihrem E-Mail-Anbieter anmelden.
  </DsAlert>

  <DsAlert
    v-else-if="showWarning"
    type="warning"
    icon="at"
    label="SMTP-Daten müssen hinterlegt sein"
  >
    Bitte stellen Sie sicher, dass alle Absender ihre SMTP-Daten in ihrem Konto
    <a
      class="text-blue-700 underline"
      :href="userSettingsStore.pwMailSettingsUrl ?? undefined"
    >hier</a>
    hinterlegt haben oder mit ihrem E-Mail-Anbieter angemeldet sind.
    Für User ohne eine funktionierende E-Mail-Verbindung werden keine Kampagnen-Mails verschickt.
  </DsAlert>
</template>

<script setup lang="ts">
import { DsAlert } from '@demvsystems/design-components';
import { computed, watch } from 'vue';

import { useKampagneCreateStore } from '@/pages/kampagnen/stores/kampagneCreateStore';
import { ImNamenVon, Versandart } from '@/pages/kampagnen/types';
import { useUserSettingsStore } from '@/pages/settings/stores/userSettingsStore';

const userSettingsStore = useUserSettingsStore();
const kampagneCreateStore = useKampagneCreateStore();

watch([
  () => kampagneCreateStore.id,
  () => kampagneCreateStore.imNamenVon,
  () => kampagneCreateStore.versandart,
], ([id, imNamenVon, versandart]) => {
  if (id === undefined) {
    return;
  }

  if (imNamenVon === ImNamenVon.Mir && versandart === Versandart.Mail) {
    void userSettingsStore.load();
  }
}, { immediate: true });

const showError = computed(() => {
  return kampagneCreateStore.imNamenVon === ImNamenVon.Mir
    && kampagneCreateStore.versandart === Versandart.Mail
    && !userSettingsStore.isMailSendingStatusActive;
});

const showWarning = computed(() => {
  return kampagneCreateStore.imNamenVon !== ImNamenVon.Mir
    && kampagneCreateStore.versandart === Versandart.Mail;
});
</script>
