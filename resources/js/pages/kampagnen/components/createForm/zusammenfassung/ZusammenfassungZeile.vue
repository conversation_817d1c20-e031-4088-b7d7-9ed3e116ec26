<template>
  <div class="flex w-full items-start justify-between space-x-12">
    <div class="h-full w-1/5 shrink-0 ">
      <h3 class="text-base font-semibold text-gray-800">
        {{ title }}
      </h3>
    </div>
    <div class="grow">
      <slot />
    </div>
    <DsButton
      v-if="onClick !== undefined"
      variant="outline"
      @click="onClick"
    >
      Bearbeiten
    </DsButton>
  </div>
</template>

<script setup lang="ts">
import { DsButton } from '@demvsystems/design-components';

defineProps<{
  title: string,
  onClick?: (() => void),
}>();
</script>
