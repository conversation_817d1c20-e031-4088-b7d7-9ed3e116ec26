<template>
  <DsModal
    :show="viewVorschauModal"
    size="md"
    anchor="top"
    custom-content
    hide-buttons
  >
    <div
      class="flex flex-col"
      style="max-height: calc(100vh - 7rem)"
    >
      <DetailContentPreview
        :cc="[]"
        :bcc="[]"
        :subject="previewBetreff"
        :empfaenger="empfaenger ?? []"
        :versandart="proxyVersandart"
        :preview-pdf="previewPdf"
        :content="previewContent"
        :suppress-errors-and-retry="suppressErrorsAndRetry"
        :absender="absender ?? []"
        class="-mx-4 -mt-4 overflow-auto p-6 sm:-mx-6 sm:-mt-6"
      />
      <footer class="-mx-6 flex items-center justify-end space-x-4 border-t px-6 pt-6">
        <DsButton
          size="lg"
          data-test="kampagne-message-preview-modal__close"
          @click="viewVorschauModal = false"
        >
          Schließen
        </DsButton>
      </footer>
    </div>
  </DsModal>
</template>

<script setup lang="ts">
import { DsButton, DsModal } from '@demvsystems/design-components';
import { computed } from 'vue';

import DetailContentPreview from '@/components/DetailContentPreview.vue';
import { Versandart } from '@/pages/kampagnen/types';
import { Versandart as VorlageVersandart } from '@/pages/vorlagen/types';
import { EmailElement } from '@/store/resources/types';

const props = defineProps<{
  previewBetreff?: string,
  previewContent?: string,
  previewPdf?: string,
  versandart: Versandart,
  suppressErrorsAndRetry: boolean,
  empfaenger?: EmailElement[],
  absender?: EmailElement[]
}>();

const viewVorschauModal = defineModel<boolean>({
  default: false,
});

const proxyVersandart = computed<VorlageVersandart>(() => {
  return props.versandart === Versandart.Brief ? VorlageVersandart.Brief : VorlageVersandart.Mail;
});
</script>
