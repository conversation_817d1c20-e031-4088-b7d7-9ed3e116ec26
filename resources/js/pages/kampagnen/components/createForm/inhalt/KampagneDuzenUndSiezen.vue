<template>
  <div class="space-y-3">
    <DsTabs v-model:active-tab="activeTab">
      <DsTab
        title="Sie-Anrede"
        tab-id="siezen"
      >
        <KampagneSubjectAndContent
          v-model:betreff="formalBetreffProxy"
          v-model:content="kampagneCreateStore.formalContent"
          :image-upload-handler="imageUploadHandler"
          append-to-label="(Siezen)"
          validation-name-betreff="attributes.formalBetreff"
          validation-name-content="attributes.formalContent"
          data-test="kampagnen__duzen-und-siezen__nachricht__formal"
          required
        >
          <div class="text-right">
            <KampagneEntwurfPreviewModal
              :kampagne-id="id"
              :content="kampagneCreateStore.formalContent"
              :betreff="formalBetreffProxy"
              :versandart="kampagneCreateStore.versandart"
              suppress-errors-and-retry
            />
          </div>
          <TextGeneration
            v-if="user !== undefined && user.attributes?.canUseTextGeneration"
            :content="kampagneCreateStore.formalContent ?? ''"
            :location="TextGenerationLocation.KampagneErstellen"
            :versandart="kampagneCreateStore.versandart"
            :anrede="Anrede.Siezen"
            :kampagne-idee-id="kampagneCreateStore.kampagneIdeeId"
            @update:content="kampagneCreateStore.formalContent = $event"
          />
        </KampagneSubjectAndContent>
      </DsTab>

      <DsTab
        title="Du-Anrede"
        tab-id="duzen"
      >
        <KampagneSubjectAndContent
          v-model:betreff="informalBetreffProxy"
          v-model:content="kampagneCreateStore.informalContent"
          :image-upload-handler="imageUploadHandler"
          append-to-label="(Duzen)"
          validation-name-betreff="attributes.informalBetreff"
          validation-name-content="attributes.informalContent"
          data-test="kampagnen__duzen-und-siezen__nachricht__informal"
        >
          <div class="text-right">
            <KampagneEntwurfPreviewModal
              :kampagne-id="id"
              :content="kampagneCreateStore.informalContent"
              :betreff="informalBetreffProxy"
              :versandart="kampagneCreateStore.versandart"
              informal
              suppress-errors-and-retry
            />
          </div>
          <TextGeneration
            v-if="user !== undefined && user.attributes?.canUseTextGeneration"
            :content="kampagneCreateStore.informalContent ?? ''"
            :location="TextGenerationLocation.KampagneErstellen"
            :versandart="kampagneCreateStore.versandart"
            :anrede="Anrede.Duzen"
            :kampagne-idee-id="kampagneCreateStore.kampagneIdeeId"
            @update:content="kampagneCreateStore.informalContent = $event"
          />
        </KampagneSubjectAndContent>
      </DsTab>
    </DsTabs>

    <DsAlert
      v-if="kampagneCreateStore.isContentTooLarge"
      label="Ihre Nachricht ist zu groß"
      type="error"
    >
      Ihre Nachricht ist zu groß und kann so leider nicht gespeichert werden.
      Momentan darf ihre Nachricht (Duzen + Siezen) <b>maximal 4MB</b> groß sein.
    </DsAlert>

    <DsAlert
      v-if="kampagneCreateStore.hasImagesAndIsBrief"
      label="Bilder werden in Briefen nicht unterstützt"
      type="error"
    >
      Bitte entfernen Sie alle Bilder aus Ihrer Nachricht.
    </DsAlert>
  </div>
</template>

<script setup lang="ts">
import { DsAlert, DsTab, DsTabs } from '@demvsystems/design-components';
import { storeToRefs } from 'pinia';
import { computed, onMounted, ref } from 'vue';

import TextGeneration from '@/components/textGeneration/TextGeneration.vue';
import { TextGenerationLocation } from '@/components/textGeneration/types';
import useCurrentUser from '@/components/users/useCurrentUser';
import { useImageUploadHandler } from '@/composables/useImageUploadHandler';
import { useKampagneCreateStore } from '@/pages/kampagnen/stores/kampagneCreateStore';
import { Versandart } from '@/pages/kampagnen/types';
import { Anrede } from '@/pages/vorgangAnlegen/types';

import KampagneEntwurfPreviewModal from './KampagneEntwurfPreviewModal.vue';
import KampagneSubjectAndContent from './KampagneSubjectAndContent.vue';

const props = defineProps<{
  tab: 'duzen' | 'siezen';
}>();

const kampagneCreateStore = useKampagneCreateStore();
const { id } = storeToRefs(kampagneCreateStore);

const activeTab = ref();
const hasBetreff = computed(() => kampagneCreateStore.versandart !== Versandart.Brief);

const { imageUploadHandler } = useImageUploadHandler(id, 'kampagnen');
const { user } = useCurrentUser();

function createBetreffProxy(betreffKey: 'formalBetreff' | 'informalBetreff') {
  return computed({
    get: () => hasBetreff.value ? (kampagneCreateStore[betreffKey] ?? '') : null,
    set: (betreff: string | null) => {
      kampagneCreateStore[betreffKey] = betreff;
    },
  });
}

const formalBetreffProxy = createBetreffProxy('formalBetreff');
const informalBetreffProxy = createBetreffProxy('informalBetreff');

onMounted(() => {
  activeTab.value = props.tab;
});
</script>
