<template>
  <ElTooltip
    :disabled="!isDisabled || isLoading"
    :content="disabledTooltipContent"
  >
    <DsButton
      variant="secondary"
      size="sm"
      data-test="kampagne-form__footer__preview"
      :disabled="isDisabled"
      :handler="openVorschauModal"
    >
      Zur Vorschau
    </DsButton>
  </ElTooltip>

  <DsModal
    :show="viewVorschauModal"
    size="md"
    anchor="top"
    custom-content
    hide-buttons
  >
    <div
      class="flex flex-col"
      style="max-height: calc(100vh - 7rem)"
    >
      <DetailContentPreview
        :cc="[]"
        :bcc="[]"
        :subject="previewBetreff"
        :empfaenger="previewEmpfaenger ?? []"
        :versandart="proxyVersandart"
        :preview-pdf="previewPdf"
        :content="previewContent"
        :suppress-errors-and-retry="suppressErrorsAndRetry"
        :absender="previewAbsender ?? []"
        class="-mx-4 -mt-4 overflow-auto p-6 sm:-mx-6 sm:-mt-6"
      />
      <footer class="-mx-6 flex items-center justify-end space-x-4 border-t px-6 pt-6">
        <DsButton
          size="lg"
          data-test="kampage-vorschau-modal__close"
          @click="viewVorschauModal = false"
        >
          Schließen
        </DsButton>
      </footer>
    </div>
  </DsModal>
</template>

<script setup lang="ts">
import { DsButton, DsModal } from '@demvsystems/design-components';
import { ElTooltip } from 'element-plus';
import { computed } from 'vue';

import DetailContentPreview from '@/components/DetailContentPreview.vue';
import { emptyHtmlToEmptyString } from '@/components/form/utils/emptyHtmlToEmptyString';
import { useKampagneEntwurfPreview } from '@/pages/kampagnen/composables/useKampagneEntwurfPreview';
import { Versandart } from '@/pages/kampagnen/types';
import { Versandart as VorlageVersandart } from '@/pages/vorlagen/types';

const props = defineProps<{
  versandart: Versandart,
  kampagneId?: string,
  content: string | null,
  betreff: string | null,
  suppressErrorsAndRetry: boolean,
  informal?: boolean
}>();

const proxyVersandart = computed<VorlageVersandart>(() => {
  return props.versandart === Versandart.Brief ? VorlageVersandart.Brief : VorlageVersandart.Mail;
});

const {
  viewVorschauModal,
  previewPdf,
  previewContent,
  previewBetreff,
  previewEmpfaenger,
  previewAbsender,
  isLoading,
  getPdf,
  getHtml,
} = useKampagneEntwurfPreview();

async function openVorschauModal() {
  if (props.kampagneId === undefined || props.content === null) {
    throw new Error('Preview without kampagne or content not possible');
  }

  if (props.versandart === Versandart.Brief) {
    await getPdf(props.kampagneId, props.content, props.informal);

    return;
  }

  if (props.betreff === null) {
    throw new Error('Email preview without subject not possible');
  }

  await getHtml(props.kampagneId, props.betreff, props.content, props.informal);
}

const isDisabled = computed(() => {
  const noContent = emptyHtmlToEmptyString(props.content) === '';
  const noBetreff = props.versandart === Versandart.Mail
      && emptyHtmlToEmptyString(props.betreff) === '';

  return noContent || noBetreff || isLoading.value;
});

const disabledTooltipContent = computed(() => {
  let content = 'Bitte tragen Sie einen Inhalt';

  if (props.versandart === Versandart.Mail) {
    content += ' und Betreff';
  }

  return content + ' ein';
});
</script>
