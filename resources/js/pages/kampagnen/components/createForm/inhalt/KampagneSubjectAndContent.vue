<template>
  <div class="space-y-4">
    <NachrichtSubject
      v-if="betreff !== null"
      v-model="betreff"
      :label="`Betreff ${appendToLabel}`"
      :required="required"
      :validation-name="validationNameBetreff"
      data-test="kampagne__nachricht__betreff"
    />

    <NachrichtContent
      v-model="content"
      :label="`Nachricht ${appendToLabel}`"
      :required="required"
      :validation-name="validationNameContent"
      :image-upload-handler="betreff === null ? undefined : imageUploadHandler"
      class="col-span-4"
      data-test="kampagne__nachricht__content"
    >
      <slot />
    </NachrichtContent>
  </div>
</template>

<script setup lang="ts">
import NachrichtContent from '@/components/form/NachrichtContent.vue';
import NachrichtSubject from '@/components/form/NachrichtSubject.vue';

const betreff = defineModel<string | null>('betreff', {
  required: true,
});
const content = defineModel<string | null>('content', {
  required: true,
});

withDefaults(defineProps<{
  appendToLabel: string,
  validationNameBetreff: string,
  validationNameContent: string,
  imageUploadHandler?: (files: File[]) => Promise<string>[],
  required?: boolean,
}>(), {
  imageUploadHandler: undefined,
});
</script>
