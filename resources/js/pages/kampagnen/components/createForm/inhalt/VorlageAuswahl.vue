<template>
  <DsFormGroup
    label="Vorlage Auswählen"
  >
    <div class="space-y-y flex flex-col sm:flex-row sm:space-x-3 sm:space-y-0">
      <DsSelect
        v-model="vorlageId"
        :data="vorlagen"
        :search-keys="['attributes.name']"
        :item-height="28"
        :is-loading="isLoading"
        class="grow"
        value-key="id"
        group-key="attributes.type"
        data-test="kampagne-create__step3__vorlage-select"
        virtualized
      >
        <template #entry="{entry}">
          <div
            class="truncate"
            :title="entry.attributes?.titel"
          >
            {{ entry.attributes?.name }}
          </div>
        </template>
      </DsSelect>

      <DsButton
        href="/vorlagen"
        variant="secondary"
        external
      >
        Vorlagen verwalten
      </DsButton>
    </div>
  </DsFormGroup>
</template>

<script setup lang="ts">
import { DsButton, DsFormGroup, DsSelect } from '@demvsystems/design-components';
import { computed, onMounted, ref, watch } from 'vue';

import { useVorlagenList } from '@/composables/useVorlagenList';
import { useKampagneCreateStore } from '@/pages/kampagnen/stores/kampagneCreateStore';
import { Versandart } from '@/pages/kampagnen/types';

const kampagneCreateStore = useKampagneCreateStore();

const vorlageId = ref<string | null>(null);

const vorlageKampagneType = computed(() => {
  switch (kampagneCreateStore.versandart) {
    case Versandart.Brief:
      return 'vorlagenKampagneBrief';
    case Versandart.Mail:
      return 'vorlagenKampagneMail';
    default:
      throw new Error('Versandart not supported');
  }
});

const {
  loadVorlagenAndIncludedVorlagen,
  vorlagen,
  vorlagenKampagneBrief,
  vorlagenKampagneMail,
  isLoading,
} = useVorlagenList({
  vorlagenType: vorlageKampagneType,
});

onMounted(async () => {
  await loadVorlagenAndIncludedVorlagen();
});

watch(() => kampagneCreateStore.kampagneIdee, (idee) => {
  if (idee === null) {
    return;
  }

  if (
    kampagneCreateStore.formalBetreff !== null || kampagneCreateStore.informalBetreff !== null
    || kampagneCreateStore.formalContent !== null || kampagneCreateStore.informalContent !== null
  ) {
    return;
  }

  if (
    kampagneCreateStore.versandart === Versandart.Brief
    && idee.attributes.briefVorlageId !== null
  ) {
    vorlageId.value = idee.attributes.briefVorlageId;
  } else if (idee.attributes.mailVorlageId !== null) {
    vorlageId.value = idee.attributes.mailVorlageId;
  }
}, { immediate: true });

// if vorlage id changes and vorlagen are loaded, set vorlage content / betreff
watch([vorlageId, isLoading], () => {
  const kampagneVorlageId = vorlagen.value.find(
    (vorlage) => vorlage.id === vorlageId.value,
  )?.relationships?.vorlage?.data?.id;

  if (kampagneVorlageId === undefined) {
    return;
  }

  let selected;
  if (kampagneCreateStore.versandart === Versandart.Brief) {
    selected = vorlagenKampagneBrief.value.find((item) => item.id === kampagneVorlageId);
  } else {
    selected = vorlagenKampagneMail.value.find((item) => item.id === kampagneVorlageId);
    kampagneCreateStore.formalBetreff = selected?.attributes.formalSubject ?? null;
    kampagneCreateStore.informalBetreff = selected?.attributes.informalSubject ?? null;
    kampagneCreateStore.attachments = selected?.attributes.attachments ?? [];
  }

  kampagneCreateStore.formalContent = selected?.attributes.formalContent ?? null;
  kampagneCreateStore.informalContent = selected?.attributes.informalContent ?? null;
}, { immediate: true });
</script>
