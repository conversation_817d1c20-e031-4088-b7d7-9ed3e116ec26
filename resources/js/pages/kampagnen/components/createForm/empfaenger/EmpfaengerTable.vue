<template>
  <DsFormGroup
    validation-name="attributes.empfaengers"
  >
    <DsTable
      v-model="selectedEmpfaengerIds"
      :columns="empfaengerColumns"
      :data="rows"
      :sort-settings="{
        name: 'name',
        order: SortOrder.Ascending,
      }"
      :is-loading="isLoading"
      no-data-text="<PERSON>lick<PERSON> Si<PERSON> oben, um Empfänger hinzuzufügen"
      condensed
      virtualized
      style="max-height: calc(100vh - 31rem)"
    >
      <template #entry-name="{entry}">
        <NameTableCell
          :is-loading="isRefreshingKundeLookup.has(entry.id)"
          :name="entry.name"
          :url="entry.url"
          @click-link="refreshKundeAfterOpeningTabAgain(entry.id)"
        />
      </template>

      <template #entry-hasAddress="{value, entry}">
        <HasAddressTableCell
          :has-address="value"
          :is-loading="isLoadingMissingAddresses || isRefreshingKundeLookup.has(entry.id)"
        />
      </template>

      <template #entry-email="{value, entry}">
        <EmailTableCell
          :email="value"
          :is-loading="isRefreshingKundeLookup.has(entry.id)"
        />
      </template>

      <template #entry-informal="{entry}">
        <DsSwitch
          :model-value="entry.informal"
          @update:model-value="kampagneCreateStore.setEmpfaengersInformal([entry.id], $event)"
        />
      </template>
    </DsTable>
  </DsFormGroup>
</template>

<script setup lang="ts">
import {
  Column,
  DsFormGroup,
  DsSwitch,
  DsTable,
  SortOrder,
} from '@demvsystems/design-components';
import { storeToRefs } from 'pinia';
import { computed } from 'vue';

import EmailTableCell from '@/pages/kampagnen/components/kundenTable/EmailTableCell.vue';
import HasAddressTableCell from '@/pages/kampagnen/components/kundenTable/HasAddressTableCell.vue';
import NameTableCell from '@/pages/kampagnen/components/kundenTable/NameTableCell.vue';
import { useKundenTable } from '@/pages/kampagnen/composables/kunden/useKundenTable';
import { useRefreshingKunde } from '@/pages/kampagnen/composables/kunden/useRefreshingKunde';
import { createBooleanFilter } from '@/pages/kampagnen/utils/createBooleanFilter';
import { KundeResource } from '@/store/resources/types';

import { useKampagneCreateStore } from '../../../stores/kampagneCreateStore';

const selectedEmpfaengerIds = defineModel<string[]>('selectedEmpfaengerIds', {
  required: true,
});

defineProps<{
  isLoading: boolean;
}>();

const kampagneCreateStore = useKampagneCreateStore();
const {
  empfaengers,
} = storeToRefs(kampagneCreateStore);

const empfaengerArray = computed(() => [...empfaengers.value.values()]);

const {
  columns,
  rows,
  isLoading: isLoadingMissingAddresses,
} = useKundenTable(empfaengerArray, { withFilters: true });

function handleRefreshedKunde(refreshedKunde: KundeResource) {
  void kampagneCreateStore.addKundenToEmpfaengers([refreshedKunde]);
}

const {
  isRefreshingKundeLookup,
  refreshKundeAfterOpeningTabAgain,
} = useRefreshingKunde(handleRefreshedKunde);

const empfaengerColumns = computed<Record<string, Column>>(() => {
  return {
    ...columns.value,
    informal: {
      label: 'Duzen',
      width: 130,
      format: {
        classes: ['justify-end'],
        filter: createBooleanFilter({
          label: 'Duzen',
          filter: (row: Record<string, unknown>) => row.informal === true,
        }, {
          label: 'Siezen',
          filter: (row: Record<string, unknown>) => row.informal === false,
        }),
      },
    },
  };
});
</script>
