<template>
  <div
    v-if="selectedEmpfaengerIds.length > 0"
  >
    <div class="flex space-x-2">
      <DsButton
        title="Empfänger ausschließen"
        icon="user-minus"
        variant="outline"
        @click="actionExcludeEmpfaengerPrompt?.open()"
      />
      <DsDropdown
        :items="empfaengerActions"
        :width="250"
        item-class="px-2 py-1 text-gray-800"
        placement="bottom-end"
        title="Weitere Aktionen"
        unfocusable
        @select="handleDropdownSelect"
      >
        <template #button>
          <DsButton
            icon="ellipsis-vertical"
            variant="outline"
          />
        </template>
        <template #item="{item}">
          {{ item.text }}
        </template>
      </DsDropdown>
    </div>

    <DsModal
      ref="actionExcludeEmpfaengerPrompt"
      :variant="ModalVariant.Warning"
      icon="exclamation-triangle"
      title="Sind Sie sicher?"
      confirm-label="Ja, Empfänger ausschließen"
      @confirm="excludeSelectedEmpfaengers"
    >
      Die gewählten Empfänger werden von dieser Kampagne ausgeschlossen.
    </DsModal>
  </div>
</template>

<script setup lang="ts">
import {
  DsButton,
  DsDropdown,
  DsModal,
  ModalInstance,
  ModalVariant,
} from '@demvsystems/design-components';
import { computed, ref } from 'vue';

import { useKampagneCreateStore } from '@/pages/kampagnen/stores/kampagneCreateStore';
import { EmpfaengerAction } from '@/pages/kampagnen/types';

const selectedEmpfaengerIds = defineModel<string[]>('selectedEmpfaengerIds', {
  required: true,
});

const kampagneCreateStore = useKampagneCreateStore();

const actionExcludeEmpfaengerPrompt = ref<ModalInstance | null>(null);

function excludeSelectedEmpfaengers() {
  void kampagneCreateStore.removeEmpfaengers(selectedEmpfaengerIds.value);

  selectedEmpfaengerIds.value = [];
}

const empfaengerActions = computed<EmpfaengerAction[]>(() => [
  {
    text: `${selectedEmpfaengerIds.value.length} Empfänger duzen`,
    handler: () => (
      void kampagneCreateStore.setEmpfaengersInformal(selectedEmpfaengerIds.value, true)
    ),
  },
  {
    text: `${selectedEmpfaengerIds.value.length} Empfänger siezen`,
    handler: () => (
      void kampagneCreateStore.setEmpfaengersInformal(selectedEmpfaengerIds.value, false)
    ),
  },
]);

const handleDropdownSelect = (index: number) => {
  empfaengerActions.value[index]?.handler();
};
</script>
