<template>
  <div class="flex w-full flex-col gap-x-4 gap-y-3 xl:flex-row">
    <FileUpload
      class="w-full xl:w-1/2"
      :uploaded-files="uploadedFiles"
      @update:uploaded-files="updateUploadedFiles($event)"
    />
    <DsFormGroup
      class="w-full xl:w-1/2"
      label="Automatischer Dateianhang"
      validation-name="files.size"
    >
      <DsMultiselect
        v-model="attachmentsProxy"
        :options="attachmentOptions"
        object-as-value
      />
    </DsFormGroup>
  </div>

  <FileSizeAlerts
    :uploaded-files="uploadedFiles"
  />
</template>

<script setup lang="ts">
import { DsFormGroup, DsMultiselect } from '@demvsystems/design-components';
import { storeToRefs } from 'pinia';

import FileSizeAlerts from '@/components/form/FileSizeAlerts.vue';
import FileUpload from '@/components/formBasisInfo/fileUpload/FileUpload.vue';
import { Attachment, attachmentLabels, UploadedFileResource } from '@/store/resources/types';
import { getMultiselectOptions, makeMultiselectItemProxy } from '@/utils/multiselectEnumHelper';

import { useKampagneCreateStore } from '../stores/kampagneCreateStore';

const kampagnenCreateStore = useKampagneCreateStore();
const { attachments, uploadedFiles } = storeToRefs(kampagnenCreateStore);

const attachmentsProxy = makeMultiselectItemProxy(attachments, attachmentLabels);

const attachmentOptions = getMultiselectOptions(Attachment, attachmentLabels);

function updateUploadedFiles(files: UploadedFileResource[]) {
  kampagnenCreateStore.uploadedFiles = files;
}
</script>
