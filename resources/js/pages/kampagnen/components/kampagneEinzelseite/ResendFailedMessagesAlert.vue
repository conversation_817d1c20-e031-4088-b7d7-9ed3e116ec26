<template>
  <DsAlert
    type="error"
    icon="at"
    label="Der E-Mail-Versand an einige Empfänger ist fehlgeschlagen"
  >
    <p v-if="isKampagneTooOldForResend">
      E-Mails können nur innerhalb von 7 Tagen nach Kampagnenversand erneut versendet werden.
    </p>

    <p v-else-if="!sendingFinished">
      Sie können den Versand hier neu versuchen, sobald sich keine E-Mails mehr im Versand befinden.
    </p>

    <p v-else-if="retryAfterFormatted !== null">
      Sie können den Versand <b>in {{ retryAfterFormatted }}</b> erneut versuchen.
    </p>

    <template v-else>
      Sie können den Versand erneut versuchen.
      <div class="mt-2">
        <DsButton
          size="sm"
          variant="danger-outline"
          :handler="resendFailedMessages"
        >
          Versand erneut versuchen
        </DsButton>
      </div>
    </template>
  </DsAlert>
</template>

<script setup lang="ts">
import { DsAlert, DsButton } from '@demvsystems/design-components';
import { StorageSerializers, useLocalStorage } from '@vueuse/core';
import { addSeconds, formatDistanceToNowStrict, isBefore, isPast } from 'date-fns';
import { de } from 'date-fns/locale';
import subDays from 'date-fns/subDays';
import { computed, onUnmounted, ref, watch } from 'vue';

import { eventBus } from '@/store/resources/store';
import { KampagneResource } from '@/store/resources/types';

const props = defineProps<{
  kampagne: KampagneResource,
  sendingFinished: boolean,
}>();

const retryDate = useLocalStorage<Date | null>(`retry-date-${props.kampagne.id}`, null, {
  serializer: StorageSerializers.date,
});
const retryAfterFormatted = ref<string | null>(null);
const dateInterval = ref<ReturnType<typeof setInterval> | null>(null);

const isKampagneTooOldForResend = computed(() => {
  const versendetAt = props.kampagne.attributes.versendetAt ?? null;
  if (versendetAt === null) {
    throw new Error('Kampagne has no versendetAt date');
  }

  return isBefore(new Date(versendetAt), subDays(new Date(), 7));
});

async function resendFailedMessages() {
  try {
    const response = await fetch(`/api/kampagnen/${props.kampagne.id}/messages/resendFailed`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (response.ok || response.status === 429) {
      // wait 5s to give the server time to send first msg
      await new Promise((resolve) => setTimeout(resolve, 5000));

      const retryAfter = response.headers.get('Retry-After');

      if (retryAfter !== null) {
        retryDate.value = addSeconds(new Date(), parseInt(retryAfter, 10));
      }

      window.location.reload();

      return;
    }

    eventBus.emit('error');
  } catch (e) {
    eventBus.emit('error');
  }
}

function updateRetryAfter() {
  if (retryDate.value === null) {
    return;
  }

  if (isPast(retryDate.value)) {
    retryDate.value = null;
    retryAfterFormatted.value = null;
    clearInterval(dateInterval.value ?? undefined);

    return;
  }

  retryAfterFormatted.value = formatDistanceToNowStrict(retryDate.value, { locale: de });
}

watch(() => retryDate.value, () => {
  if (retryDate.value === null) {
    return;
  }

  updateRetryAfter();
  dateInterval.value = setInterval(updateRetryAfter, 1000);
}, { immediate: true });

onUnmounted(() => {
  clearInterval(dateInterval.value ?? undefined);
});
</script>
