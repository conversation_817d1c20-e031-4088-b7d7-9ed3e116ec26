<template>
  <DsTable
    data-test="kampagne-einzelseite__messages-table"
    class="overflow-hidden rounded-md border border-gray-200"
    item-class="border-b"
    :data="rows"
    :columns="columns"
    virtualized
    :item-height="48"
    :is-loading="isLoadingMessages"
  >
    <template #entry-address="{value}">
      <span
        v-if="value === null"
        class="space-x-1 text-red-500"
      >
        <DsIcon name="exclamation-triangle" />
        <span>Adresse fehlt</span>
      </span>
      <template v-else>
        {{ formatAddress(value) }}
      </template>
    </template>

    <template #entry-email="{value}">
      <span
        v-if="value === '' || value === null "
        class="space-x-1 text-red-500"
      >
        <DsIcon name="exclamation-triangle" />
        <span>E-Mail fehlt</span>
      </span>
      <template v-else>
        {{ value }}
      </template>
    </template>
    <template #entry-status="{entry}">
      <DsBadge
        :type="entry.status.variant"
      >
        {{ entry.status.label }}
      </DsBadge>
    </template>
    <template #entry-aktionen="{entry}">
      <div
        v-if="entry.status !== KampagneMessageStatus.Fehler"
        class="space-x-2"
      >
        <DsButton
          v-if="entry.vorgangLink"
          size="sm"
          variant="clear"
          title="Zum Vorgang"
          icon="inbox"
          :href="entry.vorgangLink"
        />
        <DsButton
          variant="clear"
          size="sm"
          data-test="kampagne-message-preview-modal__open"
          icon="eye"
          title="Details"
          :disabled="isLoadingPreview"
          @click="emit('openVorschauModal', entry.messageId, entry.emailId)"
        />
        <DsButton
          v-if="versandart === Versandart.Brief"
          variant="clear"
          size="sm"
          data-test="kampagne-message-download-pdf"
          icon="download"
          title="PDF herunterladen"
          :href="getPdfDownloadUrl(entry.messageId)"
        />
      </div>
    </template>
  </DsTable>
</template>

<script setup lang="ts">
import {
  Column,
  createEnumFilter,
  createSearchFilter,
  DsBadge,
  DsButton,
  DsIcon,
  DsTable,
} from '@demvsystems/design-components';
import { computed } from 'vue';

import { useLoadKampagneData } from '@/pages/kampagnen/composables/useLoadKampagneData';
import {
  KampagneMessageAdresse,
  KampagneMessageStatus,
  StatusColumn,
  Versandart,
} from '@/pages/kampagnen/types';
import { createBooleanFilter } from '@/pages/kampagnen/utils/createBooleanFilter';
import { KampagneMessageResource } from '@/store/resources/types';

const props = defineProps<{
  isLoadingPreview: boolean,
  isLoadingMessages: boolean,
  kampagneId: string,
  versandart: Versandart,
  kampagneMessages: KampagneMessageResource[],
}>();

const emit = defineEmits<{
  (event: 'openVorschauModal', messageId: string, emailId: string): void,
}>();

const { getPdfDownloadUrl, kampagneId: toKampagneId } = useLoadKampagneData();

toKampagneId.value = props.kampagneId;

const columns = computed<Record<string, Column>>(() => {
  const versandartColumn: Record<string, Column> = (
    props.versandart === Versandart.Mail
      ? {
        email: {
          label: 'E-Mail-Adresse',
          format: {
            filter: createSearchFilter(['email']),
          },
        },
        status: {
          label: 'Status',
          format: {
            sort(a: Record<string, StatusColumn>, b: Record<string, StatusColumn>): number {
              return a.status?.label.localeCompare(b.status?.label ?? '') ?? 0;
            },
            filter: {
              ...createEnumFilter(['Versandbereit', 'Wird versendet', 'Abgeschlossen', 'Fehler']),
              createFilter(entry: Record<string, string[]>, key: string) {
                return (row: Record<string, unknown>) => {
                  const value = row[key] as StatusColumn;

                  return entry.selected?.includes(value.label) ?? false;
                };
              },
            },
          },
        },
      }
      : {
        address: {
          label: 'Adresse',
          format: {
            filter: createSearchFilter(['address']),
          },
        },
      }
  );

  return {
    name: {
      label: 'Name',
      format: {
        filter: createSearchFilter(['name']),
      },
    },
    ...versandartColumn,
    anrede: {
      label: 'Anrede',
      width: 130,
      format: {
        filter: createBooleanFilter({
          label: 'Duzen',
          filter: (row: Record<string, unknown>) => row.anrede === 'Duzend',
        }, {
          label: 'Siezen',
          filter: (row: Record<string, unknown>) => row.anrede === 'Siezend',
        }),
      },
    },
    aktionen: {
      label: 'Aktionen',
      width: props.versandart === Versandart.Mail ? 100 : 130,
      notSortable: true,
      format: {
        classes: ['justify-end'],
      },
    },
  };
});

function formatAddress({ ort, plz, strasse }: KampagneMessageAdresse): string {
  return `${ort}, ${plz} ${strasse}`;
}

function badgeKampagneMessageStatus(status?: KampagneMessageStatus): StatusColumn {
  switch (status) {
    case KampagneMessageStatus.Versandbereit:
      return { label: 'Versandbereit', variant: 'primary' };
    case KampagneMessageStatus.InVersand:
      return { label: 'Wird versendet', variant: 'default' };
    case KampagneMessageStatus.Abgeschlossen:
      return { label: 'Abgeschlossen', variant: 'success' };
    case KampagneMessageStatus.Fehler:
      return { label: 'Fehler', variant: 'error' };
    default:
      return { label: 'Unbekannt', variant: 'default' };
  }
}

const rows = computed(() => {
  return props.kampagneMessages.map((message: KampagneMessageResource) => ({
    messageId: message.id,
    emailId: Array.isArray(message.relationships?.emailMeta?.data)
      ? undefined
      : message.relationships?.emailMeta?.data?.id,
    name: message.attributes.name,
    email: message.attributes.email,
    address: message.attributes.adresse,
    anrede: message.attributes.informal ? 'Duzend' : 'Siezend',
    status: badgeKampagneMessageStatus(message.attributes.status),
    vorgangLink: message.links?.vorgang,
  }));
});
</script>
