<template>
  <DsButton
    :disabled="disabled"
    @click="confirmSend"
  >
    {{ kampagneCreateStore.versandart === Versandart.Mail ? "Senden / Versand planen" : "Senden" }}
  </DsButton>
  <DsModal
    ref="sendModal"
    size="sm"
    icon="question"
    title="Kampagne versenden?"
    :variant="ModalVariant.Info"
    cancel-label="Nein"
    :confirm-label="kampagneCreateStore.geplantAt !== null ? 'Versand planen' : 'Kampagne senden'"
    :confirm-disabled="empfaengersWithAddress === 0"
  >
    <ElForm
      class="space-y-3"
      @submit.prevent
    >
      <p v-if="empfaengersWithoutAddress !== 0">
        <b>Achtung!</b><br>
        <b>Bei {{ empfaengersWithoutAddress }} von {{ kampagneCreateStore.empfaengers.size }}</b>
        der ausgewählten Empfänger <b>ist keine {{ isEmailKampagne ? 'E-Mail-' : '' }}Adresse hinterlegt!</b>
        Der {{ isEmailKampagne ? 'E-Mail-' : '' }}Versand ist nur an vorhandene,
        valide {{ isEmailKampagne ? 'E-Mail-' : '' }}Adressen möglich.
      </p>
      <div
        v-if="empfaengersWithAddress !== 0"
        class="flex flex-col gap-y-2"
      >
        <p>
          Möchten Sie die Kampagne <b> an {{ empfaengersWithAddress }} Empfänger versenden? </b>
        </p>
        <p v-if="kampagneCreateStore.versandart === Versandart.Mail">
          Alternativ können Sie einen Versandzeitpunkt festlegen,
          um die Kampagne zu einem späteren Zeitpunkt zu verschicken.
        </p>

        <template
          v-if="kampagneCreateStore.versandart === Versandart.Mail"
        >
          <ElSwitch
            v-model="showTime"
            data-test="kampagne-create__step4__show-time"
            active-text="Versandzeitpunkt festlegen"
          />
          <ElFormItem
            v-if="showTime"
            :error="geplantAtErrors"
          >
            <ElDatePicker
              v-model="geplantAtProxy"
              data-test="kampagne-create__step4__geplant-at"
              type="datetime"
              format="DD.MM.YYYY HH:mm"
              :disabled-date="disabledDate"
              :default-time="defaultTime"
              :show-now="false"
            />
          </ElFormItem>
        </template>
        <p v-if="kampagneCreateStore.geplantAt !== null">
          Die Kampagne wird <b> am {{ format(kampagneCreateStore.geplantAt, "dd.MM.yyyy 'um' HH:mm 'Uhr'") }} </b> verschickt.
        </p>
      </div>
      <p v-else>
        <b>Es gibt keine weiteren Empfänger mit hinterlegter {{ isEmailKampagne ? 'E-Mail-' : '' }}Adresse</b>,
        an die versendet werden könnte. Bitte überarbeiten Sie Ihre Empfängerliste.
      </p>
    </ElForm>
  </DsModal>
</template>

<script setup lang="ts">
import { DsButton, DsModal, ModalInstance, ModalVariant } from '@demvsystems/design-components';
import format from 'date-fns/format';
import { ElDatePicker, ElForm, ElFormItem, ElSwitch } from 'element-plus';
import { computed, ref, watch } from 'vue';

import { useKundenTable } from '@/pages/kampagnen/composables/kunden/useKundenTable';
import { useKampagneCreateStore } from '@/pages/kampagnen/stores/kampagneCreateStore';
import { Versandart } from '@/pages/kampagnen/types';

const props = defineProps<{
  sendKampagne: () => Promise<void>;
  disabled: boolean;
}>();

const kampagneCreateStore = useKampagneCreateStore();
const { missingAddressKundenIds } = useKundenTable();
const isEmailKampagne = computed(() => kampagneCreateStore.versandart === Versandart.Mail);
const sendModal = ref<ModalInstance | null>(null);
const showTime = ref<boolean>(false);

const geplantAtProxy = computed({
  get: () => kampagneCreateStore.geplantAt || new Date(),
  set: (value) => {
    kampagneCreateStore.geplantAt = value;
  },
});

const disabledDate = computed<(date: Date) => boolean>(
  () => (date: Date) => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    return (date < today);
  },
);

const defaultTime = computed(() => {
  const now = new Date();
  const twoHours = 2 * 60 * 60 * 1000;

  return new Date(now.getTime() + twoHours);
});

const geplantAtErrors = computed(() => {
  const errors = kampagneCreateStore.errors['attributes.geplantAt'];

  if (errors === undefined || errors.length === 0) {
    return undefined;
  }

  return errors.join(' ');
});

const empfaengersWithoutAddress = computed(() => {
  if (isEmailKampagne.value) {
    return Array.from(kampagneCreateStore.empfaengers.values()).filter(
      (empf) => empf.attributes.email === '' || empf.attributes.email === null,
    ).length;
  }

  return Array.from(kampagneCreateStore.empfaengers.values()).filter(
    (empf) => missingAddressKundenIds.value.has(empf.attributes.externalId),
  ).length;
});

const empfaengersWithAddress = computed(() => (
  kampagneCreateStore.empfaengers.size - empfaengersWithoutAddress.value
));

function confirmSend() {
  void sendModal.value?.open({
    confirmed: props.sendKampagne,
  });
}

watch(showTime, (newValue) => {
  kampagneCreateStore.errors = {};
  kampagneCreateStore.geplantAt = newValue ? defaultTime.value : null;
});
</script>
