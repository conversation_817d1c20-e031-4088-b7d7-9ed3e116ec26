<template>
  <DsSkeleton
    v-if="isLoading"
    class="h-4 w-36"
  />
  <span
    v-else-if="email === '' || email === null "
    class="space-x-1 text-red-500"
  >
    <DsIcon name="exclamation-triangle" />
    <span>E-Mail fehlt</span>
  </span>
  <template v-else>
    {{ email }}
  </template>
</template>

<script setup lang="ts">
import { DsIcon, DsSkeleton } from '@demvsystems/design-components';

defineProps<{
  email: string | null,
  isLoading?: boolean,
}>();
</script>
