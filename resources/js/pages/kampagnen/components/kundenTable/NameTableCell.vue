<template>
  <DsSkeleton
    v-if="isLoading"
    class="h-4 w-36"
  />
  <div
    v-else
    class="flex space-x-3"
  >
    <a
      :href="url"
      target="_blank"
      rel="noopener noreferrer"
      class="space-x-1.5 text-blue-500 hover:underline"
      @click="$emit('clickLink')"
      @mousedown.middle="$emit('clickLink')"
    >
      <span>
        {{ name }}
      </span>
      <DsIcon name="up-right-from-square" />
    </a>
  </div>
</template>

<script setup lang="ts">
import { DsIcon, DsSkeleton } from '@demvsystems/design-components';

defineProps<{
  name: string,
  url: string | undefined,
  isLoading?: boolean,
}>();

defineEmits<{
  clickLink: [],
}>();
</script>
