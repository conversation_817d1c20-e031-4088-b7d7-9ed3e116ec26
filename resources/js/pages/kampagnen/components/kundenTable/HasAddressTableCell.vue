<template>
  <DsSkeleton
    v-if="isLoading"
    class="h-4 w-36"
  />
  <span
    v-else-if="hasAddress"
    class="space-x-1 text-green-700"
  >
    <DsIcon name="check" />
    <span>Adresse vorhanden</span>
  </span>
  <span
    v-else-if="hasAddress === undefined"
    class="space-x-1 text-red-500"
  >
    <DsIcon name="exclamation-triangle" />
    <span>Fehler bei Datenabruf</span>
  </span>
  <span
    v-else
    class="space-x-1 text-red-500"
  >
    <DsIcon name="exclamation-triangle" />
    <span>Adresse fehlt / unvollständig</span>
  </span>
</template>

<script setup lang="ts">
import { DsIcon, DsSkeleton } from '@demvsystems/design-components';

defineProps<{
  hasAddress: boolean | undefined,
  isLoading?: boolean,
}>();
</script>
