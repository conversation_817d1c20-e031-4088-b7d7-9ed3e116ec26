<template>
  <DsButton
    data-test="kampagne-uebersicht__delete-button"
    icon="trash"
    variant="clear"
    size="sm"
    title="Kampagne löschen"
    :disabled="isLoading"
    @click="confirmDelete"
  />
  <DsModal
    ref="confirmationDeleteModal"
    :variant="ModalVariant.Error"
    title="Ausgewählte Kampagne löschen?"
    icon="trash"
    confirm-label="Ja, Kampagne löschen"
    cancel-label="Nein, Abbrechen"
  >
    <div class="flex min-w-0 flex-col space-y-2">
      <p>
        Sind Sie sicher, dass Sie diese Kampagne löschen möchten?
        Diese Aktion kann nicht mehr rückgängig gemacht werden.
      </p>
    </div>
  </DsModal>
</template>

<script lang="ts" setup>
import {
  DsButton,
  DsModal,
  ModalInstance,
  ModalVariant,
} from '@demvsystems/design-components';
import { ref } from 'vue';

import { useDeleteKampagne } from '@/pages/kampagnen/composables/kampagneCreate/useDeleteKampagne';
import { useLoadKampagnenList } from '@/pages/kampagnen/composables/useLoadKampagnenList';
import { eventBus } from '@/store/resources/store';

const props = defineProps<{
  id: string,
}>();

const { loadKampagnen } = useLoadKampagnenList();
const { deleteKampagne, isLoading } = useDeleteKampagne();
const confirmationDeleteModal = ref<ModalInstance | null>(null);

async function deleteKampagneAndLoadKampagnen() {
  await deleteKampagne(props.id);
  await loadKampagnen();
  eventBus.emit('kampagneGeloescht');
}

function confirmDelete() {
  void confirmationDeleteModal.value?.open({
    confirmed: deleteKampagneAndLoadKampagnen,
  });
}
</script>
