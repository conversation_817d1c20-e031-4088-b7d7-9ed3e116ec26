<template>
  <DsButton
    icon="search"
    data-test="erweiterte-kundensuche__open"
    @click="openModal"
  >
    Empfängerliste hinzufügen
  </DsButton>
  <DsModal
    :show="show"
    anchor="top"
    hide-buttons
    custom-content
    size="md"
  >
    <div
      class="flex flex-col"
      style="max-height: calc(100vh - 7rem)"
    >
      <header class="z-50 -mx-6 flex items-center space-x-3 px-6 pb-6 shadow">
        <h2 class="grow text-lg font-bold leading-none tracking-wide">
          Erweiterte Kundensuche
        </h2>
        <DsButton
          variant="secondary"
          icon="xmark"
          @click="closeModal"
        />
      </header>

      <ErweiterteKundensucheForm
        v-if="step === 1"
      />
      <ErweiterteKundensucheResults
        v-else
      />

      <footer class="-mx-6 flex items-center justify-end space-x-4 border-t px-6 pt-6">
        <template v-if="step === 1">
          <DsButton
            variant="danger"
            class="mr-auto"
            icon="eraser"
            data-test="erweiterte-kundensuche__formular-reset"
            @click="erweiterteKundensucheStore.reset()"
          >
            Zurücksetzen
          </DsButton>
          <DsButton
            variant="secondary"
            data-test="erweiterte-kundensuche__cancel"
            @click="closeModal"
          >
            Abbrechen
          </DsButton>
          <DsButton
            data-test="erweiterte-kundensuche__search"
            :handler="fetchResults"
          >
            Suchen
          </DsButton>
        </template>
        <template v-else>
          <DsButton
            variant="secondary"
            data-test="erweiterte-kundensuche__back"
            @click="step = 1"
          >
            Zurück
          </DsButton>
          <DsButton
            data-test="erweiterte-kundensuche__add-results"
            :disabled="isAddingKundenDisabled"
            @click="emitResults"
          >
            {{ erweiterteKundensucheStore.foundKunden.length }} Empfänger hinzufügen
          </DsButton>
        </template>
      </footer>
    </div>
  </DsModal>
</template>

<script setup lang="ts">
import { DsButton, DsModal } from '@demvsystems/design-components';
import { computed, ref } from 'vue';

import ErweiterteKundensucheForm
  from '@/pages/kampagnen/components/erweiterteKundensuche/ErweiterteKundensucheForm.vue';
import ErweiterteKundensucheResults
  from '@/pages/kampagnen/components/erweiterteKundensuche/ErweiterteKundensucheResults.vue';
import { useErweiterteKundensucheStore } from '@/pages/kampagnen/stores/erweiterteKundensucheStore';
import { KundeResource } from '@/store/resources/types';

const erweiterteKundensucheStore = useErweiterteKundensucheStore();

const emit = defineEmits<{
  searchResults: [results: KundeResource[]],
}>();

const show = ref(false);
const step = ref<1 | 2>(1);

const isAddingKundenDisabled = computed(() => {
  return erweiterteKundensucheStore.foundKunden.length === 0
    || erweiterteKundensucheStore.isEmpfaengerLimitReached;
});

function openModal() {
  show.value = true;
}

function closeModal() {
  show.value = false;
  step.value = 1;
}

async function fetchResults() {
  step.value = await erweiterteKundensucheStore.submit() ? 2 : 1;
}

function emitResults() {
  emit('searchResults', erweiterteKundensucheStore.foundKunden);
  closeModal();
}
</script>
