<template>
  <DsForm
    :validation-errors="erweiterteKundensucheStore.errors"
    class="-mx-6 overflow-auto px-6 "
  >
    <MaklerFilter class="space-y-3 py-3 md:space-y-0" />
    <DsTabs>
      <DsTab
        title="Kundendaten"
        class="space-y-3 py-3"
        data-test="erweiterte-kundensuche__kundendaten-tab"
      >
        <Kundendaten />
      </DsTab>
      <DsTab
        title="Vertragsdaten"
        class="py-3"
        data-test="erweiterte-kundensuche__vertragsdaten-tab"
      >
        <Vertragsdaten />
      </DsTab>
      <DsTab
        title="Dokumente"
        class="py-3"
        data-test="erweiterte-kundensuche__dokumente-tab"
      >
        <Dokumente />
      </DsTab>
    </DsTabs>
  </DsForm>
</template>

<script setup lang="ts">
import { DsForm, DsTab, DsTabs } from '@demvsystems/design-components';

import Dokumente from '@/pages/kampagnen/components/erweiterteKundensuche/Dokumente.vue';
import MaklerFilter from '@/pages/kampagnen/components/erweiterteKundensuche/MaklerFilter.vue';
import Vertragsdaten from '@/pages/kampagnen/components/erweiterteKundensuche/Vertragsdaten.vue';
import Kundendaten
  from '@/pages/kampagnen/components/erweiterteKundensuche/kundendaten/Kundendaten.vue';
import { useErweiterteKundensucheStore } from '@/pages/kampagnen/stores/erweiterteKundensucheStore';

const erweiterteKundensucheStore = useErweiterteKundensucheStore();
</script>
