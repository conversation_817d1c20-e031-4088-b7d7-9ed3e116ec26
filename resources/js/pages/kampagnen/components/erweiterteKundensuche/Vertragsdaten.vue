<template>
  <div class="space-y-3 p-1">
    <div class="flex flex-col gap-x-4 gap-y-3 sm:flex-row">
      <DsFormGroup
        label="Status"
        class="grow"
        validation-name="vertragsstatus"
      >
        <DsMultiselect
          v-model="vertragsstatusProxy"
          :options="vertragsstatusOptions"
          data-test="erweiterte-kundensuche__vertragsdaten__vertragsstatus"
          object-as-value
        />
      </DsFormGroup>
      <div class="self-center sm:pt-6">
        <DsSwitch
          v-model="erweiterteKundensucheStore.vertragsstatusAusschliessen"
          data-test="erweiterte-kundensuche__vertragsdaten__vertragsstatus-ausschliessen"
        >
          Ausschließen
        </DsSwitch>
      </div>
    </div>

    <div class="flex flex-col gap-x-4 gap-y-3 sm:flex-row">
      <DsFormGroup
        label="Gesellschaften"
        class="grow"
        validation-name="vertragsgesellschaften"
      >
        <DsMultiselect
          v-model="gesellschaftenProxy"
          :options="gesellschaftenOptions"
          :is-loading="isLoadingGesellschaften"
          :item-height="28"
          data-test="erweiterte-kundensuche__vertragsdaten__gesellschaften"
          virtualized
          object-as-value
        />
      </DsFormGroup>
      <div class="self-center sm:pt-6">
        <DsSwitch
          v-model="erweiterteKundensucheStore.vertragsgesellschaftenAusschliessen"
          data-test="erweiterte-kundensuche__vertragsdaten__gesellschaften-ausschliessen"
        >
          Ausschließen
        </DsSwitch>
      </div>
    </div>

    <div class="flex flex-col gap-x-4 gap-y-3 sm:flex-row">
      <DsFormGroup
        label="Sparten"
        class="grow"
        validation-name="vertragssparten"
      >
        <DsMultiselect
          v-model="spartenProxy"
          :options="spartenOptions"
          :is-loading="isLoadingSparten"
          :item-height="28"
          data-test="erweiterte-kundensuche__vertragsdaten__sparten"
          object-as-value
        />
      </DsFormGroup>
      <div class="self-center sm:pt-6">
        <DsSwitch
          v-model="erweiterteKundensucheStore.vertragsspartenAusschliessen"
          data-test="erweiterte-kundensuche__vertragsdaten__sparten-ausschliessen"
        >
          Ausschließen
        </DsSwitch>
      </div>
    </div>

    <DsFormGroup
      label="Vertragstyp"
      class="grow"
      validation-name="vertragstyp"
    >
      <DsSelect
        v-model="erweiterteKundensucheStore.vertragstyp"
        :data="vertragstypData"
      />
    </DsFormGroup>

    <div class="grid gap-x-4 md:grid-cols-2">
      <DsFormGroup
        label="Bruttobeitrag von"
        validation-name="bruttobeitrag.von"
      >
        <DsFormattedNumberInput
          v-model="erweiterteKundensucheStore.bruttobeitrag.von"
          type="number"
          suffix="€"
          :default-value="null"
          data-test="erweiterte-kundensuche__vertragsdaten__bruttobeitrag-von"
        />
      </DsFormGroup>
      <DsFormGroup
        label="Bruttobeitrag bis"
        validation-name="bruttobeitrag.bis"
      >
        <DsFormattedNumberInput
          v-model="erweiterteKundensucheStore.bruttobeitrag.bis"
          type="number"
          suffix="€"
          :default-value="null"
          data-test="erweiterte-kundensuche__vertragsdaten__bruttobeitrag-bis"
        />
      </DsFormGroup>
    </div>

    <div class="grid gap-x-4 md:grid-cols-2">
      <DsFormGroup
        label="Nettobeitrag von"
        validation-name="nettobeitrag.von"
      >
        <DsFormattedNumberInput
          v-model="erweiterteKundensucheStore.nettobeitrag.von"
          type="number"
          suffix="€"
          :default-value="null"
          data-test="erweiterte-kundensuche__vertragsdaten__nettobeitrag-von"
        />
      </DsFormGroup>
      <DsFormGroup
        label="Nettobeitrag bis"
        validation-name="nettobeitrag.bis"
      >
        <DsFormattedNumberInput
          v-model="erweiterteKundensucheStore.nettobeitrag.bis"
          type="number"
          suffix="€"
          :default-value="null"
          data-test="erweiterte-kundensuche__vertragsdaten__nettobeitrag-bis"
        />
      </DsFormGroup>
    </div>

    <DsFormGroup
      label="Verwahrstelle"
      class="grow"
      validation-name="verwahrstelle"
    >
      <DsInput
        v-model="erweiterteKundensucheStore.verwahrstelle"
        data-test="erweiterte-kundensuche__vertragsdaten__verwahrstelle"
      />
    </DsFormGroup>
  </div>
</template>

<script setup lang="ts">
import {
  DsFormattedNumberInput,
  DsFormGroup,
  DsInput,
  DsMultiselect,
  DsSelect,
  DsSwitch,
  MultiselectItem,
} from '@demvsystems/design-components';
import { storeToRefs } from 'pinia';
import { computed } from 'vue';

import {
  gesellschaften,
  isLoading as isLoadingGesellschaften,
} from '@/components/formBasisInfo/gesellschaft/gesellschaftenList';
import {
  isLoading as isLoadingSparten,
  sparten,
} from '@/components/formBasisInfo/sparte/spartenList';
import { useErweiterteKundensucheStore } from '@/pages/kampagnen/stores/erweiterteKundensucheStore';
import { Vertragsstatus, Vertragstyp } from '@/pages/kampagnen/types';
import { GesellschaftResource, SparteResource } from '@/store/resources/types';
import { getMultiselectOptions, makeMultiselectItemProxy } from '@/utils/multiselectEnumHelper';

const erweiterteKundensucheStore = useErweiterteKundensucheStore();

const vertragstypData = [
  {
    value: Vertragstyp.Eigenvertrag,
    label: 'Eigenvertrag',
  },
  {
    value: Vertragstyp.Fremdvertrag,
    label: 'Fremdvertrag',
  },
  {
    value: Vertragstyp.Korrespondenzmakler,
    label: 'Korrespondenzmakler',
  },
  {
    value: Vertragstyp.Antrag,
    label: 'Antrag',
  },
  {
    value: Vertragstyp.Voranfrage,
    label: 'Voranfrage',
  },
];

const vertragsstatusLabels: Record<Vertragsstatus, string>  = {
  [Vertragsstatus.Aktiv]: 'Aktiv',
  [Vertragsstatus.Storniert]: 'Storniert',
  [Vertragsstatus.Beitragsfrei]: 'Beitragsfrei',
  [Vertragsstatus.Sonstige]: 'Sonstige',
  [Vertragsstatus.Ruhend]: 'Ruhend',
};

const {
  vertragsstatus,
  vertragsgesellschaften,
  vertragssparten,
} = storeToRefs(erweiterteKundensucheStore);

const vertragsstatusOptions = getMultiselectOptions(Vertragsstatus, vertragsstatusLabels);
const vertragsstatusProxy = makeMultiselectItemProxy(vertragsstatus, vertragsstatusLabels);

function mapResourcesToOptions(
  resources: (GesellschaftResource | SparteResource)[],
  useExternalId: boolean = true,
): MultiselectItem[] {
  return resources.map((resource) => ({
    value: useExternalId ? resource.attributes.externalId : resource.id,
    label: resource.attributes.name,
  }));
}

function findResourceExternalIdForOption(
  resources: (GesellschaftResource | SparteResource)[],
  option: MultiselectItem,
): string | undefined {
  const selectedResource = resources.find(
    (resource) => (
      resource.attributes.externalId === option.value
    ),
  );

  return selectedResource?.attributes.externalId;
}

function findResourceIdForOption(
  resources: (GesellschaftResource | SparteResource)[],
  option: MultiselectItem,
): string | undefined {
  const selectedResource = resources.find(
    (resource) => (
      resource.id === option.value
    ),
  );

  return selectedResource?.id;
}

const gesellschaftenOptions = computed<MultiselectItem[]>(
  () => mapResourcesToOptions(gesellschaften.value),
);

const gesellschaftenProxy = computed<MultiselectItem[]>({
  get: () => mapResourcesToOptions(
    gesellschaften.value.filter((gesellschaft) => (
      vertragsgesellschaften.value.includes(gesellschaft.attributes.externalId)
    )),
  ),
  set: (options: MultiselectItem[]) => {
    vertragsgesellschaften.value = options
      .map((option) => findResourceExternalIdForOption(gesellschaften.value, option))
      .filter((option): option is string => option !== undefined);
  },
});

const spartenOptions = computed<MultiselectItem[]>(
  () => mapResourcesToOptions(sparten.value, false),
);

const spartenProxy = computed<MultiselectItem[]>({
  get: () => mapResourcesToOptions(
    sparten.value.filter((sparte) => (
      vertragssparten.value.includes(sparte.id)
    )),
    false,
  ),
  set: (options: MultiselectItem[]) => {
    vertragssparten.value = options
      .map((option) => findResourceIdForOption(sparten.value, option))
      .filter((option): option is string => option !== undefined);
  },
});
</script>
