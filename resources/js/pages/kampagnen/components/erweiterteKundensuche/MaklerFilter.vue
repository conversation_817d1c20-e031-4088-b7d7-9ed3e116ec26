<template>
  <div class="grid gap-x-4 md:grid-cols-2">
    <DsFormGroup
      label="Nach Makler filtern"
      validation-name="maklerExternalId"
    >
      <DsSelect
        v-model="erweiterteKundensucheStore.maklerExternalId"
        :data="underlings"
        :search-keys="['attributes.name', 'attributes.email']"
        value-key="attributes.externalId"
        data-test="erweiterte-kundensuche__makler-filter"
      >
        <template #entry="{entry}">
          {{ entry.attributes?.name }}
        </template>
      </DsSelect>
    </DsFormGroup>
    <DsFormGroup
      label="Empfoh<PERSON> von"
      validation-name="empfohlenVonKundeId"
    >
      <KundeSelect
        v-model="erweiterteKundensucheStore.empfohlenVonKundeId"
        placeholder="Kunde auswählen"
        value-key="attributes.externalId"
      />
    </DsFormGroup>
  </div>
</template>

<script setup lang="ts">
import { DsFormGroup, DsSelect } from '@demvsystems/design-components';
import { onMounted } from 'vue';

import KundeSelect from '@/components/formBasisInfo/kunde/KundeSelect.vue';
import useHierarchy from '@/components/users/useHierarchy';
import { useErweiterteKundensucheStore } from '@/pages/kampagnen/stores/erweiterteKundensucheStore';

const erweiterteKundensucheStore = useErweiterteKundensucheStore();

const { underlings, loadUnderlingsAndParents } = useHierarchy();

onMounted(async () => {
  await loadUnderlingsAndParents();
});
</script>
