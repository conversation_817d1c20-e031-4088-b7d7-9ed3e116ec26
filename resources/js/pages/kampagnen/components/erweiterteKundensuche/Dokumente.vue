<template>
  <div class="space-y-3 p-1">
    <div class="flex flex-col gap-x-4 gap-y-3 sm:flex-row">
      <DsFormGroup
        label="Dokumente"
        class="grow"
        validation-name="dokumenttyp"
      >
        <DsSelect
          v-model="erweiterteKundensucheStore.dokumenttyp"
          :data="erweiterteKundensucheStore.dokumenttypen"
          :search-keys="['attributes.name']"
          :item-height="28"
          value-key="id"
          virtualized
          data-test="erweiterte-kundensuche__dokumente__dokumenttyp"
        >
          <template #entry="{entry}">
            <div
              class="truncate"
              :title="entry.attributes?.name"
            >
              {{ entry.attributes?.name }}
            </div>
          </template>
        </DsSelect>
      </DsFormGroup>
      <div class="self-center sm:pt-6">
        <DsSwitch
          v-model="erweiterteKundensucheStore.dokumenttypAusschliessen"
          data-test="erweiterte-kundensuche__dokumente__dokumenttyp-ausschliessen"
        >
          Ausschließen
        </DsSwitch>
      </div>
    </div>

    <div class="grid gap-x-4 md:grid-cols-2">
      <DsFormGroup
        label="Hochgeladen von"
        validation-name="hochgeladen.von"
      >
        <DsInput
          v-model="erweiterteKundensucheStore.hochgeladen.von"
          :datepicker-config="hochgeladenVonConfig"
          type="date"
          data-test="erweiterte-kundensuche__dokumente__hochgeladen-von"
        />
      </DsFormGroup>
      <DsFormGroup
        label="Hochgeladen bis"
        validation-name="hochgeladen.bis"
      >
        <DsInput
          v-model="erweiterteKundensucheStore.hochgeladen.bis"
          :datepicker-config="hochgeladenBisConfig"
          type="date"
          data-test="erweiterte-kundensuche__dokumente__hochgeladen-bis"
        />
      </DsFormGroup>
    </div>
  </div>
</template>

<script setup lang="ts">
import { DsFormGroup, DsInput, DsSelect, DsSwitch } from '@demvsystems/design-components';
import { computed, onMounted } from 'vue';

import { useErweiterteKundensucheStore } from '@/pages/kampagnen/stores/erweiterteKundensucheStore';

const erweiterteKundensucheStore = useErweiterteKundensucheStore();

const hochgeladenVonConfig = computed(() => ({
  ...(erweiterteKundensucheStore.hochgeladen.bis === null)
    ? null
    : { upperLimit: new Date(erweiterteKundensucheStore.hochgeladen.bis) },
}));

const hochgeladenBisConfig = computed(() =>  ({
  ...(erweiterteKundensucheStore.hochgeladen.von === null)
    ? null
    : { lowerLimit: new Date(erweiterteKundensucheStore.hochgeladen.von) },
}));

onMounted(async () => {
  if (erweiterteKundensucheStore.dokumenttypen.length === 0) {
    await erweiterteKundensucheStore.initDokumenttypen();
  }
});
</script>
