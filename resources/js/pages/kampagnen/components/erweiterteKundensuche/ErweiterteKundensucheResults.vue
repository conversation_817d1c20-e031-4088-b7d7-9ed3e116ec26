<template>
  <div class="-mx-6 space-y-3 overflow-auto px-6 py-3">
    <DsTable
      :columns="columns"
      :data="rows"
      :sort-settings="{
        name: 'name',
        order: SortOrder.Ascending,
      }"
      no-data-text="Ihre Suche hat keine Treffer ergeben"
      condensed
      virtualized
      :style="`max-height: calc(100vh - ${isEmpfaengerLimitReached ? '23rem' : '17rem'})`"
    >
      <template #entry-name="{entry}">
        <NameTableCell
          :name="entry.name"
          :url="entry.url"
          :is-loading="isRefreshingKundeLookup.has(entry.id)"
          @click-link="refreshKundeAfterOpeningTabAgain(entry.id)"
        />
      </template>

      <template #entry-hasAddress="{value, entry}">
        <AddressTableCell
          :has-address="value"
          :is-loading="isLoading || isRefreshingKundeLookup.has(entry.id)"
        />
      </template>

      <template #entry-email="{value, entry}">
        <EmailTableCell
          :email="value"
          :is-loading="isRefreshingKundeLookup.has(entry.id)"
        />
      </template>
    </DsTable>
    <DsAlert
      v-if="isEmpfaengerLimitReached"
      label="Zu viele Empfänger"
      type="error"
    >
      Momentan darf eine {{ versandart === Versandart.Mail ? 'E-Mail-' : 'Brief-' }}Kampagne
      an <b>maximal {{ empfaengerLimit }}</b> Empfänger verschickt werden.
      Bitte verfeinern Sie Ihre Suche.
    </DsAlert>
  </div>
</template>

<script setup lang="ts">
import { DsAlert, DsTable, SortOrder } from '@demvsystems/design-components';
import { storeToRefs } from 'pinia';

import EmailTableCell from '@/pages/kampagnen/components/kundenTable/EmailTableCell.vue';
import AddressTableCell from '@/pages/kampagnen/components/kundenTable/HasAddressTableCell.vue';
import NameTableCell from '@/pages/kampagnen/components/kundenTable/NameTableCell.vue';
import { useKundenTable } from '@/pages/kampagnen/composables/kunden/useKundenTable';
import { useRefreshingKunde } from '@/pages/kampagnen/composables/kunden/useRefreshingKunde';
import { useErweiterteKundensucheStore } from '@/pages/kampagnen/stores/erweiterteKundensucheStore';
import { useKampagneCreateStore } from '@/pages/kampagnen/stores/kampagneCreateStore';
import { Versandart } from '@/pages/kampagnen/types';
import { KundeResource } from '@/store/resources/types';

const { versandart } = useKampagneCreateStore();

const { foundKunden } = storeToRefs(useErweiterteKundensucheStore());

const { isEmpfaengerLimitReached, empfaengerLimit } = useErweiterteKundensucheStore();

const { columns, rows, isLoading, loadMissingAddressKundenIds } = useKundenTable(foundKunden);

function handleRefreshedKunde(refreshedKunde: KundeResource) {
  const idx = foundKunden.value.findIndex((k) => k.id === refreshedKunde.id);
  if (idx === -1) {
    return;
  }

  foundKunden.value[idx] = refreshedKunde;
  void loadMissingAddressKundenIds([refreshedKunde]);
}

const {
  isRefreshingKundeLookup,
  refreshKundeAfterOpeningTabAgain,
} = useRefreshingKunde(handleRefreshedKunde);
</script>
