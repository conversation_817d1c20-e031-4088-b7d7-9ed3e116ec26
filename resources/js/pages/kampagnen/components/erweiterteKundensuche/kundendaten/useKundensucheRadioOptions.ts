import { computed, Ref, WritableComputedRef } from 'vue';

import { KundensucheRadioOptions } from '@/pages/kampagnen/types';

export function useKundensucheRadioOptions(): {
  createProxy: (property: Ref<boolean | null>) => WritableComputedRef<KundensucheRadioOptions>
} {
  function toNativeValue(value: KundensucheRadioOptions): boolean | null {
    if (value === KundensucheRadioOptions.NichtBeachten) {
      return null;
    }

    return value === KundensucheRadioOptions.Ja;
  }

  function fromNativeValue(value: boolean | null): KundensucheRadioOptions {
    if (value === null) {
      return  KundensucheRadioOptions.NichtBeachten;
    }

    return value ? KundensucheRadioOptions.Ja : KundensucheRadioOptions.Nein;
  }

  function createProxy(property: Ref<boolean | null>) {
    return computed({
      get() {
        return fromNativeValue(property.value);
      },
      set(value) {
        property.value = toNativeValue(value);
      },
    },
    );
  }

  return {
    createProxy,
  };
}
