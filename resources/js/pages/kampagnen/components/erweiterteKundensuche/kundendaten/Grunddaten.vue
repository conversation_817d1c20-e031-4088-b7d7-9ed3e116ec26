<template>
  <Accordion
    v-model="erweiterteKundensuche.accordionIsOpen.grunddaten"
    title="Grunddaten"
  >
    <DsFormGroup
      label="Interne Kundennummer"
      validation-name="kundennummerIntern"
    >
      <div class="flex flex-row">
        <DsInput
          v-model="erweiterteKundensuche.kundennummerIntern"
          class="grow"
          data-test="erweiterte-kundensuche__kundennummerintern__ort"
        />
      </div>
    </DsFormGroup>
    <DsFormGroup
      label="Ort"
      validation-name="ort"
    >
      <div class="flex flex-row">
        <DsInput
          v-model="erweiterteKundensuche.ort"
          class="grow"
          data-test="erweiterte-kundensuche__kundenakte__ort"
        />
      </div>
    </DsFormGroup>

    <div class="grid gap-x-4 md:grid-cols-2">
      <DsFormGroup
        label="PL<PERSON> von"
        validation-name="plz.von"
      >
        <DsInput
          v-model="erweiterteKundensuche.plz.von"
          data-test="erweiterte-kundensuche__kundenakte__plz-von"
        />
      </DsFormGroup>
      <DsFormGroup
        label="PLZ bis"
        validation-name="plz.bis"
      >
        <DsInput
          v-model="erweiterteKundensuche.plz.bis"
          data-test="erweiterte-kundensuche__kundenakte__plz-bis"
        />
      </DsFormGroup>
    </div>

    <div class="grid gap-x-4 md:grid-cols-2">
      <DsFormGroup
        label="Alter von"
        validation-name="kundenAlter.von"
      >
        <DsInput
          v-model="erweiterteKundensuche.kundenAlter.von"
          type="number"
          data-test="erweiterte-kundensuche__kundenakte__kundenalter-von"
        />
      </DsFormGroup>
      <DsFormGroup
        label="Alter bis"
        validation-name="kundenAlter.bis"
      >
        <DsInput
          v-model="erweiterteKundensuche.kundenAlter.bis"
          type="number"
          data-test="erweiterte-kundensuche__kundenakte__kundenalter-bis"
        />
      </DsFormGroup>
    </div>
    <div class="grid gap-x-4 md:grid-cols-2">
      <DsFormGroup
        label="Geburtsmonat von"
        validation-name="geburtsmonat.von"
      >
        <DsSelect
          v-model="erweiterteKundensuche.geburtsmonat.von"
          :data="months"
          data-test="erweiterte-kundensuche__kundenakte__geburtsmonat-von"
        />
      </DsFormGroup>
      <DsFormGroup
        label="Geburtsmonat bis"
        validation-name="geburtsmonat.bis"
      >
        <DsSelect
          v-model="erweiterteKundensuche.geburtsmonat.bis"
          :data="months"
          data-test="erweiterte-kundensuche__kundenakte__geburtsmonat-bis"
        />
      </DsFormGroup>
    </div>

    <div class="grid gap-x-4 md:grid-cols-2">
      <DsFormGroup
        label="Geburtstag von"
        validation-name="geburtstag.von"
      >
        <DsInput
          v-model="erweiterteKundensuche.geburtstag.von"
          data-test="erweiterte-kundensuche__kundenakte__geburtstag-von"
          :datepicker-config="datepickerConfig"
          type="date"
        />
      </DsFormGroup>
      <DsFormGroup
        label="Geburtstag bis"
        validation-name="geburtstag.bis"
      >
        <DsInput
          v-model="erweiterteKundensuche.geburtstag.bis"
          data-test="erweiterte-kundensuche__kundenakte__geburtstag-bis"
          :datepicker-config="datepickerConfig"
          type="date"
        />
      </DsFormGroup>
    </div>
    <div class="flex flex-col gap-x-4 gap-y-3 sm:flex-row">
      <DsFormGroup
        label="Berufsstatus"
        class="grow"
        validation-name="berufsstatus"
      >
        <DsMultiselect
          v-model="berufsstatusProxy"
          :options="berufsstatusOptions"
          data-test="erweiterte-kundensuche__kundenakte__berufsstatus"
          object-as-value
        />
      </DsFormGroup>
      <div class="self-center sm:pt-6">
        <DsSwitch
          v-model="erweiterteKundensuche.berufsstatusAusschliessen"
          data-test="erweiterte-kundensuche__kundenakte__berufsstatus-ausschliessen"
        >
          Ausschließen
        </DsSwitch>
      </div>
    </div>

    <div class="flex flex-col gap-x-4 gap-y-3 sm:flex-row">
      <DsFormGroup
        label="Status"
        class="grow"
        validation-name="kundenstatus"
      >
        <DsMultiselect
          v-model="kundenstatusProxy"
          :options="kundenstatusOptions"
          data-test="erweiterte-kundensuche__kundenakte__kundenstatus"
          object-as-value
        />
      </DsFormGroup>
      <div class="self-center sm:pt-6">
        <DsSwitch
          v-model="erweiterteKundensuche.statusAusschliessen"
          data-test="erweiterte-kundensuche__kundenakte__kundenstatus-ausschliessen"
        >
          Ausschließen
        </DsSwitch>
      </div>
    </div>
    <div class="flex flex-col gap-x-4 gap-y-3 sm:flex-row">
      <DsFormGroup
        label="Zielgruppe"
        class="grow"
        validation-name="zielgruppe"
      >
        <DsMultiselect
          v-model="zielgruppeProxy"
          :options="zielgruppeOptions"
          data-test="erweiterte-kundensuche__kundenakte__zielgruppe"
          object-as-value
        />
      </DsFormGroup>
      <div class="self-center sm:pt-6">
        <DsSwitch
          v-model="erweiterteKundensuche.zielgruppeAusschliessen"
          data-test="erweiterte-kundensuche__kundenakte__zielgruppe-ausschliessen"
        >
          Ausschließen
        </DsSwitch>
      </div>
    </div>
  </Accordion>
</template>

<script setup lang="ts">
import {
  DsFormGroup,
  DsInput,
  DsMultiselect,
  DsSwitch,
  DsSelect,
  DatepickerConfig,
} from '@demvsystems/design-components';
import { storeToRefs } from 'pinia';

import Accordion
  from '@/components/Accordion.vue';
import { useErweiterteKundensucheStore } from '@/pages/kampagnen/stores/erweiterteKundensucheStore';
import { Berufsstatus, Kundenstatus, Zielgruppe } from '@/pages/kampagnen/types';
import { makeMultiselectItemProxy, getMultiselectOptions } from '@/utils/multiselectEnumHelper';

const erweiterteKundensuche = useErweiterteKundensucheStore();

const datepickerConfig: DatepickerConfig = {
  upperLimit: new Date(),
};

const kundenstatusLabels: Record<Kundenstatus, string>  =  {
  [Kundenstatus.Kunde]: 'Kunde',
  [Kundenstatus.Interessent]: 'Interessent',
  [Kundenstatus.KundeA]: 'Kunde (A)',
  [Kundenstatus.KundeB]: 'Kunde (B)',
  [Kundenstatus.KundeC]: 'Kunde (C)',
  [Kundenstatus.KundeD]: 'Kunde (D)',
  [Kundenstatus.KundeStorniert]: 'Kunde storniert',
  [Kundenstatus.KundeVerstorben]: 'Kunde verstorben',
  [Kundenstatus.InteressentA]: 'Interessent (A)',
  [Kundenstatus.InteressentB]: 'Interessent (B)',
  [Kundenstatus.InteressentC]: 'Interessent (C)',
  [Kundenstatus.InteressentD]: 'Interessent (D)',
};

const berufsstatusLabels: Record<Berufsstatus, string> = {
  [Berufsstatus.Angestellte]: 'Angestellte',
  [Berufsstatus.Minijob]: 'Angestellte - Minijob',
  [Berufsstatus.AngestellteOeffentlicherDienst]: 'Angestellte öffentlicher Dienst',
  [Berufsstatus.Arbeitssuchend]: 'Arbeitssuchend',
  [Berufsstatus.BeamterAufLebenszeit]: 'Beamter auf Lebenszeit',
  [Berufsstatus.BeamterAufZeitWiderrufProbe]: 'Beamter auf Widerruf / Probe / Zeit',
  [Berufsstatus.Bundeswehr]: 'Bundeswehr',
  [Berufsstatus.ErwerbsunfaehigNichtErwerbstaetig]: 'Erwerbsunfähig / nicht erwerbstätig',
  [Berufsstatus.GeschaeftsfuehrenderGesellschafter]: 'Geschäftsführender Gesellschafter',
  [Berufsstatus.GeschaeftsfuehrenderGesellschafterOhneEntgeltfortzahlung]: 'Geschäftsführender Gesellschafter ohne Entgeltfortzahlung',
  [Berufsstatus.HausfrauHausmann]: 'Hausfrau / Hausmann',
  [Berufsstatus.PersonInBerufsausbildung]: 'Person in Berufsausbildung',
  [Berufsstatus.PersonInElternzeit]: 'Person in Elternzeit',
  [Berufsstatus.PersonInSchulausbildungPraktikumFsj]: 'Person in Schulausbildung / Praktikum / FSJ',
  [Berufsstatus.PersonInStudium]: 'Person in Studium',
  [Berufsstatus.RentnerRuhestaendler]: 'Rentner / Ruheständler',
  [Berufsstatus.SelbststaendigerFreiberufler]: 'Selbstständiger / Freiberufler',
  [Berufsstatus.Sonstige]: 'Sonstige',
  [Berufsstatus.Vorstand]: 'Vorstand',
};

const zielgruppeLabels: Record<Zielgruppe, string>  = {
  [Zielgruppe.Angestellte]: 'Angestellte',
  [Zielgruppe.AngestellteOeffentlicherDienst]: 'Angestellte öffentlicher Dienst',
  [Zielgruppe.Aerzte]: 'Ärzte',
  [Zielgruppe.Auszubildende]: 'Auszubildende',
  [Zielgruppe.Beamte]: 'Beamte',
  [Zielgruppe.Existenzgruender]: 'Existenzgründer',
  [Zielgruppe.Familie]: 'Familie',
  [Zielgruppe.FreieBerufe]: 'Freie Berufe',
  [Zielgruppe.GesellschafterGeschaeftsfuehrerGmbH]: 'Gesellschafter Geschäftsführer GmbH',
  [Zielgruppe.HaeusleBauer]: 'Häusle-Bauer',
  [Zielgruppe.Hausverwalter]: 'Hausverwalter',
  [Zielgruppe.Humanmediziner]: 'Humanmediziner',
  [Zielgruppe.Immobilienbesitzer]: 'Immobilienbesitzer',
  [Zielgruppe.Kinder]: 'Kinder',
  [Zielgruppe.KuenstlerUndPublizisten]: 'Künstler und Publizisten',
  [Zielgruppe.Landwirte]: 'Landwirte',
  [Zielgruppe.Mediziner]: 'Mediziner',
  [Zielgruppe.Piloten]: 'Piloten',
  [Zielgruppe.Ruhestaendler]: 'Ruheständler',
  [Zielgruppe.Schueler]: 'Schüler',
  [Zielgruppe.SelbststaendigeHandwerker]: 'Selbständige Handwerker',
  [Zielgruppe.SelbststaendigeFreiberufler]: 'Selbständige/Freiberufler',
  [Zielgruppe.SoldatenPolizistenFeuerwehrleute]: 'Soldaten, Polizisten, Feuerwehrleute',
  [Zielgruppe.Studenten]: 'Studenten',
  [Zielgruppe.Veterinaermediziner]: 'Veterinärmediziner',
  [Zielgruppe.Zahnaerzte]: 'Zahnärzte',
};

const months: { label: string, value: number }[] = [
  { label: 'Januar', value: 1 },
  { label: 'Februar', value: 2 },
  { label: 'März', value: 3 },
  { label: 'April', value: 4 },
  { label: 'Mai', value: 5 },
  { label: 'Juni', value: 6 },
  { label: 'Juli', value: 7 },
  { label: 'August', value: 8 },
  { label: 'September', value: 9 },
  { label: 'Oktober', value: 10 },
  { label: 'November', value: 11 },
  { label: 'Dezember', value: 12 },
];

const { berufsstatus, kundenstatus, zielgruppe } = storeToRefs(erweiterteKundensuche);

const berufsstatusOptions = getMultiselectOptions(Berufsstatus, berufsstatusLabels);
const kundenstatusOptions = getMultiselectOptions(Kundenstatus, kundenstatusLabels);
const zielgruppeOptions = getMultiselectOptions(Zielgruppe, zielgruppeLabels);

const berufsstatusProxy = makeMultiselectItemProxy(berufsstatus, berufsstatusLabels);
const kundenstatusProxy = makeMultiselectItemProxy(kundenstatus, kundenstatusLabels);
const zielgruppeProxy = makeMultiselectItemProxy(zielgruppe, zielgruppeLabels);
</script>
