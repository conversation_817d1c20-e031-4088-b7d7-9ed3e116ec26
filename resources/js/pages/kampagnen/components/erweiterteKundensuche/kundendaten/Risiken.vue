<template>
  <Accordion
    v-model="accordionIsOpen.risiken"
    title="Risiken"
  >
    <KundensucheRadioGroup
      v-model="negativeCreditRatingProxy"
      label="Negative Bonität"
      data-test="erweiterte-kundensuche__kundenakte__negative-credit-rating"
    />

    <KundensucheRadioGroup
      v-model="boatProxy"
      label="Segel-/ Motorboot"
      data-test="erweiterte-kundensuche__kundenakte__boat"
    />

    <KundensucheRadioGroup
      v-model="motorcycleProxy"
      label="Motorrad"
      data-test="erweiterte-kundensuche__kundenakte__motorcycle"
    />

    <KundensucheRadioGroup
      v-model="carProxy"
      label="PKW"
      data-test="erweiterte-kundensuche__kundenakte__car"
    />

    <DsFormGroup label="Raucher">
      <DsRadioGroup
        v-model="smoker"
        variant="button"
      >
        <DsRadioButton
          :value="RaucherRadioOptions.Ja"
        >
          Ja
        </DsRadioButton>
        <DsRadioButton
          :value="RaucherRadioOptions.Nein"
        >
          Nein
        </DsRadioButton>
        <DsRadioButton
          :value="RaucherRadioOptions.Gelegentlich"
        >
          Gelegentlich
        </DsRadioButton>
        <DsRadioButton
          :value="RaucherRadioOptions.NichtBeachten"
        >
          Nicht beachten
        </DsRadioButton>
      </DsRadioGroup>
    </DsFormGroup>

    <KundensucheRadioGroup
      v-model="petOwnerProxy"
      label="Tierhalter"
      data-test="erweiterte-kundensuche__kundenakte__pet-owner"
    />

    <KundensucheRadioGroup
      v-model="dangerousHobbiesProxy"
      label="Gefährliche Hobbies"
      data-test="erweiterte-kundensuche__kundenakte__dangerous-hobbies"
    />

    <KundensucheRadioGroup
      v-model="seriousIllnessProxy"
      label="Hat schwere Krankheiten"
      data-test="erweiterte-kundensuche__kundenakte__serious-illness"
    />
  </Accordion>
</template>

<script setup lang="ts">
import { DsFormGroup, DsRadioButton, DsRadioGroup } from '@demvsystems/design-components';
import { storeToRefs } from 'pinia';

import Accordion
  from '@/components/Accordion.vue';
import KundensucheRadioGroup
  from '@/pages/kampagnen/components/erweiterteKundensuche/kundendaten/KundesucheRadioGroup.vue';
import { useErweiterteKundensucheStore } from '@/pages/kampagnen/stores/erweiterteKundensucheStore';
import { RaucherRadioOptions } from '@/pages/kampagnen/types';

import { useKundensucheRadioOptions } from './useKundensucheRadioOptions';

const {
  negativeCreditRating,
  boat,
  motorcycle,
  car,
  smoker,
  petOwner,
  dangerousHobbies,
  seriousIllness,
  accordionIsOpen,
} = storeToRefs(useErweiterteKundensucheStore());
const { createProxy } = useKundensucheRadioOptions();

const negativeCreditRatingProxy = createProxy(negativeCreditRating);
const boatProxy = createProxy(boat);
const motorcycleProxy = createProxy(motorcycle);
const carProxy = createProxy(car);
const petOwnerProxy = createProxy(petOwner);
const dangerousHobbiesProxy = createProxy(dangerousHobbies);
const seriousIllnessProxy = createProxy(seriousIllness);
</script>
