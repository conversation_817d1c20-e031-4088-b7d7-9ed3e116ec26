<template>
  <Accordion
    v-model="erweiterteKundensuche.accordionIsOpen.berufUndEinkommen"
    title="<PERSON><PERSON><PERSON> und Einkommen"
  >
    <DsFormGroup
      label="Beruf"
      validation-name="beruf"
    >
      <DsInput
        v-model="erweiterteKundensuche.beruf"
        data-test="erweiterte-kundensuche__kundenakte__beruf"
      />
    </DsFormGroup>

    <div class="grid gap-x-4 gap-y-3 md:grid-cols-2">
      <DsFormGroup
        label="Kaufmännisch tätig von"
        validation-name="kaufmaennischTaetig.von"
      >
        <DsFormattedNumberInput
          v-model="erweiterteKundensuche.kaufmaennischTaetig.von"
          type="number"
          suffix="%"
          :default-value="null"
          data-test="erweiterte-kundensuche__kundenakte__kaufmaennisch-taetig"
        />
      </DsFormGroup>
      <DsFormGroup
        label="Kaufmännisch tätig bis"
        validation-name="kaufmaennischTaetig.bis"
      >
        <DsFormattedNumberInput
          v-model="erweiterteKundensuche.kaufmaennischTaetig.bis"
          type="number"
          suffix="%"
          :default-value="null"
          data-test="erweiterte-kundensuche__kundenakte__kaufmaennisch-taetig"
        />
      </DsFormGroup>

      <DsFormGroup
        label="Körperlich tätig von"
        validation-name="koerperlichTaetig.von"
      >
        <DsFormattedNumberInput
          v-model="erweiterteKundensuche.koerperlichTaetig.von"
          type="number"
          suffix="%"
          :default-value="null"
          data-test="erweiterte-kundensuche__kundenakte__korperlich-taetig"
        />
      </DsFormGroup>
      <DsFormGroup
        label="Körperlich tätig bis"
        validation-name="koerperlichTaetig.bis"
      >
        <DsFormattedNumberInput
          v-model="erweiterteKundensuche.koerperlichTaetig.bis"
          type="number"
          suffix="%"
          :default-value="null"
          data-test="erweiterte-kundensuche__kundenakte__korperlich-taetig"
        />
      </DsFormGroup>

      <DsFormGroup
        label="Nettoeinkommen von"
        validation-name="nettoeinkommen.von"
      >
        <DsFormattedNumberInput
          v-model="erweiterteKundensuche.nettoeinkommen.von"
          type="number"
          suffix="€"
          :default-value="null"
          data-test="erweiterte-kundensuche__kundenakte__nettoeinkommen-von"
        />
      </DsFormGroup>
      <DsFormGroup
        label="Nettoeinkommen bis"
        validation-name="nettoeinkommen.bis"
      >
        <DsFormattedNumberInput
          v-model="erweiterteKundensuche.nettoeinkommen.bis"
          type="number"
          suffix="€"
          :default-value="null"
          data-test="erweiterte-kundensuche__kundenakte__nettoeinkommen-bis"
        />
      </DsFormGroup>
      <DsFormGroup
        label="Bruttoeinkommen von"
        validation-name="bruttoeinkommen.von"
      >
        <DsFormattedNumberInput
          v-model="erweiterteKundensuche.bruttoeinkommen.von"
          type="number"
          suffix="€"
          :default-value="null"
          data-test="erweiterte-kundensuche__kundenakte__bruttoeinkommen-von"
        />
      </DsFormGroup>
      <DsFormGroup
        label="Bruttoeinkommen bis"
        validation-name="bruttoeinkommen.bis"
      >
        <DsFormattedNumberInput
          v-model="erweiterteKundensuche.bruttoeinkommen.bis"
          type="number"
          suffix="€"
          :default-value="null"
          data-test="erweiterte-kundensuche__kundenakte__bruttoeinkommen-bis"
        />
      </DsFormGroup>
    </div>
  </Accordion>
</template>

<script setup lang="ts">
import {
  DsFormattedNumberInput,
  DsFormGroup,
  DsInput,
} from '@demvsystems/design-components';

import Accordion
  from '@/components/Accordion.vue';
import { useErweiterteKundensucheStore } from '@/pages/kampagnen/stores/erweiterteKundensucheStore';

const erweiterteKundensuche = useErweiterteKundensucheStore();
</script>
