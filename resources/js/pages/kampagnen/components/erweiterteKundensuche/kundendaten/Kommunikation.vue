<template>
  <Accordion
    v-model="accordionIsOpen.kommunikation"
    title="Kommunikation"
  >
    <KundensucheRadioGroup
      v-model="finanzmanagerAccessProxy"
      label="Besitzt Zugang zum Finanzmanager"
      data-test="erweiterte-kundensuche__kundenakte__finanzmanager-access"
    />
    <KundensucheRadioGroup
      v-model="hasEmailAddressProxy"
      label="Hat E-Mail Adresse"
      data-test="erweiterte-kundensuche__kundenakte__has-email-address"
    />
  </Accordion>
</template>

<script setup lang="ts">
import { storeToRefs } from 'pinia';

import Accordion
  from '@/components/Accordion.vue';
import KundensucheRadioGroup
  from '@/pages/kampagnen/components/erweiterteKundensuche/kundendaten/KundesucheRadioGroup.vue';
import { useErweiterteKundensucheStore } from '@/pages/kampagnen/stores/erweiterteKundensucheStore';

import { useKundensucheRadioOptions } from './useKundensucheRadioOptions';

const {
  finanzmanagerAccess,
  hasEmailAddress,
  accordionIsOpen,
} = storeToRefs(useErweiterteKundensucheStore());
const { createProxy } = useKundensucheRadioOptions();

const finanzmanagerAccessProxy = createProxy(finanzmanagerAccess);
const hasEmailAddressProxy = createProxy(hasEmailAddress);
</script>
