<template>
  <DsFormGroup :label="label">
    <DsRadioGroup
      :model-value="modelValue"
      variant="button"
      @update:model-value="$emit('update:modelValue', $event)"
    >
      <DsRadioButton
        :value="KundensucheRadioOptions.Ja"
      >
        Ja
      </DsRadioButton>
      <DsRadioButton
        :value="KundensucheRadioOptions.Nein"
      >
        Nein
      </DsRadioButton>
      <DsRadioButton
        :value="KundensucheRadioOptions.NichtBeachten"
      >
        Nicht beachten
      </DsRadioButton>
    </DsRadioGroup>
  </DsFormGroup>
</template>

<script setup lang="ts">
import { DsFormGroup, DsRadioButton, DsRadioGroup } from '@demvsystems/design-components';

import { KundensucheRadioOptions } from '@/pages/kampagnen/types';

defineProps<{
  label: string,
  modelValue: KundensucheRadioOptions,
}>();

defineEmits<{
  (event: 'update:modelValue', value: KundensucheRadioOptions): KundensucheRadioOptions
}>();
</script>
