<template>
  <div class="overflow-y-auto p-4 sm:p-6">
    <section
      class="mx-auto flex flex-col space-y-7 md:max-w-4xl"
    >
      <div
        v-if="isLoadingKampagne"
        class="space-y-3"
      >
        <DsSkeleton class="h-8 w-1/4" />
        <DsSkeleton class="h-32 w-full" />
      </div>
      <template
        v-else
      >
        <div class="space-y-3">
          <span v-if="versendetAtFormatted !== null">
            Versendet am {{ versendetAtFormatted }}
          </span>
          <div
            v-if="vorgangId || versandart === Versandart.Brief"
            class="flex space-x-2"
          >
            <DsButton
              v-if="vorgangId"
              size="sm"
              icon="inbox"
              variant="secondary"
              :href="`/vorgaenge/${vorgangId}`"
            >
              Zur Vorgangsgruppe
            </DsButton>
            <DsButton
              v-if="versandart === Versandart.Brief"
              size="sm"
              icon="download"
              variant="primary"
              :href="getPdfDownloadUrl()"
            >
              Alle PDFs herunterladen
            </DsButton>
          </div>
        </div>
        <div
          v-if="hasEmailsStillBeingProcessedOrSent || hasEmailsWithStatusFehler"
          class="space-y-5"
        >
          <EmailsStillBeingProcessedOrSentAlert v-if="hasEmailsStillBeingProcessedOrSent" />
          <ResendFailedMessagesAlert
            v-if="kampagne !== null && hasEmailsWithStatusFehler"
            :sending-finished="!hasEmailsStillBeingProcessedOrSent"
            :kampagne="kampagne"
          />
        </div>
        <KampagneStatistics
          v-if="versandart === Versandart.Mail"
          label="Verschickt"
          class="self-start"
          :current="sentMessages"
          :total="messagesCount"
        />
        <div class="flex flex-col space-y-3">
          <h4 class="text-large font-semibold text-gray-800">
            Empfänger
          </h4>
          <KampagneMessagesTable
            style="max-height: max(calc(100vh - 40rem), 20rem)"
            :is-loading-preview="isLoadingPreview"
            :is-loading-messages="isLoadingMessages"
            :kampagne-id="kampagneId"
            :versandart="versandart"
            :kampagne-messages="kampagneMessages"
            @open-vorschau-modal="openVorschauModal"
          />
          <KampagneMessageVorschauModal
            v-model="viewVorschauModal"
            :preview-betreff="previewBetreff"
            :preview-content="previewContent"
            :preview-pdf="previewPdf"
            :versandart="versandart"
            :absender="selectedAbsender"
            :empfaenger="selectedEmpfaenger"
            suppress-errors-and-retry
          />
        </div>

        <DsButton
          class="self-start"
          variant="secondary"
          @click="routeToList"
        >
          Zur Übersicht
        </DsButton>
      </template>
    </section>
  </div>
</template>

<script lang="ts" setup>
import { DsButton, DsSkeleton } from '@demvsystems/design-components';
import { format } from 'date-fns';
import { computed, onMounted, ref, watch } from 'vue';
import { useRouter } from 'vue-router';

import { get } from '@/api';
import KampagneMessagesTable
  from '@/pages/kampagnen/components/kampagneEinzelseite/KampagneMessagesTable.vue';
import KampagneStatistics
  from '@/pages/kampagnen/components/kampagneEinzelseite/KampagneStatistics.vue';
import ResendFailedMessagesAlert
  from '@/pages/kampagnen/components/kampagneEinzelseite/ResendFailedMessagesAlert.vue';
import { useKampagneMessagePreview } from '@/pages/kampagnen/composables/useKampagneMessagePreview';
import { useKampagnePageTitle } from '@/pages/kampagnen/composables/useKampagnePageTitle';
import { useLoadKampagneData } from '@/pages/kampagnen/composables/useLoadKampagneData';
import EmailsStillBeingProcessedOrSentAlert
  from '@/pages/kampagnen/pages/EmailsStillBeingProcessedOrSentAlert.vue';
import { KampagneStatus, Versandart } from '@/pages/kampagnen/types';
import { eventBus } from '@/store/resources/store';
import { EmailElement, EmailStatus, KampagneResource } from '@/store/resources/types';

import KampagneMessageVorschauModal
  from '../components/createForm/inhalt/KampagneMessagePreviewModal.vue';

const props = defineProps<{
  id?: string,
}>();

const {
  viewVorschauModal,
  previewPdf,
  previewContent,
  previewBetreff,
  isLoading: isLoadingPreview,
  getPdf,
  getHtml,
} = useKampagneMessagePreview();

const {
  kampagneId,
  messageMap,
  emailMap,
  isLoadingMessages,
  fetchMessages,
  getPdfDownloadUrl,
  messagesCount,
  sentMessages,
} = useLoadKampagneData();

const router = useRouter();

const {
  setPageTitle,
} = useKampagnePageTitle();

const selectedAbsender = ref<EmailElement[]>([]);
const selectedEmpfaenger = ref<EmailElement[]>([]);

const isLoadingKampagne = ref<boolean>(false);
const kampagne = ref<KampagneResource | null>(null);

const kampagneMessages = computed(() =>
  Array.from(messageMap.value.values()).sort((a, b) =>
    new Date(a.attributes.createdAt).getTime() - new Date(b.attributes.createdAt).getTime(),
  ),
);
const versendetAtFormatted = computed<string | null>(() => {
  const versendetAt = kampagne.value?.attributes.versendetAt ?? null;
  if (versendetAt === null) {
    return null;
  }

  return format(new Date(versendetAt), "dd.MM.yyyy 'um' HH:mm 'Uhr'");
});
const versandart = computed(() => kampagne.value?.attributes.versandart ?? Versandart.Mail);
const vorgangId = computed(() => kampagne.value?.attributes.vorgangId ?? null);

async function openVorschauModal(messageId: string, emailId: string) {
  const selectedEmailMeta = emailMap.value.get(emailId) || null;

  if (selectedEmailMeta !== null) {
    const { absenderActual, absender, empfaengerActual, empfaenger } = selectedEmailMeta.attributes;

    selectedAbsender.value = absenderActual ? [absenderActual] : [absender];
    selectedEmpfaenger.value = empfaengerActual ?? empfaenger;
  }

  if (versandart.value === Versandart.Brief) {
    await getPdf(kampagneId.value, messageId);
  } else {
    await getHtml(kampagneId.value, messageId);
  }
}

const hasEmailsWithStatusFehler = computed(() =>
  [...emailMap.value.values()].some((email) => email.attributes.status === EmailStatus.Fehler),
);

const hasEmailsStillBeingProcessedOrSent = computed(() =>
  [...emailMap.value.values()].some((email) => [EmailStatus.Versandbereit, EmailStatus.InVersand]
    .includes(email.attributes.status!)),
);

watch(() => props.id, async (newId) => {
  if (newId === undefined) {
    return;
  }

  kampagneId.value = newId;

  try {
    isLoadingKampagne.value = true;
    const response = await get<KampagneResource>(`kampagnen/${newId}`);

    if (response.data.data !== undefined) {
      kampagne.value = response.data.data;

      if (kampagne.value.attributes.status === KampagneStatus.Entwurf) {
        void router.replace({
          name: 'kampagnen.edit.basis',
          params: {
            id: kampagne.value.id,
          },
        });
      }

      if (kampagne.value.attributes.status === KampagneStatus.Geplant) {
        void router.replace({
          name: 'kampagnen.list',
        });
      }

      setPageTitle(kampagne.value.attributes.titel as string);
    }
  } catch {
    eventBus.emit('error');
  } finally {
    isLoadingKampagne.value = false;
  }
}, { immediate: true });

onMounted(() => {
  void fetchMessages();
});

function routeToList() {
  void router.push({
    name: 'kampagnen.list',
  });
}
</script>
