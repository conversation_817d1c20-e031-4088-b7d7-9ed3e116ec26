<template>
  <DsForm
    class="w-full space-y-5"
    :validation-errors="kampagneCreateStore.errors"
    data-test="kampagne-create__step2"
    @submit.prevent
  >
    <div class="flex flex-row justify-between">
      <div class="flex flex-row justify-start space-x-3">
        <ErweiterteKundenSucheModal
          @search-results="kampagneCreateStore.addKundenToEmpfaengers"
        />
        <KundeSelect
          v-model="selectedKunde"
          class="w-72"
          placeholder="Empfänger hinzufügen"
          :override-url="overrideUrl"
          kunde-as-value
        />
      </div>
      <EmpfaengerActions
        v-model:selected-empfaenger-ids="selectedEmpfaengerIds"
      />
    </div>
    <DsAlert
      v-if="ideenSucheIsTooLarge"
      type="warning"
      label="Die Suchergebnisse haben das Empfängerlimit überschritten"
    >
      Es wurden nur die ersten
      {{ erweiterteKundenSucheStore.empfaengerLimit }} Empfänger hinzugefügt.
    </DsAlert>
    <EmpfaengerTable
      v-model:selected-empfaenger-ids="selectedEmpfaengerIds"
      :is-loading="erweiterteKundenSucheStore.isLoading"
    />
  </DsForm>
</template>

<script setup lang="ts">
import { DsAlert, DsForm } from '@demvsystems/design-components';
import { computed, ref, watch } from 'vue';

import KundeSelect from '@/components/formBasisInfo/kunde/KundeSelect.vue';
import useCurrentUser from '@/components/users/useCurrentUser';
import EmpfaengerActions
  from '@/pages/kampagnen/components/createForm/empfaenger/EmpfaengerActions.vue';
import ErweiterteKundenSucheModal
  from '@/pages/kampagnen/components/erweiterteKundensuche/ErweiterteKundensucheModal.vue';
import { useErweiterteKundensucheStore } from '@/pages/kampagnen/stores/erweiterteKundensucheStore';
import { ImNamenVon } from '@/pages/kampagnen/types';
import { KundeResource } from '@/store/resources/types';

import EmpfaengerTable from '../../components/createForm/empfaenger/EmpfaengerTable.vue';
import { useKampagneCreateStore } from '../../stores/kampagneCreateStore';

const {
  user,
} = useCurrentUser();

const kampagneCreateStore = useKampagneCreateStore();
const erweiterteKundenSucheStore = useErweiterteKundensucheStore();

const selectedKunde = ref<KundeResource | null>(null);
const ideenSucheIsTooLarge = ref<boolean>(false);

const overrideUrl = computed(() => (
  kampagneCreateStore.id !== undefined
    ? `kampagnen/${kampagneCreateStore.id}/kunden`
    : 'kunden'
));

const selectedEmpfaengerIds = ref<string[]>([]);

watch(selectedKunde, async (newKunde) => {
  if (newKunde === null) {
    return;
  }

  await kampagneCreateStore.addKundenToEmpfaengers([newKunde]);
  selectedKunde.value = null;
});

const kundensucheMaklerExternalIdProxy = computed<string | undefined>(() => {
  if (kampagneCreateStore.imNamenVon === ImNamenVon.ZustaendigerVermittler) {
    return undefined;
  }

  if (kampagneCreateStore.imNamenVon === ImNamenVon.Mir) {
    return user.value?.attributes.externalId;
  }

  return kampagneCreateStore.senderExternalId ?? undefined;
});

erweiterteKundenSucheStore.initialMaklerExternalId =
  kundensucheMaklerExternalIdProxy.value ?? null;

watch(
  () => kampagneCreateStore.kampagneIdee,
  async (newIdee) => {
    if (newIdee === null) {
      return;
    }

    const kundensucheVorlage = newIdee?.attributes.kundensucheVorlage;
    erweiterteKundenSucheStore.reset(kundensucheVorlage ?? undefined);
    if (kampagneCreateStore.empfaengers.size > 0) {
      return;
    }

    await erweiterteKundenSucheStore.submit();

    if (erweiterteKundenSucheStore.isEmpfaengerLimitReached) {
      erweiterteKundenSucheStore.foundKunden = erweiterteKundenSucheStore.foundKunden.slice(
        0, erweiterteKundenSucheStore.empfaengerLimit,
      );
      ideenSucheIsTooLarge.value = true;
    }

    await kampagneCreateStore.addKundenToEmpfaengers(erweiterteKundenSucheStore.foundKunden);
  }, { immediate: true });
</script>
