<template>
  <DsForm
    data-test="kampagne-create__step3"
    :validation-errors="kampagnenCreateStore.errors"
    class="w-full space-y-5"
    @submit.prevent
  >
    <VorlageAuswahl />
    <KampagneDuzenUndSiezen :tab="activeTab" />
    <FormFiles
      v-if="kampagnenCreateStore.versandart === Versandart.Mail"
    />
    <KampagnenErrorAlert />
  </DsForm>
</template>

<script setup lang="ts">
import { DsForm } from '@demvsystems/design-components';

import FormFiles from '../../components/FormFiles.vue';
import KampagnenErrorAlert from '../../components/KampagnenErrorAlert.vue';
import KampagneDuzenUndSiezen from '../../components/createForm/inhalt/KampagneDuzenUndSiezen.vue';
import VorlageAuswahl from '../../components/createForm/inhalt/VorlageAuswahl.vue';
import { useKampagneCreateStore } from '../../stores/kampagneCreateStore';
import { Versandart } from '../../types';

const kampagnenCreateStore = useKampagneCreateStore();

withDefaults(defineProps<{
  activeTab: 'duzen' | 'siezen';
}>(), {
  activeTab: 'siezen',
});
</script>
