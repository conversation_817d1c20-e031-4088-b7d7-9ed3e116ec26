<template>
  <div class="w-full space-y-7">
    <SendingStatusNotActiveAlert />

    <div class="divide-y">
      <ZusammenfassungZeile
        title="Absender"
        :on-click="() => navigateTo('kampagnen.edit.basis')"
        class="mb-5"
        data-test="kampagne-create__step4__absender"
      >
        <span class="w-full text-base"> {{ kampagnenCreateStore.senderLabel }} </span>
      </ZusammenfassungZeile>
      <ZusammenfassungZeile
        title="Empfänger"
        :on-click="() => navigateTo('kampagnen.edit.recipients')"
        class="py-5"
        data-test="kampagne-create__step4__empfaenger"
      >
        <span class="w-full">{{ kampagnenCreateStore.empfaengers.size }} </span>
      </ZusammenfassungZeile>

      <ZusammenfassungDuzenUndSiezen />
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router';

import SendingStatusNotActiveAlert
  from '@/pages/kampagnen/components/createForm/zusammenfassung/SendingStatusNotActiveAlert.vue';

import ZusammenfassungDuzenUndSiezen from '../../components/createForm/zusammenfassung/ZusammenfassungDuzenUndSiezen.vue';
import ZusammenfassungZeile from '../../components/createForm/zusammenfassung/ZusammenfassungZeile.vue';
import { useKampagneCreateStore } from '../../stores/kampagneCreateStore';

const kampagnenCreateStore = useKampagneCreateStore();
const router = useRouter();

async function navigateTo(route: string) {
  await router.push({
    name: route,
    params: { id: kampagnenCreateStore.id },
  });
}
</script>
