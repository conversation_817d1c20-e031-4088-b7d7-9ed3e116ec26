<template>
  <DsForm
    class="w-full space-y-5"
    :validation-errors="kampagneCreateStore.errors"
    data-test="kampagne-create__step1"
    @submit.prevent
  >
    <div class="flex grow flex-col gap-x-4 gap-y-5">
      <Versandart />
      <Kampagnentitel />
      <VersendenImNamen />
      <VorgaengeAnlegen />
    </div>
  </DsForm>
</template>

<script setup lang="ts">
import { DsForm } from '@demvsystems/design-components';

import Kampagnentitel
  from '@/pages/kampagnen/components/createForm/basisKonfiguration/Kampagnentitel.vue';
import Versandart
  from '@/pages/kampagnen/components/createForm/basisKonfiguration/Versandart.vue';
import VersendenImNamen
  from '@/pages/kampagnen/components/createForm/basisKonfiguration/VersendenImNamen.vue';
import VorgaengeAnlegen
  from '@/pages/kampagnen/components/createForm/basisKonfiguration/VorgaengeAnlegen.vue';
import { useKampagneCreateStore } from '@/pages/kampagnen/stores/kampagneCreateStore';

const kampagneCreateStore = useKampagneCreateStore();
</script>
