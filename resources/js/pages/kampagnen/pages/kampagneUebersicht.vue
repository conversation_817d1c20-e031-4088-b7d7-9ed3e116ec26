<template>
  <div class="overflow-y-auto p-4 sm:p-6">
    <div class="mx-auto w-full grow space-y-7 md:max-w-4xl">
      <KampagneListSkeleton
        v-if="kampagnenStore.isLoading"
      />
      <template v-else>
        <TutorialVideoAlert
          v-if="showVideoAlert"
          @hide-video-alert="hideVideoAlert"
        />
        <DsTabs>
          <DsTab title="Entwürfe">
            <KampagneList
              data-test="kampagne-uebersicht__entwurf-table"
              :status="KampagneStatus.Entwurf"
              :kampagnen="entwurfKampagnen"
              :max-height="maxHeight"
              no-data-text="Klicken Sie oben rechts, um eine neue Kampagne zu erstellen"
            />
          </DsTab>
          <DsTab
            :badge="geplanteKampagnen.length"
            title="Geplant"
          >
            <KampagneList
              data-test="kampagne-uebersicht__geplant-table"
              :status="KampagneStatus.Geplant"
              :max-height="maxHeight"
              :kampagnen="geplanteKampagnen"
              no-data-text="Planen Sie den Versand einer Kampagne für einen späteren Zeitpunkt, um sie hier zu sehen"
            />
          </DsTab>
          <DsTab title="Versendet">
            <KampagneListAbgeschlossen
              data-test="kampagne-uebersicht__abgeschlossen-table"
              :kampagnen="abgeschlosseneKampagnen"
              :max-height="maxHeight"
              no-data-text="Schließen Sie eine Kampagne ab, um sie hier zu sehen"
            />
          </DsTab>
        </DsTabs>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { DsTabs, DsTab } from '@demvsystems/design-components';
import { useLocalStorage } from '@vueuse/core';
import { computed, onMounted } from 'vue';

import KampagneList from '@/pages/kampagnen/components/kampagneUebersicht/KampagneList.vue';
import KampagneListAbgeschlossen
  from '@/pages/kampagnen/components/kampagneUebersicht/KampagneListAbgeschlossen.vue';
import TutorialVideoAlert from '@/pages/kampagnen/components/kampagneUebersicht/TutorialVideoAlert.vue';
import KampagneListSkeleton from '@/pages/kampagnen/components/kampagneUebersicht/skeleton/KampagneListSkeleton.vue';
import { useKampagnenStore } from '@/pages/kampagnen/stores/kampagnenUebersichtStore';
import { KampagneStatus } from '@/pages/kampagnen/types';

const showVideoAlert = useLocalStorage<boolean>('showKampagneTutorialAlert', true);

const hideVideoAlert = () => {
  showVideoAlert.value = false;
};

const kampagnenStore = useKampagnenStore();

const maxHeight = computed(() => {
  return `max(calc(80vh - ${showVideoAlert.value ? 15 : 7}rem), 20rem)`;
});

onMounted(async () => {
  await kampagnenStore.loadKampagnen();
});

const entwurfKampagnen = computed(() =>
  kampagnenStore.kampagnenList
    .filter((kampagne) => kampagne.attributes.status === KampagneStatus.Entwurf),
);

const geplanteKampagnen = computed(() =>
  kampagnenStore.kampagnenList
    .filter((kampagne) => kampagne.attributes.status === KampagneStatus.Geplant),
);
const abgeschlosseneKampagnen = computed(() =>
  kampagnenStore.kampagnenList
    .filter((kampagne) => (
      kampagne.attributes.status === KampagneStatus.Abgeschlossen
      || kampagne.attributes.status === KampagneStatus.InBearbeitung
    )),
);
</script>
