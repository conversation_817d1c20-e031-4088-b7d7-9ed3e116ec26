<template>
  <div class="grow p-4 sm:p-6">
    <section
      class="mx-auto flex size-full min-h-full flex-col md:max-w-4xl lg:space-y-5"
      style="max-height: calc(100vh - 13rem)"
    >
      <header class="-mx-6 flex justify-start px-6">
        <DsStepper
          v-if="!kampagneCreateStore.isLoading"
          :active-step="activeStep"
          @change="changeActiveStep"
        >
          <DsStep
            v-for="({label, icon, step}) in STEP_CONFIG"
            :key="step"
            :step="step"
            :label="label"
            :icon="icon"
            :clickable="isStepClickable(step)"
            :data-test="`kampagne-create__stepper__step${step}`"
          />
        </DsStepper>
        <StepperSkeleton v-else />
      </header>
      <div
        ref="routerViewElement"
        class="-mx-4 h-screen grow overflow-y-auto p-4 sm:-mx-6 sm:p-6 lg:-mx-0 lg:rounded-md lg:border"
      >
        <router-view v-if="!kampagneCreateStore.isLoading" />
        <CreateFormSkeleton v-else />
      </div>
      <footer
        class="-mx-6 flex items-center justify-between space-x-3 px-6"
      >
        <template v-if="!kampagneCreateStore.isLoading">
          <div class="flex space-x-3">
            <DsButton
              variant="secondary"
              @click="routeToList"
            >
              Zur Übersicht
            </DsButton>
            <DsButton
              variant="danger"
              @click="confirmDelete"
            >
              Kampagne löschen
            </DsButton>
          </div>
          <div class="flex space-x-3">
            <DsButton
              v-if="activeStep > 1"
              variant="secondary"
              @click="activeStep -= 1"
            >
              Zurück
            </DsButton>
            <DsButton
              v-if="activeStep !== 4"
              :handler="submitStepAndContinue"
              :disabled="isNotSubmittable"
            >
              Weiter
            </DsButton>
            <ConfirmSendModal
              v-if="activeStep === 4"
              :send-kampagne="sendKampagne"
              :disabled="isNotSubmittable || !canSendKampagne"
            />
          </div>
        </template>
        <FooterSkeleton v-else />
      </footer>
    </section>
  </div>
  <DsModal
    ref="deleteModal"
    size="sm"
    icon="trash"
    title="Kampagne löschen?"
    :variant="ModalVariant.Error"
    cancel-label="Abbrechen"
    confirm-label="Kampagne löschen"
  >
    <div class="space-y-2">
      <p>
        Wollen Sie die Kampagne wirklich löschen?
        Alle eingegebenen Daten gehen dabei verloren.
        Diese Aktion kann nicht mehr rückgängig gemacht werden.
      </p>
      <p>
        Wollen Sie die Daten behalten und später weiterbearbeiten,
        wählen Sie bitte stattdessen die Option „Zur Übersicht“.
      </p>
    </div>
  </DsModal>
  <KampagneIdeenModal
    v-if="id === undefined"
  />
</template>

<script setup lang="ts">
import {
  DsButton,
  DsModal,
  DsStep,
  DsStepper,
  ModalInstance,
  ModalVariant,
} from '@demvsystems/design-components';
import { computed, nextTick, onUnmounted, ref, useTemplateRef, watch } from 'vue';
import { useRouter } from 'vue-router';

import ConfirmSendModal from '@/pages/kampagnen/components/ConfirmSendModal.vue';
import KampagneIdeenModal
  from '@/pages/kampagnen/components/createForm/basisKonfiguration/KampagneIdeenModal.vue';
import CreateFormSkeleton
  from '@/pages/kampagnen/components/createForm/skeleton/CreateFormSkeleton.vue';
import FooterSkeleton from '@/pages/kampagnen/components/createForm/skeleton/FooterSkeleton.vue';
import StepperSkeleton from '@/pages/kampagnen/components/createForm/skeleton/StepperSkeleton.vue';
import {
  STEP_CONFIG,
  useCurrentStepConfig,
} from '@/pages/kampagnen/composables/kampagneCreate/useCurrentStepConfig';
import { useDeleteKampagne } from '@/pages/kampagnen/composables/kampagneCreate/useDeleteKampagne';
import { useKampagnePageTitle } from '@/pages/kampagnen/composables/useKampagnePageTitle';
import { useKampagneCreateStore } from '@/pages/kampagnen/stores/kampagneCreateStore';
import { ImNamenVon, KampagneStatus, Step, Versandart } from '@/pages/kampagnen/types';
import { useUserSettingsStore } from '@/pages/settings/stores/userSettingsStore';

const kampagneCreateStore = useKampagneCreateStore();

const currentStepConfig = useCurrentStepConfig();

const userSettingsStore = useUserSettingsStore();

const { setPageTitle } = useKampagnePageTitle();

const canSendKampagne = computed<boolean>(() => {
  if (kampagneCreateStore.versandart === Versandart.Brief) {
    return true;
  }

  if (kampagneCreateStore.imNamenVon !== ImNamenVon.Mir) {
    return true;
  }

  return userSettingsStore.isMailSendingStatusActive;
});

const activeStep = ref<Step>(1);

const router = useRouter();
const routerViewElement = useTemplateRef<HTMLElement>('routerViewElement');

const props = defineProps<{
  id?: string,
}>();

const isNotSubmittable = computed(() => {
  return kampagneCreateStore.isNotSubmittable(activeStep.value);
});

function isStepClickable(step: Step) {
  return activeStep.value - step > 0;
}

const deleteModal = ref<ModalInstance | null>(null);

const { deleteKampagne } = useDeleteKampagne();

async function cancelAndDelete() {
  if (kampagneCreateStore.id !== undefined) {
    await deleteKampagne(kampagneCreateStore.id);
  }

  kampagneCreateStore.reset();
  await routeToList();
}

function confirmDelete() {
  void deleteModal.value?.open({
    confirmed: cancelAndDelete,
  });
}

async function routeToList() {
  await router.push({ name: 'kampagnen.list' });
}

async function submitStepAndContinue() {
  const success = await kampagneCreateStore.submit(activeStep.value);

  if (success) {
    activeStep.value += 1;
  }
}

async function sendKampagne(): Promise<void> {
  const success = await kampagneCreateStore.sendKampagne();
  if (!success) {
    return Promise.reject();
  }

  if (success && kampagneCreateStore.geplantAt === null) {
    await router.push({
      name: 'kampagnen.show',
      params: { id: kampagneCreateStore.id },
    });
  }

  if (success && kampagneCreateStore.geplantAt !== null) {
    await router.push({
      name: 'kampagnen.list',
    });
  }
}

function initActiveStep() {
  activeStep.value = currentStepConfig.value?.step ?? 1;
}

kampagneCreateStore.reset();

async function changeActiveStep(step: number) {
  activeStep.value = step as Step;

  const route = STEP_CONFIG[step - 1]?.routeName;
  const currentRoute = router.currentRoute.value.name;
  if (route === undefined || route === currentRoute) {
    return;
  }

  await router.push({
    name: route,
    params: {
      id: kampagneCreateStore.id,
    },
  });
}

watch(() => props.id, async (newId) => {
  if (newId === undefined) {
    kampagneCreateStore.reset();
    await nextTick();
    activeStep.value = 1;
    setPageTitle('Neue Kampagne - Basis Konfiguration');
    kampagneCreateStore.isInitialized = true;

    return;
  }

  initActiveStep();
  const success = await kampagneCreateStore.load(newId);

  if (!success) {
    await router.push({ name: 'kampagnen.create.basis' });
  }

  if (kampagneCreateStore.status === KampagneStatus.Geplant) {
    await router.push({
      name: 'kampagnen.list',
    });
  }

  if (kampagneCreateStore.titel !== null) {
    setPageTitle(kampagneCreateStore.titel);
  }

  if (
    kampagneCreateStore.status !== KampagneStatus.Entwurf
    && kampagneCreateStore.status !== KampagneStatus.Geplant
  ) {
    await router.push({
      name: 'kampagnen.show',
      params: { id: newId },
    });
  }

  kampagneCreateStore.isInitialized = true;
}, { immediate: true });

watch(router.currentRoute, () => {
  initActiveStep();
  if (routerViewElement.value !== null) {
    routerViewElement.value?.scrollTo({ top: 0 });
  }

  if (kampagneCreateStore.titel !== null) {
    setPageTitle(kampagneCreateStore.titel);
  }
});

watch([
  () => kampagneCreateStore.id,
  () => kampagneCreateStore.imNamenVon,
  () => kampagneCreateStore.versandart,
], ([id, imNamenVon, versandart]) => {
  if (id === undefined) {
    return;
  }

  if (imNamenVon === ImNamenVon.Mir && versandart === Versandart.Mail) {
    void userSettingsStore.load();
  }
}, { immediate: true });

onUnmounted(() => {
  kampagneCreateStore.reset();
});
</script>
