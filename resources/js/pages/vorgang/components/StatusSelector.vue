<template>
  <DsSelect
    id="status-select"
    v-model="value"
    :data="selectableStatuses"
    :dropdown-width="150"
    placement="bottom"
    inline
    required
  >
    <template #entry="{entry}">
      <VorgangStatusComponent
        :status="entry.value"
      />
    </template>
  </DsSelect>
</template>

<script setup lang="ts">
import { DsSelect } from '@demvsystems/design-components';
import { useVModel } from '@vueuse/core';

import VorgangStatusComponent from '@/components/VorgangStatus.vue';
import { statuses, VorgangStatus } from '@/types';

const props = defineProps<{
  // eslint-disable-next-line vue/no-unused-properties
  modelValue: VorgangStatus
}>();

const emits = defineEmits(['update:modelValue']);

const selectableStatuses = statuses.filter(
  (status) => status.value !== VorgangStatus.Entwurf,
);

const value = useVModel(props, 'modelValue', emits);
</script>

<style>
  #status-select-dropdown button:nth-child(3),
  #status-select-dropdown button:nth-child(4),
  #status-select-dropdown button:nth-child(5),
  #status-select-dropdown button:nth-child(6) {
    display: none;
  }
</style>
