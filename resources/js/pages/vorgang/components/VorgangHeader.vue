<template>
  <header
    v-if="vorgang !== undefined"
    class="relative z-10 flex h-14 items-center justify-between space-x-6 px-5 shadow-sm"
  >
    <VorgangTitle />

    <div
      class="flex items-center space-x-4"
    >
      <VorgangsnummerTag
        :nummer="vorgang.attributes.vorgangsnummer"
        data-test="vorgang__vorgangsnummer"
        class="inline-flex"
      />
      <VorgangsartTag
        :art="vorgang.attributes.vorgangsart"
        data-test="vorgang__art"
        class="inline-flex"
      />
      <StatusSelector
        v-model="vorgang.attributes.status"
        data-test="vorgang__status"
      />
    </div>
  </header>
</template>

<script setup lang="ts">
import VorgangsartTag from '@/components/tags/VorgangsartTag.vue';
import VorgangsnummerTag from '@/components/tags/VorgangsnummerTag.vue';
import { useVorgangFromRoute } from '@/store/resources/composition';

import StatusSelector from './StatusSelector.vue';
import VorgangTitle from './VorgangTitle.vue';

const vorgang = useVorgangFromRoute();
</script>
