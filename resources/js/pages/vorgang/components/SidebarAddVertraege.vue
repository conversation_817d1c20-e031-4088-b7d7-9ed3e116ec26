<template>
  <div>
    <a
      class="group flex
        cursor-pointer items-center rounded-md border p-2
        text-sm leading-tight
      "
      @click.prevent="showModal = true"
    >

      <div
        class="grow"
      >
        <div
          class="font-medium"
        >Verträge hinzufügen</div>
      </div>

      <IconBtn
        title="Diesem Vorgang Verträge hinzufügen"
        icon="plus"
        class="ml-1 flex-none"
        grouped
      />
    </a>
    <AddVertraegeModal
      :show="showModal"
      @close="showModal = false"
      @confirm="showModal = false"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

import IconBtn from '@/components/IconBtn.vue';
import AddVertraegeModal from '@/pages/vorgang/components/Modals/AddVertraegeModal.vue';

const showModal = ref(false);
</script>
