<template>
  <div class="text-center text-gray-900">
    <div class="mt-6">
      <VorgangNotFoundSVG class="mx-auto w-2/3" />
    </div>
    <div class="text-xl">
      Der Vorgang
      <span
        v-if="vorgangId"
        class="font-bold text-gray-800"
      >
        #{{ vorgangId }}
      </span>
      wurde nicht gefunden.
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useRoute } from 'vue-router';

import VorgangNotFoundSVG from '../../../../svg/VorgangNotFound.vue';

const route = useRoute();
const vorgangId = computed(() => (
  'vorgangsnummer' in route.params ? `${route.params.vorgangsnummer}` : undefined
));
</script>
