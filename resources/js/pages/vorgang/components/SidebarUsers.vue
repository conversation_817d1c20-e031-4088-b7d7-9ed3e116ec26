<template>
  <div>
    <UserSelectMenu
      :preselected-users="preselectedUsers"
      :user-type="userType"
      :disabled="isUntervorgang"
    >
      <template #button>
        <SidebarCta
          :title="userSelectCtaTitle"
          :label="userType"
          :disabled="isUntervorgang"
          action-icon="cog"
        />
      </template>
    </UserSelectMenu>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

import { useVorgangFromRoute } from '@/store/resources/composition';
import { UserResource } from '@/store/resources/types';

import UserSelectMenu from './SelectMenu/UserSelectMenu.vue';
import SidebarCta from './SidebarCta.vue';

withDefaults(defineProps<{
  preselectedUsers: UserResource[],
  userType: 'bearbeiter' | 'beobachter',
}>(), {
  preselectedUsers: () => ([]),
});

const vorgang = useVorgangFromRoute();

const isUntervorgang = computed(() => vorgang.value?.attributes.isUntervorgang);
const userSelectCtaTitle = computed(
  () => isUntervorgang.value
    ? 'Bearbeiter/Beobachter können nur in der Vorgangsgruppe geändert werden'
    : 'Bearbeiter/Beobachter hinzufügen oder entfernen',
);
</script>
