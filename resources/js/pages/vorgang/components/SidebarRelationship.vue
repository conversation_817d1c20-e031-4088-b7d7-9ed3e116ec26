<template>
  <component
    :is="externalLink !== '' ? 'a' : 'div'"
    :href="externalLink"
    :class="{
      group: !shouldNotGroup,
      'cursor-pointer': externalLink !== '',
    }"
    class="
      flex items-center rounded-md border p-2
      text-sm leading-tight
    "
    target="_blank"
    rel="noopener noreferrer"
  >
    <FallbackImage
      v-if="logo"
      class="mr-2 h-8 w-10 flex-none"
      :src="logo"
      alt="Gesellschaftslogo"
    />

    <div
      class="grow"
      :title="externalLink !== '' ? 'In einem neuen Tab öffnen' : undefined"
    >
      <div class="text-gray-700">
        {{ relationshipType }}
      </div>
      <div
        class="font-medium"
        v-text="title"
      />
    </div>

    <IconBtn
      v-if="externalLink !== ''"
      title="In einem neuen Tab öffnen"
      icon="external-link-alt"
      class="ml-1 flex-none"
      grouped
    />
  </component>
</template>

<script setup lang="ts">
import FallbackImage from '@/components/FallbackImage.vue';
import IconBtn from '@/components/IconBtn.vue';

withDefaults(defineProps<{
  relationshipType?: string,
  title?: string,
  externalLink?: string,
  logo?: string,
  shouldNotGroup?: boolean,
}>(), {
  relationshipType: '',
  title: '',
  externalLink: '',
  logo: '',
});
</script>
