<template>
  <SelectMenu
    :selected-items="preselectedUsers"
    :items="visibleFirmenMembers"
    :fuse-options="fuseOptions"
    :loading="isLoading"
    :disabled="disabled"
    @open="loadVisibleFirmenMembers"
    @select="handleSelect"
    @deselect="handleDeselect"
  >
    <template #button>
      <slot name="button" />
    </template>

    <template #item="{item}">
      <UserTag
        v-if="!isLoading"
        :user="item"
      />
    </template>

    <template #selected-items="{items}">
      <UserList
        v-if="items && items.length > 0"
        :users="items as UserResource[]"
      />
    </template>
  </SelectMenu>
</template>

<script setup lang="ts">
import { computed } from 'vue';

import { del, get, post } from '@/api';
import SelectMenu from '@/components/SelectMenu/SelectMenu.vue';
import UserTag from '@/components/tags/UserTag.vue';
import UserList from '@/components/users/UserList.vue';
import useHierarchy from '@/components/users/useHierarchy';
import { injectStore, useVorgangFromRoute } from '@/store/resources/composition';
import { UserResource, VorgangResource } from '@/store/resources/types';

const props = withDefaults(defineProps<{
  preselectedUsers: UserResource[],
  userType: 'bearbeiter' | 'beobachter',
}>(), {
  preselectedUsers: () => ([]),
});
defineEmits<{
  (event: 'selectedItems'): void
}>();

const vorgang = useVorgangFromRoute();
const store = injectStore();
const disabled = false;
const {
  isLoading,
  visibleFirmenMembers,
  loadVisibleFirmenMembers,
} = useHierarchy();

const updateStore = async () => {
  if (vorgang.value === undefined) {
    return;
  }

  const response = await get<VorgangResource>(`/vorgaenge/${vorgang.value.id}`, {
    include: [
      'timeline.element',
      'timeline.owner',
      'participants',
    ],
    sort: [{
      name: 'updatedAt',
    }],
  });

  store.update(response.data);
};

const requestUrl = computed(() => `vorgaenge/${vorgang?.value?.id}/participants/${props.userType}`);

const handleSelect = async (event: UserResource) => {
  if (vorgang.value !== undefined) {
    await post<VorgangResource>(
      requestUrl.value,
      {
        data: {
          type: 'vorgang_participants',
          relationships: {
            user: {
              data: {
                type: 'users',
                id: parseInt(event.id, 10),
              },
            },
          },
        },
      },
    );
    await updateStore();
  }
};

const handleDeselect = async (event: UserResource) => {
  await del<VorgangResource>(
    `${requestUrl.value}/${event.id}`,
  );
  await updateStore();
};

const fuseOptions = {
  keys: ['data.attributes.name'],
  threshold: 0.4,
};
</script>
