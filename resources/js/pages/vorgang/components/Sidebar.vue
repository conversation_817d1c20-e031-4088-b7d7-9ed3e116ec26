<template>
  <div class="space-y-7">
    <div
      class="space-y-1.5"
    >
      <SidebarRelationship
        v-if="kunde"
        relationship-type="Kunde"
        :title="kunde.attributes?.name"
        :external-link="kunde.links?.external"
        data-test="vorgang__sidebar__kunde"
      />
      <SidebarRelationship
        v-if="gesellschaft"
        relationship-type="Gesellschaft"
        :title="gesellschaft.attributes?.name"
        :logo="gesellschaft.links?.logo"
        :external-link="gesellschaft.links?.external"
        data-test="vorgang__sidebar__gesellschaft"
      />
      <SidebarRelationship
        v-if="vertriebsweg"
        relationship-type="Kommunikation über"
        :title="vertriebsweg.attributes?.name"
        :logo="vertriebsweg.links?.logo"
        :external-link="vertriebsweg.links?.external"
        data-test="vorgang__sidebar__vertriebsweg"
      />
      <SidebarRelationship
        v-for="vertrag in vertraege"
        :key="vertrag.id"
        :relationship-type="`Vertrag${getSparteName(vertrag)}`"
        :title="vertrag.attributes.vertragsnummer"
        :external-link="vertrag.links?.external"
        data-test="vorgang__sidebar__vertrag"
      />
      <SidebarAddVertraege
        v-if="vertraege.length === 0 && kunde"
        data-test="vorgang__sidebar__add-vertrag"
      />
      <SidebarRelationship
        v-if="sparte"
        relationship-type="Sparte"
        :title="sparte.attributes?.displayName"
        :logo="sparte.links?.logo"
        :external-link="sparte.links?.external"
        data-test="vorgang__sidebar__sparte"
      />
      <SidebarRelationship
        v-if="bezug"
        relationship-type="Bezug"
        :title="bezug.attributes?.benennung"
        :external-link="bezug.attributes?.url ?? ''"
        data-test="vorgang__sidebar__bezug"
      />
    </div>
    <SidebarUser
      user-type="bearbeiter"
      :preselected-users="bearbeiter"
      data-test="vorgang__sidebar__bearbeiter"
    />

    <SidebarUser
      user-type="beobachter"
      :preselected-users="beobachter"
      data-test="vorgang__sidebar__beobachter"
    />

    <SidebarItem
      v-if="files.length > 0"
      label="Dokumente"
      title="Hier werden sämtliche Dokumente aufgeführt, welche innerhalb des Vorgangs verschickt, empfangen oder hochgeladen wurden. Über die Funktion 'Bestehende Dokumente' in den Aktionen können diese Dokumente jederzeit wiederverwendet werden."
      data-test="vorgang__sidebar__dokumente"
    >
      <FileList :files="files" />
    </SidebarItem>

    <component
      :is="element"
      v-for="(element, domainName) in elemente"
      :key="domainName"
    />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

import FileList from '@/components/fileList/FileList.vue';
import { sidebar as elemente } from '@/domains';
import SidebarAddVertraege from '@/pages/vorgang/components/SidebarAddVertraege.vue';
import { injectStore, useVorgangFromRoute } from '@/store/resources/composition';
import type { FileResource, ParticipantResource, VertragResource } from '@/store/resources/types';
import { ParticipantType, UserResource } from '@/store/resources/types';

import SidebarItem from './SidebarItem.vue';
import SidebarRelationship from './SidebarRelationship.vue';
import SidebarUser from './SidebarUsers.vue';

const store = injectStore();
const vorgang = useVorgangFromRoute();
const kunde = computed(() => store.kunden.findRelated(vorgang.value?.relationships?.kunde));
const gesellschaft = computed(
  () => store.gesellschaften.findRelated(vorgang.value?.relationships?.gesellschaft),
);
const vertriebsweg = computed(
  () => store.gesellschaften.findRelated(vorgang.value?.relationships?.vertriebsweg),
);
const vertraege = computed(
  () => store.vertraege.findAllRelated(vorgang.value?.relationships?.vertraege),
);
const participants = computed(
  () => store.participants.findAllRelated(vorgang.value?.relationships?.participants),
);
const hasParticipants = (userId: string, participantType: ParticipantType): boolean => {
  return participants.value.filter((participant: ParticipantResource) =>
    (participant.attributes.participantType === participantType
      && participant.attributes.userId == userId),
  ).length > 0;
};

const beobachter = computed(
  () => store.users.getAll().filter((user: UserResource) =>
    hasParticipants(user.id, ParticipantType.Beobachter)),
);
const bearbeiter = computed(
  () => store.users.getAll().filter((user: UserResource) =>
    hasParticipants(user.id, ParticipantType.Bearbeiter)),
);
const sparte = computed(() => store.sparten.findRelated(vorgang.value?.relationships?.sparte));
const bezug = computed(() => store.bezuege.findRelated(vorgang.value?.relationships?.bezug));

const files = computed(
  () => store.files.getAll()
    .filter((
      resource: FileResource,
      index: number,
      self: FileResource[],
    ) => (
      // filter duplicate files
      index === self.findIndex(
        ({ attributes }) => attributes.size === resource.attributes.size
          && attributes.name === resource.attributes.name,
      )
    ))
    .sort((a, b) => (
      a.attributes.name.localeCompare(b.attributes.name)
    )),
);

const getSparteName = (vertrag: VertragResource) => {
  const sparteName = store.sparten.findRelated(
    vertrag.relationships?.sparte,
  )?.attributes?.displayName;

  return sparteName ? ` (${sparteName})` : '';
};
</script>
