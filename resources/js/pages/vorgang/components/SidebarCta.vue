<template>
  <div
    ref="header"
    :class="{[disabled ? 'group cursor-not-allowed' : 'group cursor-pointer']: hasAction}"
    class="flex h-6 items-center justify-between pb-0.5 focus:outline-none"
    data-test="vorgang__sidebar__cta"
    @click="actionHandler"
  >
    <div
      class="
        mr-2 flex items-center text-left text-xs font-semibold uppercase
        leading-none tracking-wide text-gray-700 focus:outline-none
      "
    >
      {{ label }}
      <slot name="label-addon" />
    </div>

    <button
      v-if="hasAction"
      type="button"
      class="
        size-6 rounded text-sm font-medium leading-none
        transition duration-100 ease-out focus:outline-none
      "
      :class="[
        disabled
          ? 'cursor-not-allowed text-gray-300 focus:bg-gray-100'
          : 'text-blue-500 focus:bg-blue-200 active:bg-blue-100 group-hover:bg-blue-100',
      ]"
      :disabled="disabled"
    >
      <DsIcon :name="actionIcon ?? ''" />
    </button>

    <slot
      name="action"
      :reference="header"
    />
  </div>
</template>

<script setup lang="ts">
import { DsIcon } from '@demvsystems/design-components';
import { computed, useTemplateRef } from 'vue';

const props = withDefaults(defineProps<{
  label: string,
  actionIcon?: string,
  actionHandler?: (payload: MouseEvent) => void,
  disabled?: boolean,
}>(), {
  actionHandler: () => {},
  actionIcon: '',
});

const header = useTemplateRef<HTMLElement>('header');
const hasAction = computed(() => props.actionIcon !== '');
</script>
