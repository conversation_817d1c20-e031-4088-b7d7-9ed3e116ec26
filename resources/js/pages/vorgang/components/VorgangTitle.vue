<template>
  <div
    v-if="!isEditing"
    class="-my-1.5 overflow-x-auto"
  >
    <div
      class="my-1 flex items-center space-x-1.5"
    >
      <h2
        :title="vorgang?.attributes.titel"
        class="truncate text-xl font-semibold text-gray-800"
        data-test="vorgang__titel"
      >
        {{ vorgang?.attributes.titel }}
      </h2>

      <DsButton
        icon="pen"
        variant="clear"
        size="sm"
        data-test="vorgang__edit-titel__button"
        class="pr-1"
        @click="isEditing = true"
      />
    </div>

    <div
      v-if="vorgangstypTitel !== null"
      :title="vorgangstypTitel"
      data-test="vorgang__vorgangstyp"
      class="-mt-1.5 mb-1.5 truncate text-sm font-medium tracking-wide text-gray-500"
    >
      {{ vorgangstypTitel }}
    </div>
  </div>

  <form
    v-else
    class="flex grow items-center space-x-2"
    @keydown.enter.prevent="submitNewTitle"
    @keydown.esc="abortEditing"
  >
    <DsInput
      v-model="editedTitle"
      class="grow py-1"
      data-test="vorgang__titel-form__input"
      immediate
      autofocus
    />
    <DsButton
      :disabled="!isEditedTitleValid"
      icon="check"
      variant="primary"
      data-test="vorgang__titel-form__submit-button"
      @click="submitNewTitle"
    />
    <DsButton
      icon="xmark"
      variant="secondary"
      data-test="vorgang__titel-form__abort-button"
      @click="abortEditing"
    />
  </form>
</template>

<script setup lang="ts">
import { DsButton, DsInput } from '@demvsystems/design-components';
import { computed, ref } from 'vue';

import { useVorgangFromRoute } from '@/store/resources/composition';
import { useVorgangstypenStore } from '@/stores/vorgangstypenStore';

const vorgang = useVorgangFromRoute();
const vorgangstypenStore = useVorgangstypenStore();

const isEditing = ref(false);
const editedTitle = ref(vorgang.value?.attributes.titel ?? '');

const vorgangstyp = computed(() => {
  const vorgangstypId = vorgang.value?.relationships?.vorgangTyp?.data?.id;

  if (vorgangstypId === undefined) {
    return undefined;
  }

  return vorgangstypenStore.findById(vorgangstypId);
});

const vorgangstypTitel = computed(
  () => vorgang.value?.attributes.titel === vorgangstyp.value?.attributes.titel
    ? null
    : vorgangstyp.value?.attributes.titel);

const isEditedTitleValid = computed(() => (
  editedTitle.value.length > 0 && editedTitle.value.length <= 255
));

function abortEditing() {
  isEditing.value = false;
  editedTitle.value = vorgang.value?.attributes.titel ?? '';
}

function submitNewTitle() {
  if (vorgang.value === undefined) {
    return;
  }

  isEditing.value = false;
  vorgang.value.attributes.titel = editedTitle.value;
}
</script>
