<template>
  <div>
    <!-- Loading skeleton -->

    <!-- Header -->
    <div class="flex h-14 items-center justify-between px-5 shadow-sm">
      <DsSkeleton class="h-6 w-72 rounded" />
      <div class="flex items-center space-x-3">
        <DsSkeleton class="h-6 w-32 rounded" />
        <DsSkeleton class="h-6 w-40 rounded" />
        <DsSkeleton class="h-6 w-24 rounded" />
      </div>
    </div>

    <!-- Content -->
    <div class="flex space-x-5 p-5">
      <!-- Timeline -->
      <div class="grow">
        <!-- Text entry 1 -->
        <div class="relative grow pb-6">
          <span
            aria-hidden="true"
            class="absolute left-4 top-7 -ml-px h-full w-0.5 bg-gray-200"
          />

          <div class="relative flex space-x-3">
            <div class="mt-5 size-8 shrink-0 rounded-full bg-white p-1.5">
              <DsSkeleton
                class="size-full rounded-full"
                gradient
              />
            </div>

            <div class="flex h-52 w-full flex-col rounded-lg border p-4">
              <div class="flex items-center">
                <DsSkeleton class="mr-3 size-10 rounded-full" />
                <div>
                  <DsSkeleton class="h-4 w-40 rounded-md" />
                  <DsSkeleton class="mt-1 h-3 w-24 rounded-md" />
                </div>
              </div>

              <DsSkeleton
                class="mt-3 grow rounded-lg"
                gradient
              />
            </div>
          </div>
        </div>

        <!-- Inline entry 1 -->
        <div class="relative grow pb-6">
          <span
            aria-hidden="true"
            class="absolute left-4 -ml-px h-full w-0.5 bg-gray-200 opacity-75"
          />

          <div class="relative flex space-x-3">
            <div class="size-8 shrink-0 rounded-full bg-white p-1.5">
              <DsSkeleton class="size-full rounded-full opacity-75" />
            </div>

            <div class="mt-1 grow space-x-2 opacity-75">
              <DsSkeleton class="inline-block h-3 w-40 rounded" />
              <DsSkeleton class="inline-block h-3 w-24 rounded" />
              <DsSkeleton class="inline-block h-3 w-5 rounded" />
              <DsSkeleton class="inline-block h-3 w-12 rounded" />
              <DsSkeleton class="inline-block h-3 w-40 rounded" />
              <DsSkeleton class="inline-block h-3 w-12 rounded" />
            </div>
          </div>
        </div>

        <!-- Inline entry 2 -->
        <div class="relative grow pb-6 opacity-50">
          <span
            aria-hidden="true"
            class="absolute left-4 -ml-px h-full w-0.5 bg-gray-200"
          />

          <div class="relative flex space-x-3">
            <div class="size-8 shrink-0 rounded-full bg-white p-1.5">
              <DsSkeleton class="size-full rounded-full" />
            </div>

            <div class="mt-1 grow space-x-2">
              <DsSkeleton class="inline-block h-3 w-5 rounded" />
              <DsSkeleton class="inline-block h-3 w-40 rounded" />
              <DsSkeleton class="inline-block h-3 w-24 rounded" />
              <DsSkeleton class="inline-block h-3 w-12 rounded" />
            </div>
          </div>
        </div>

        <!-- Text entry 2 -->
        <div class="relative grow pb-6 opacity-25">
          <span
            aria-hidden="true"
            class="absolute left-4 -ml-px h-full w-0.5 bg-gray-200"
          />

          <div class="relative flex space-x-3">
            <div class="mt-5 size-8 shrink-0 rounded-full bg-white p-1.5">
              <DsSkeleton
                class="size-full rounded-full"
                gradient
              />
            </div>

            <div class="flex h-40 w-full flex-col rounded-lg border p-4">
              <div class="flex items-center">
                <DsSkeleton class="mr-3 size-10 rounded-full" />
                <div>
                  <DsSkeleton class="h-4 w-40 rounded-md" />
                  <DsSkeleton class="mt-1 h-3 w-24 rounded-md" />
                </div>
              </div>

              <DsSkeleton
                class="mt-3 grow rounded-lg"
                gradient
              />
            </div>
          </div>
        </div>
      </div>

      <!-- Sidebar -->
      <div class="w-80 space-y-5">
        <div class="space-y-1">
          <DsSkeleton class="h-12 rounded" />
          <DsSkeleton class="h-12 rounded" />
        </div>

        <div class="space-y-2 opacity-75">
          <DsSkeleton class="h-4 w-1/2 rounded" />
          <DsSkeleton class="h-16 rounded" />
        </div>

        <div class="space-y-2 opacity-50">
          <DsSkeleton class="h-4 w-1/3 rounded" />
          <DsSkeleton class="h-20 rounded" />
        </div>

        <div class="space-y-2 opacity-25">
          <DsSkeleton class="h-4 w-2/3 rounded" />
          <DsSkeleton class="h-32 rounded" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { DsSkeleton } from '@demvsystems/design-components';
</script>
