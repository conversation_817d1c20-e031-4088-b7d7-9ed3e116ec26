<template>
  <DsAlert
    v-if="hasAlert"
    id="vorgang-info-alert"
    type="error"
    label="Ein Fehler ist aufgetreten"
  >
    <div v-html="alertText" />
  </DsAlert>
</template>

<script setup lang="ts">
import { DsAlert } from '@demvsystems/design-components';

import { useVorgangErrorAlert } from '@/pages/vorgang/composables/useVorgangErrorAlert';

const {
  alertText,
  hasAlert,
} = useVorgangErrorAlert();
</script>

<style>
#vorgang-info-alert ul {
  @apply list-disc list-inside;
}
</style>
