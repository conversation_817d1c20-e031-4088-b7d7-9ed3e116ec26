<template>
  <DsModal
    :show="show"
    title="Courtage erfassen"
    hide-buttons
  >
    Möchten Sie zum angelegten Vorgang eine Courtage erfassen?

    <p class="mt-2">
      Sie können die Courtage auch nachträglich unten
      über die Aktion “Courtage erfassen” erfassen.
    </p>

    <div class="mt-4 flex justify-end space-x-3">
      <DsButton
        size="lg"
        variant="secondary"
        @click="emits('close')"
      >
        Jetzt nicht
      </DsButton>
      <DsButton
        size="lg"
        :href="courtageErfassenUrl"
        external
        @click="emits('close')"
      >
        Courtage erfassen
      </DsButton>
    </div>
  </DsModal>
</template>

<script setup lang="ts">
import { DsButton, DsModal } from '@demvsystems/design-components';
import { computed } from 'vue';

const props = defineProps<{
  show: boolean,
  vorgangId: string,
}>();

const emits = defineEmits<{
  (event: 'close'): void;
}>();

const courtageErfassenUrl = computed(() => (
  `/api/vorgaenge/${props.vorgangId}/courtageerfassung`
));
</script>
