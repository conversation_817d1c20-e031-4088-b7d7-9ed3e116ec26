<template>
  <DsModal
    :show="show"
    title="Verträge hinzufügen"
    action-required
    hide-buttons
    :variant="step === 0 ? ModalVariant.Info : ModalVariant.Warning"
    :icon="step === 0 ? '' : 'circle-exclamation'"
  >
    <div data-test="vorgang__add-vertrag__modal">
      <template v-if="step === 0">
        Achtung: Nach dem Hinzufügen eines Vertrags/mehrerer Verträge
        können die Verträge in diesem Vorgang nicht mehr geändert werden.
        <div class="mt-3">
          <DsForm @submit.prevent>
            <Vertraege
              :model-value="vertraege"
              :kunde-id="vorgang?.relationships?.kunde?.data?.id"
              @update:model-value="vertraege = $event"
            />
          </DsForm>
        </div>
        <div class="mt-4 flex justify-end space-x-3">
          <DsButton
            size="lg"
            variant="secondary"
            @click="closeModal"
          >
            Abbrechen
          </DsButton>
          <DsButton
            size="lg"
            :disabled="vertraege.length === 0"
            @click="step = 1"
          >
            Übernehmen
          </DsButton>
        </div>
      </template>

      <template v-else>
        Sind Sie sich sicher, dass Sie die ausgewählten Verträge dem Vorgang zuordnen wollen?
        Diese Aktion kann nicht mehr rückgängig gemacht werden.
        <div class="mt-3 flex items-center justify-between text-sm font-medium text-gray-700">
          {{ vertraege.length === 1 ? 'Ausgewählter Vertrag' : 'Ausgewählte Verträge' }}
        </div>
        <ul class="mt-1 max-h-60 list-inside list-disc overflow-y-auto">
          <li
            v-for="vertrag in vertraege"
            :key="vertrag.id"
          >
            <span
              class="font-semibold"
              v-text="vertrag.attributes.vertragsnummer"
            />
          </li>
        </ul>
        <div class="mt-4 flex justify-end space-x-3">
          <DsButton
            size="lg"
            variant="secondary"
            @click="step = 0"
          >
            Zurück
          </DsButton>
          <DsButton
            size="lg"
            :disabled="vertraege.length === 0"
            :handler="sendRequest"
          >
            Übernehmen
          </DsButton>
        </div>
      </template>
    </div>
  </DsModal>
</template>

<script setup lang="ts">
import { DsButton, DsForm, DsModal, ModalVariant } from '@demvsystems/design-components';
import { ref } from 'vue';

import { post } from '@/api';
import Vertraege from '@/components/formBasisInfo/vertrag/Vertraege.vue';
import { injectStore, useVorgangFromRoute } from '@/store/resources/composition';
import { eventBus } from '@/store/resources/store';
import { VertragResource, VorgangResource } from '@/store/resources/types';

import { reloadStoreFromCurrentVorgang } from '../Timeline/Actions/storeService';

defineProps<{
  show: boolean,
}>();

const emit = defineEmits<{
  (event: 'close'): void;
  (event: 'confirm'): void;
}>();

const step = ref(0);

const vorgang = useVorgangFromRoute();
const store = injectStore();
const vertraege = ref<VertragResource[]>([]);

const closeModal = () => {
  emit('close');
  step.value = 0;
  vertraege.value = [];
};

const sendRequest = async () => {
  if (vorgang.value === undefined) {
    return Promise.reject();
  }

  if (vertraege.value.length === 0) {
    return Promise.reject();
  }

  try {
    await post<VorgangResource>(
      `/vorgaenge/${vorgang.value?.id}/vertraege`,
      {
        data: vertraege.value.map((vertrag: VertragResource) => {
          return {
            id: vertrag.id,
            type: vertrag.type,
          };
        }),
      },
    );

    await reloadStoreFromCurrentVorgang(store, vorgang.value?.id);
    closeModal();
  } catch (e) {
    const msg = 'Verträge konnten nicht hinzugefügt werden';
    eventBus.emit('error', msg);

    throw Error(msg);
  }
};
</script>
