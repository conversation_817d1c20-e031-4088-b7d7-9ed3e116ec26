<template>
  <DsModal
    :show="show"
    title="Schaden erfassen"
    hide-buttons
  >
    Möchten Si<PERSON> zum angelegten Vorgang einen Schaden in der Kundenakte erfassen?

    <p class="mt-2">
      <PERSON>e können den Schaden auch nachträglich unten
      über die Aktion “Schaden erfassen” erfassen.
    </p>

    <div class="mt-4 flex justify-end space-x-3">
      <DsButton
        size="lg"
        variant="secondary"
        @click="emits('close')"
      >
        Jetzt nicht
      </DsButton>
      <DsButton
        size="lg"
        :href="schadenErfassenUrl"
        external
        @click="emits('close')"
      >
        Schaden erfassen
      </DsButton>
    </div>
  </DsModal>
</template>

<script setup lang="ts">
import { DsButton, DsModal } from '@demvsystems/design-components';
import { computed } from 'vue';

const props = defineProps<{
  show: boolean,
  vorgangId: string,
}>();

const emits = defineEmits<{
  (event: 'close'): void;
}>();

const schadenErfassenUrl = computed(() => (
  `/api/vorgaenge/${props.vorgangId}/schadenerfassung`
));
</script>
