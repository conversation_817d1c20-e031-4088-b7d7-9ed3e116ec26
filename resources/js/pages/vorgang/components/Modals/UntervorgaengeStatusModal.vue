<template>
  <DsModal
    :show="show"
    title="Noch offene Untervorgänge ebenfalls schließen?"
    icon="fa-folder-tree"
    cancel-label="Nein"
    confirm-label="Ja, offene Untervorgänge schließen"
    @confirm="emits('confirm')"
    @close="emits('close')"
  >
    Diese Vorgangsgruppe beinhaltet womöglich noch offene Untervorgänge.
    Möchten Sie diese ebenfalls auf den Status „Erledigt“ setzen?
  </DsModal>
</template>

<script setup lang="ts">
import { DsModal } from '@demvsystems/design-components';

defineProps<{
  show: boolean,
}>();

const emits = defineEmits<{
  (event: 'close'): void;
  (event: 'confirm'): void;
}>();
</script>
