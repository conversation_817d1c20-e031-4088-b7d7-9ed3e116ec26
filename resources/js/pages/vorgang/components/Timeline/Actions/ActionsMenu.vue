<template>
  <div class="grid grid-cols-1 gap-4 xl:grid-cols-2">
    <div
      v-for="groupedAction in groupedActions"
      :key="groupedAction.group"
      class="flex flex-auto flex-col space-y-1"
    >
      <div
        class="mb-2 flex text-xs font-semibold uppercase leading-none tracking-wide text-gray-700"
        v-text="groupedAction.group"
      />

      <DsButton
        v-for="action in groupedAction.actions"
        :key="getLabel(action)"
        :icon="action.icon"
        :disabled="action.disabled"
        text-align="left"
        class="truncate"
        variant="secondary"
        @click="handleClick(action)"
      >
        {{ getLabel(action) }}
      </DsButton>
    </div>
  </div>
</template>

<script setup lang="ts">
import { DsButton } from '@demvsystems/design-components';
import { computed, markRaw } from 'vue';

import { StackComponent } from '@/composables/navigationStack/types';
import useVorgangAnlegenModal from '@/pages/vorgangAnlegen/composables/useVorgangAnlegenModal';
import { injectStore, useVorgangFromRoute } from '@/store/resources/composition';
import { eventBus } from '@/store/resources/store';
import { useVorgangstypenStore } from '@/stores/vorgangstypenStore';
import { VorgangStatus } from '@/types/Vorgang';

import Erinnerung from './Erinnerung.vue';
import Kommentar from './Kommentar.vue';
import Korrespondenz from './Korrespondenz.vue';
import Mahnung from './Mahnung.vue';
import Nachricht from './Nachricht.vue';
import Notiz from './Notiz.vue';
import {
  Action,
  GroupedActions,
  isComponentAction,
  isExternalAction,
  isFolgevorgangAction,
} from './types';

const vorgangstypenStore = useVorgangstypenStore();

const vorgang = useVorgangFromRoute();

const store = injectStore();

const vorgangstyp = computed(() => {
  const vorgangstypId = vorgang.value?.relationships?.vorgangTyp?.data?.id;

  if (vorgangstypId === undefined) {
    return undefined;
  }

  return vorgangstypenStore.findById(vorgangstypId);
});
const vertraege = computed(() => (
  store.vertraege.findAllRelated(vorgang.value?.relationships?.vertraege)
));

const isCourtageerfassungPossible = computed(() => (
  vorgangstyp.value?.attributes.titel === 'Antrag einreichen'
));

const isSchadenerfassungPossible = computed(() => (
  vorgangstyp.value?.attributes.titel === 'Schadenmeldung'
  && vorgang.value?.relationships?.kunde !== undefined
  && !vertraege.value.some((vertrag) => vertrag.attributes.isSchaden)
));

const groupedActions = computed(() => <GroupedActions[]>[
  {
    group: 'Kommunikation',
    actions: [
      {
        component: {
          title: 'Korrespondenz hochladen',
          component: markRaw(Korrespondenz),
          componentName: 'Korrespondenz',

        },
        icon: 'upload',
      },
      {
        component: {
          title: 'Nachricht verfassen',
          componentName: 'Nachricht',
          component: markRaw(Nachricht),
        },
        icon: 'plus',
      },
      {
        component: {
          title: 'Erinnerung versenden',
          componentName: 'Erinnerung',
          component: markRaw(Erinnerung),
        },
        icon: 'alarm-clock',
        disabled: vorgang.value?.attributes.status === VorgangStatus.Mahnung1
          || vorgang.value?.attributes.status === VorgangStatus.Mahnung2
          || vorgang.value?.attributes.status === VorgangStatus.Erinnerung2
          || vorgang.value?.attributes.isGroup,
      },
      {
        component:{
          title: 'Mahnung versenden',
          componentName: 'Mahnung',
          component: markRaw(Mahnung),
        },
        icon: 'exclamation-triangle',
        disabled: vorgang.value?.attributes.status === VorgangStatus.Mahnung2
          || vorgang.value?.attributes.isGroup,
      },
    ],
  },
  {
    group: 'Verwaltung',
    actions: [
      {
        component: {
          title: 'Kommentar verfassen',
          componentName: 'Kommentar',
          component: markRaw(Kommentar),
        },
        icon: 'comment',
      },
      {
        component: {
          title: 'Gesprächsnotiz',
          componentName: 'Notiz',
          component: markRaw(Notiz),
        },
        icon: 'comment-alt-edit',
      },
      {
        label: 'Folgevorgang erstellen',
        icon: 'fast-forward',
      },
      ...(isCourtageerfassungPossible.value ? [{
        label: 'Courtage erfassen',
        icon: 'external-link-alt',
        href: `/api/vorgaenge/${vorgang.value?.id}/courtageerfassung`,
      }] : []),
      ...(isSchadenerfassungPossible.value ? [{
        label: 'Schaden erfassen',
        icon: 'external-link-alt',
        href: `/api/vorgaenge/${vorgang.value?.id}/schadenerfassung`,
      }] : []),
    ],
  },
]);

const getLabel = (action: Action) => {
  if (isFolgevorgangAction(action) || isExternalAction(action)) {
    return action.label;
  }

  if (isComponentAction(action)) {
    return action.component.title;
  }

  throw new Error('Unknown action type!');
};

const { open } = useVorgangAnlegenModal();

const emit = defineEmits<{
  (event: 'pushComponent', component: StackComponent): void,
}>();

eventBus.on(
  'openNachrichtVerfassen',
  (element) => {
    emit('pushComponent', {
      title: 'Nachricht verfassen',
      componentName: 'Nachricht',
      component: markRaw(Nachricht),
      props: {
        element: element,
        fromBrief: true,
      },
    });
  },
);

const handleClick = (action: Action) => {
  if (action.disabled) {
    return;
  }

  // if no action has no component open vorgang modal.
  if (isFolgevorgangAction(action)) {
    open(vorgang.value);

    return;
  }

  if (isComponentAction(action)) {
    emit('pushComponent', action.component);

    return;
  }

  if (isExternalAction(action)) {
    window.open(action.href, '_blank');

    return;
  }

  throw new Error('Unknown action type!');
};
</script>
