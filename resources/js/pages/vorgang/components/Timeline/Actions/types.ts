import { StackComponent } from '@/composables/navigationStack/types';

export interface BaseAction {
  icon: string,
  disabled?: boolean, // if the action is disabled.
}

export type ComponentAction = BaseAction & {
  component: StackComponent;
};

export type ExternalAction = BaseAction & {
  label: string;
  href: string;
};

export type FolgevorgangAction = BaseAction & {
  label: 'Folgevorgang erstellen';
};

export type Action = ComponentAction | ExternalAction | FolgevorgangAction;

export const isComponentAction = (action: Action): action is ComponentAction => (
  'component' in action
);

export const isExternalAction = (action: Action): action is ExternalAction => (
  'href' in action && 'label' in action
);

export const isFolgevorgangAction = (action: Action): action is FolgevorgangAction => (
  'label' in action && action.label === 'Folgevorgang erstellen'
);

export interface GroupedActions {
  group: string,
  actions: Action[],
}

export enum KorrespondenzVariant {
  Erinnerungen = 'erinnerungen',
  Mahnungen = 'mahnungen',
  Korrespondenzen = 'korrespondenzen',
}
