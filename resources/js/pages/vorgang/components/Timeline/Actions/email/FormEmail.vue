<template>
  <DsForm
    class="space-y-5"
    :validation-errors="validationErrors"
  >
    <div class="space-y-7">
      <DsAlert
        type="info"
        label="Es wird ein Verlauf der vorherigen Korrespondenz ergänzt"
      >
        <PERSON><PERSON>
        {{ korrespondenzVariantLabel }}
        werden ALLE eingegangenen sowie ausgegangenen E-Mails innerhalb dieses Vorgangs
        in den E-Mail-Verlauf eingefügt. Falls Sie dies nicht wünschen,
        wählen Sie bitte die Option "Kompletter Vorgang in den Verlauf" ab.
      </DsAlert>

      <ChangeFaelligAt
        v-if="korrespondenzVariant === KorrespondenzVariant.Mahnungen
          || korrespondenzVariant === KorrespondenzVariant.Erinnerungen"
        v-model:faellig-at="faelligAt"
        v-model:has-faellig-at="hasFaelligAt"
      />

      <OwnerSelection
        v-model:owner-id="ownerId"
        headline="Absender"
        form-group-label="Verfassen"
        data-test-prefix="aktionen"
      />

      <EmpfaengerFormSection
        v-model:has-cc-and-bcc="hasCcAndBcc"
        :validation-names="{
          empfaenger: 'attributes.empfaenger',
          cc: 'attributes.cc',
          bcc: 'attributes.bcc',
        }"
      >
        <template #empfaenger>
          <EmpfaengerMultiselect
            v-model="empfaengerMailProxy"
            :suggestion-context="empfaengerSuggestionContext"
            required
            data-test="aktionen__empfaenger"
          />
        </template>
        <template #cc>
          <EmpfaengerMultiselect
            v-model="ccProxy"
            :suggestion-context="empfaengerSuggestionContext"
            data-test="aktionen__cc"
          />
        </template>
        <template #bcc>
          <EmpfaengerMultiselect
            v-model="bccProxy"
            :suggestion-context="empfaengerSuggestionContext"
            data-test="aktionen__bcc"
          />
        </template>
      </EmpfaengerFormSection>

      <FormSection title="Nachricht">
        <NachrichtSubject
          v-model="betreff"
          data-test="aktionen__nachricht__subject"
          validation-name="attributes.betreff"
          required
        />
        <NachrichtContent
          v-model="content"
          :is-loading="isLoadingTextGeneration"
          data-test="aktionen__nachricht__content"
          validation-name="attributes.content"
          class="col-span-4"
          required
        >
          <TextGeneration
            v-if="isTextGenerationActive"
            :content="content"
            :location="TextGenerationLocation.NachrichtVerfassen"
            :anrede="isInformal ? Anrede.Duzen : Anrede.Siezen"
            :vorgangstyp-id="vorgangTypId"
            :vorgang-id="vorgang?.id"
            :empfaengertyp="empfaengertyp"
            :empfaenger-mail="firstEmpfaengerMail"
            @update:content="content = $event"
          />
        </NachrichtContent>
        <FormFiles
          v-model:uploaded-files="uploadedFiles"
          v-model:existing-documents="externalFiles"
          :kunde-id="kundeId"
          :gesellschaft-id="gesellschaftId"
          :vertraege-ids="vertraegeIds"
          :remove-file-from-existing-documents="removeFileFromExistingDocuments"
          :add-docs-from-first-element="addDocsFromFirstElement"
          :show-tooltip="korrespondenzVariant === KorrespondenzVariant.Erinnerungen
            || korrespondenzVariant === KorrespondenzVariant.Mahnungen"
          show-total-file-size-alerts
        />

        <DsFormGroup validation-name="data.attributes.include_full_history">
          <DsSwitch
            v-model="includeFullHistory"
          >
            Kompletter Vorgang in den Verlauf
          </DsSwitch>
        </DsFormGroup>
      </FormSection>

      <DsButton
        :handler="save"
        :disabled="isTotalFileSizeTooLarge"
        class="float-right"
        size="lg"
        icon="angle-right"
      >
        Weiter zur Vorschau
      </DsButton>
    </div>
  </DsForm>
</template>

<script setup lang="ts">
import { DsAlert, DsButton, DsForm, DsFormGroup, DsSwitch } from '@demvsystems/design-components';
import axios from 'axios';
import { computed, markRaw, onBeforeMount, onMounted, ref } from 'vue';

import { extractErrors, isAxiosError, post, put } from '@/api';
import ChangeFaelligAt from '@/components/form/ChangeFaelligAt.vue';
import EmpfaengerFormSection from '@/components/form/EmpfaengerFormSection.vue';
import EmpfaengerMultiselect from '@/components/form/EmpfaengerMultiselect.vue';
import FormFiles from '@/components/form/FormFiles.vue';
import FormSection from '@/components/form/FormSection.vue';
import NachrichtContent from '@/components/form/NachrichtContent.vue';
import NachrichtSubject from '@/components/form/NachrichtSubject.vue';
import OwnerSelection from '@/components/form/OwnerSelection.vue';
import {
  EmpfaengerSuggestionContext,
  useEmpfaengerSuggestions,
} from '@/components/form/useEmpfaengerSuggestions';
import { FIFTEEN_MB_IN_B } from '@/components/form/useFiles';
import { emptyHtmlToEmptyString } from '@/components/form/utils/emptyHtmlToEmptyString';
import { multiselectItemToEmailElement } from '@/components/form/utils/mail';
import TextGeneration from '@/components/textGeneration/TextGeneration.vue';
import { TextGenerationLocation } from '@/components/textGeneration/types';
import useCurrentUser from '@/components/users/useCurrentUser';
import { StackComponent } from '@/composables/navigationStack/types';
import { useTextGeneration } from '@/composables/useTextGeneration';
import { Anrede } from '@/pages/vorgangAnlegen/types';
import { injectStore, useVorgangFromRoute } from '@/store/resources/composition';
import { ExternalFileResource, FileResource, KorrespondenzResource } from '@/store/resources/types';

import { KorrespondenzVariant } from '../types';

import Preview from './Preview.vue';
import useFormEmail from './uses/useFormEmail';

const props = defineProps<{
  korrespondenzVariant: KorrespondenzVariant,
  fromBrief?: boolean,
  element?: KorrespondenzResource,
}>();

const emit = defineEmits<{
  (event: 'save'): void
  (event: 'pushComponent', component: StackComponent): void
}>();

const { isLoading: isLoadingTextGeneration } = useTextGeneration();
const isLoading = ref(false);
const vorgang = useVorgangFromRoute();
const id = ref();
const validationErrors = ref({});

const korrespondenzVariantLabel = computed(() => {
  switch (props.korrespondenzVariant) {
    case KorrespondenzVariant.Erinnerungen:
      return 'Erinnerung';
    case KorrespondenzVariant.Mahnungen:
      return 'Mahnung';
    default:
      return 'Korrespondenz';
  }
});

const addDocsFromFirstElement = computed(() => [
  KorrespondenzVariant.Erinnerungen,
  KorrespondenzVariant.Mahnungen,
].includes(props.korrespondenzVariant));

const {
  hasFaelligAt,
  faelligAt,
  ownerId,
  hasCcAndBcc,
  betreff,
  content,
  empfaengerMailProxy,
  ccProxy,
  bccProxy,
  kundeId,
  gesellschaftId,
  vertraegeIds,
  sparteId,
  vorgangTypId,
  includeFullHistory,
  externalVertriebswegId,
  isInformal,

  getKorrespondenzDTO,
  initFormForBrief,
  uploadedFiles,
  externalFiles,
  files,
  totalFileSize,
  removeFileFromExistingDocuments,
  saveFiles,
} = useFormEmail(
  props.korrespondenzVariant,
  props.korrespondenzVariant === KorrespondenzVariant.Erinnerungen
    || props.korrespondenzVariant === KorrespondenzVariant.Mahnungen,
);

const store = injectStore();

const  { user } = useCurrentUser();

const isTextGenerationActive = computed(() => user.value !== undefined
  && user.value.attributes?.canUseTextGeneration
  && props.korrespondenzVariant === KorrespondenzVariant.Korrespondenzen,
);

onBeforeMount(async () => {
  if (!props.fromBrief || props.element === undefined) {
    return;
  }

  await initFormForBrief();
});

onBeforeMount(() => {
  if (!props.fromBrief || props.element?.relationships?.files === undefined) {
    return;
  }

  const relatedFiles = store.files.findAllRelated(props.element.relationships?.files) ?? [];
  relatedFiles.forEach((element: FileResource) => {
    const file = <ExternalFileResource>{
      id: element.id,
      type: 'files',
      attributes: {
        name: element.attributes.name,
        origin: 'aktuellerVorgang',
        url: element.links?.self,
      },
    };

    externalFiles.value.push(file);
  });
});

onMounted(() => {
  if (vorgang.value?.attributes.vorgangsnummer !== undefined
      && !betreff.value?.includes(vorgang.value.attributes.vorgangsnummer)
  ) {
    betreff.value = (`${betreff.value} (${vorgang.value.attributes.vorgangsnummer})`).trim();
  }
});

const create = async () => {
  return post<KorrespondenzResource>(
    `/vorgaenge/${vorgang.value?.id}/${props.korrespondenzVariant}`,
    getKorrespondenzDTO({ status: 'entwurf' }),
  );
};

const update = async () => {
  return put<KorrespondenzResource>(
    `/vorgaenge/${vorgang.value?.id}/${props.korrespondenzVariant}/${id.value}`,
    getKorrespondenzDTO({ status: 'entwurf' }),
  );
};

const save = async () => {
  try {
    isLoading.value = true;

    const response = id.value
      ? await update()
      : await create();

    if (
      vorgang.value?.relationships?.timeline?.data === undefined
        || response.data?.data === undefined
    ) {
      return;
    }

    const korrespondenz = response.data.data;

    id.value = korrespondenz.id;
    validationErrors.value = {};

    if (korrespondenz.links?.content === undefined) {
      return;
    }

    const contentResponse = await axios.get<{ content: string }>(
      korrespondenz.links.content,
    );

    korrespondenz.attributes.content = contentResponse.data.content;

    emit('pushComponent',
      {
        title: 'Vorschau',
        componentName: 'Preview',
        component: markRaw(Preview),
        props: {
          entwurfKorrespondenz: korrespondenz,
          korrespondenzVariant: props.korrespondenzVariant,
          faelligAt: faelligAt,
          saveFiles,
          files,
          dtoInBearbeitung: getKorrespondenzDTO({
            status: 'in_bearbeitung',
            content: emptyHtmlToEmptyString(korrespondenz.attributes.content),
            betreff: emptyHtmlToEmptyString(korrespondenz.attributes.betreff),
          }),
        },
      } as StackComponent,
    );
  } catch (e) {
    if (!isAxiosError(e) || e.response === undefined) {
      throw e;
    }

    validationErrors.value = extractErrors(e.response.data?.errors ?? []);
  } finally {
    isLoading.value = false;
  }
};

const isTotalFileSizeTooLarge = computed(() => totalFileSize.value > FIFTEEN_MB_IN_B);

const empfaengerSuggestionContext = computed<EmpfaengerSuggestionContext>(() => ({
  kundeId,
  gesellschaftId,
  sparteId,
  vorgangstypId: vorgangTypId,
  vorgangId: vorgang.value?.id,
  externalVertriebswegId,
}));

const {
  suggestions,
  isLoading: isLoadingSuggestions,
} = useEmpfaengerSuggestions(empfaengerSuggestionContext);

const firstEmpfaengerMail = computed(() => {
  if (empfaengerMailProxy.value.length === 0) {
    return undefined;
  }

  return multiselectItemToEmailElement(empfaengerMailProxy.value)[0]?.email;
});

const empfaengertyp = computed(() => {
  if (isLoadingSuggestions.value) {
    return undefined;
  }

  return suggestions.value.find(
    (suggestion) => suggestion.email === firstEmpfaengerMail.value
    && suggestion.context !== 'vorgang',
  )?.context;
});
</script>
