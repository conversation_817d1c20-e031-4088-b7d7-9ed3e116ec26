import { get } from 'lodash-es';

import { VorgangStatus } from '@/types';

import { KorrespondenzVariant } from '../../types';

import vorlagen from './vorlagen.json';

type VorlageUtilsOptions = {
  korrespondenzVariant?: KorrespondenzVariant,
  vorgangStatus?: VorgangStatus,
  empfaengerTyp?: string,
  useFondsFinanzVorlage?: boolean,
  isInformal?: boolean,
  isDunkelverarbeitet?: boolean,
  isExternallySentAntrag?: boolean,
};

export function getContentFromVorlage(options: VorlageUtilsOptions): string {
  const {
    korrespondenzVariant,
    vorgangStatus,
    empfaengerTyp,
  } = options;

  if (korrespondenzVariant === undefined
    || vorgangStatus === undefined
    || !['kunde', 'gesellschaft', 'demv', 'vertriebsweg'].includes(empfaengerTyp ?? '')) {
    return '';
  }

  return get(vorlagen, getPathForVorlage(options)) as string;
}

function getPathForVorlage(options: VorlageUtilsOptions): string {
  const {
    korrespondenzVariant,
    vorgangStatus,
    empfaengerTyp,
    useFondsFinanzVorlage,
    isInformal,
    isDunkelverarbeitet,
    isExternallySentAntrag,
  } = options;

  const isErinnerung = (
    korrespondenzVariant === KorrespondenzVariant.Erinnerungen
  );

  const shouldUseSecondVorlage = (
    (isErinnerung && VorgangStatus.Erinnerung1 === vorgangStatus)
    || (!isErinnerung && VorgangStatus.Mahnung1 === vorgangStatus)
  );

  let path = '';
  path += useFondsFinanzVorlage ? 'fondsfinanz' : 'gesellschaft';
  path += isErinnerung ? '.erinnerung' : '.mahnung';
  path += shouldUseSecondVorlage ? '.2' : '.1';

  if (isDunkelverarbeitet) {
    path += '.dunkelverarbeitet';
  } else if (isExternallySentAntrag) {
    path += '.externallySentAntrag';
  } else if (empfaengerTyp === 'vertriebsweg') {
    path += '.gesellschaft';
  } else {
    path += `.${empfaengerTyp}`;

    if (empfaengerTyp === 'kunde') {
      path += isInformal ? '.informal' : '.formal';
    }
  }

  return path;
}

export function getBetreffFromVorlage(
  korrespondenzVariant?: KorrespondenzVariant,
  vorgangStatus?: VorgangStatus,
  korrespondenzBetreff?: string,
): string {
  if (vorgangStatus === undefined) {
    return '';
  }

  const isErinnerung = (
    korrespondenzVariant === KorrespondenzVariant.Erinnerungen
  );

  const shouldUseSecondVorlage = (
    (isErinnerung && VorgangStatus.Erinnerung1 === vorgangStatus)
    || (!isErinnerung && VorgangStatus.Mahnung1 === vorgangStatus)
  );

  const betreffPrefix = (
    `${shouldUseSecondVorlage ? '2' : '1'}. ${isErinnerung ? 'Erinnerung' : 'Mahnung'}: `
  );

  if (korrespondenzBetreff) {
    return betreffPrefix + korrespondenzBetreff;
  }

  return betreffPrefix + '<span data-type="tag" data-name="vorgang:titel"></span>';
}
