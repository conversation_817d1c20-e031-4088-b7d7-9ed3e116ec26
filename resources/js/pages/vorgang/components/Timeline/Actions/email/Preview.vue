<template>
  <div>
    <KorrespondenzDetails
      v-if="vorgang !== undefined && entwurfKorrespondenz !== undefined"
      :vorgang="vorgang"
      :korrespondenz="entwurfKorrespondenz"
      :files="files"
      :suppress-errors-and-retry="true"
      show-actions
    />

    <div class="flex justify-end">
      <DsButton
        size="lg"
        icon="paper-plane"
        :handler="update"
      >
        {{ buttonText }}
      </DsButton>
    </div>
  </div>
</template>

<script setup lang="ts">
import { DsButton } from '@demvsystems/design-components';
import { format, subDays } from 'date-fns';
import { computed } from 'vue';

import { put } from '@/api';
import KorrespondenzDetails from '@/pages/korrespondenzDetails/korrespondenzDetails.vue';
import { injectStore, useVorgangFromRoute } from '@/store/resources/composition';
import { FileResource, KorrespondenzResource } from '@/store/resources/types';

import { reloadStoreFromCurrentVorgang } from '../storeService';
import { KorrespondenzVariant } from '../types';

const props = defineProps<{
  entwurfKorrespondenz: KorrespondenzResource,
  korrespondenzVariant: KorrespondenzVariant,
  dtoInBearbeitung: Record<string, unknown>,
  saveFiles: (ownerId: string, ownerType: string) => Promise<void>,
  files: FileResource[],
  faelligAt?: Date | undefined,
}>();

const emit = defineEmits<{
  (event: 'resetNavigationStack'): void
}>();

const store = injectStore();
const vorgang = useVorgangFromRoute();

const buttonText = computed(() => {
  switch (props.korrespondenzVariant) {
    case KorrespondenzVariant.Mahnungen:
      return 'Mahnung versenden';
    case KorrespondenzVariant.Erinnerungen:
      return 'Erinnerung versenden';
    default:
      return 'Korrespondenz versenden';
  }
});

// update status
const update = async () => {
  if (props.entwurfKorrespondenz === undefined || vorgang.value === undefined) {
    return;
  }

  const ownerId = props.entwurfKorrespondenz.id;
  const ownerType = props.entwurfKorrespondenz.type;

  if (ownerId === undefined || ownerType === undefined) {
    return;
  }

  try {
    await props.saveFiles(ownerId, ownerType);

    if (props.faelligAt && props.faelligAt > subDays(new Date(), 1)) {
      vorgang.value.attributes.faelligAt = format(props.faelligAt, 'yyyy-MM-dd');
    }

    // change status to in_bearbeitung.
    await put<KorrespondenzResource>(
      `/vorgaenge/${vorgang.value.id ?? ''}/${props.korrespondenzVariant}/${props.entwurfKorrespondenz.id}`,
      props.dtoInBearbeitung,
    );

    await reloadStoreFromCurrentVorgang(store, vorgang.value.id);

    emit('resetNavigationStack');
  } catch (error) {
    console.log(error);
  }
};
</script>
