import { MultiselectItem } from '@demvsystems/design-components';
import { addWeeks } from 'date-fns';
import { isArray } from 'lodash-es';
import { computed, onMounted, ref, watch } from 'vue';

import { useFiles } from '@/components/form/useFiles';
import {
  emailElementToMultiselectItem,
  multiselectItemToEmailElement,
} from '@/components/form/utils/mail';
import useCurrentUser from '@/components/users/useCurrentUser';
import { useAnsprechpartners } from '@/composables/useAnsprechpartners';
import { useVorlagen } from '@/composables/useVorlagen';
import {
  FONDSFINANZ_GESELLSCHAFT_EXTERNAL_ID,
  VORGANGSTYP_ANTRAG_EINREICHEN_ID,
  VORGANGSTYP_BESTANDSUEBERTRAGUNG_IDS,
} from '@/constants/ids';
import { Versandart } from '@/pages/vorlagen/types';
import { injectStore, useVorgangFromRoute } from '@/store/resources/composition';
import {
  EmailElement,
  isEmailElement,
  VertragResource,
  Vorgangsart,
} from '@/store/resources/types';
import { useVorgangstypenStore } from '@/stores/vorgangstypenStore';
import { getReceiverMailAddress } from '@/utils/getUserMailAddress';

import { KorrespondenzVariant } from '../../types';
import { getBetreffFromVorlage, getContentFromVorlage } from '../vorlageUtils/vorlageUtils';

const entwurfKorrespondenz = ref<KorrespondenzVariant | null>(null);

export default (
  korrespondenzVariant: KorrespondenzVariant,
  useVorlage?: boolean,
) => { // eslint-disable-line @typescript-eslint/explicit-module-boundary-types
  const store = injectStore();
  const vorgangstypenStore = useVorgangstypenStore();
  const vorgang = useVorgangFromRoute();

  const {
    uploadedFiles,
    externalFiles,
    files,
    totalFileSize,
    removeFileFromExistingDocuments,
    saveFiles,
  } = useFiles();

  const versandart = ref(Versandart.Mail);

  const kunde = computed(() => store.kunden.findRelated(vorgang.value?.relationships?.kunde));

  const isInformal = computed(() => kunde.value?.attributes?.informal);

  const firstKorrespondenz = store.korrespondenzen.find(
    store.timelineEintraege?.find(
      vorgang.value?.relationships?.timeline?.data[0]?.id,
    )?.relationships?.element?.data?.id,
  );

  const hasFaelligAt = ref<boolean>([
    KorrespondenzVariant.Erinnerungen,
    KorrespondenzVariant.Mahnungen,
  ].includes(korrespondenzVariant));
  const faelligAt = ref<Date>();

  const ownerId = ref<string>();

  const betreff = ref<string>(
    useVorlage
      ? getBetreffFromVorlage(
        korrespondenzVariant,
        vorgang.value?.attributes.status,
        firstKorrespondenz?.attributes?.betreff,
      )
      : (firstKorrespondenz?.attributes?.betreff ?? ''),
  );

  const gesellschaftId = vorgang.value?.relationships?.gesellschaft?.data?.id;
  const externalGesellschaftId = store.gesellschaften.findRelated(
    vorgang.value?.relationships?.gesellschaft,
  )?.attributes?.externalId;
  const vorgangTypId = vorgang.value?.relationships?.vorgangTyp?.data?.id;
  const sparteId = vorgang.value?.relationships?.sparte?.data?.id;
  const externalVertriebswegId = store.gesellschaften.findRelated(
    vorgang.value?.relationships?.vertriebsweg,
  )?.attributes?.externalId;

  const isDunkelverarbeitet = (
    vorgang.value?.attributes.vorgangsart === Vorgangsart.Dunkelverarbeitet
  );
  const isExternallySentAntrag = (
    vorgang.value?.attributes.vorgangsart === Vorgangsart.Aufgabe
    && vorgangTypId === VORGANGSTYP_ANTRAG_EINREICHEN_ID
  );

  const empfaengerMailProxy = ref<MultiselectItem[]>([]);
  onMounted(async () => {
    if (isDunkelverarbeitet && vorgangTypId !== undefined || isExternallySentAntrag) {
      const {
        loadAnsprechpartnerList,
        ansprechpartnerMails,
      } = useAnsprechpartners();

      await loadAnsprechpartnerList(
        vorgangTypId,
        gesellschaftId,
        sparteId,
      );

      empfaengerMailProxy.value = emailElementToMultiselectItem(ansprechpartnerMails.value);

      return;
    }

    if (!isArray(firstKorrespondenz?.attributes?.empfaenger)) {
      return;
    }

    if (!isEmailElement(firstKorrespondenz?.attributes?.empfaenger[0] ?? {})) {
      return;
    }

    empfaengerMailProxy.value = emailElementToMultiselectItem(
      firstKorrespondenz?.attributes?.empfaenger ?? [],
    );
  });

  const ccProxy = ref<MultiselectItem[]>(emailElementToMultiselectItem(
    firstKorrespondenz?.attributes?.cc ?? [],
  ));

  const bccProxy = ref<MultiselectItem[]>([]);
  onMounted(async () => {
    const bcc = firstKorrespondenz?.attributes?.bcc ?? [];

    if (bcc.length === 0 && (isDunkelverarbeitet || isExternallySentAntrag)) {
      bcc.push(await getReceiverMailAddress(useCurrentUser().user.value?.id));
    }

    bccProxy.value = emailElementToMultiselectItem(bcc);
  });

  const hasCcAndBcc = ref(
    firstKorrespondenz?.attributes?.hasCcAndBcc ?? false,
  );

  const content = ref<string>((() => {
    if (!useVorlage) {
      return '';
    }

    const vorgangstypId = vorgang.value?.relationships?.vorgangTyp?.data?.id ?? '';
    const isFondsFinanzVorlageVorgangstyp =
      VORGANGSTYP_BESTANDSUEBERTRAGUNG_IDS.includes(vorgangstypId)
      || vorgangstypId === VORGANGSTYP_ANTRAG_EINREICHEN_ID;

    const useFondsFinanzVorlage = (externalVertriebswegId === FONDSFINANZ_GESELLSCHAFT_EXTERNAL_ID
        || externalGesellschaftId === FONDSFINANZ_GESELLSCHAFT_EXTERNAL_ID)
      && isFondsFinanzVorlageVorgangstyp
      && isDunkelverarbeitet;

    const empfaengerTyp = vorgangstypenStore.findById(vorgangstypId)?.attributes.empfaengerTyp;

    return getContentFromVorlage({
      korrespondenzVariant,
      vorgangStatus: vorgang.value?.attributes.status,
      empfaengerTyp,
      useFondsFinanzVorlage,
      isInformal: kunde?.value?.attributes?.informal,
      isDunkelverarbeitet,
      isExternallySentAntrag,
    });
  })());

  const kundeId = kunde.value?.id;

  const vertraegeIds = computed(() => store.vertraege.getAll().map(
    ({ id }: VertragResource) => id),
  );

  const includeFullHistory = ref(true);

  const getKorrespondenzDTO = (options: {
    status: string,
    content?: string | undefined,
    betreff?: string | undefined
  }) => ({
    data: {
      type: korrespondenzVariant,
      attributes: {
        status: options.status,
        versandart: 'email',
        content: options.content ?? content.value,
        betreff: options.betreff ?? betreff.value,
        empfaenger: multiselectItemToEmailElement(empfaengerMailProxy.value),
        bcc: multiselectItemToEmailElement(bccProxy.value),
        cc: multiselectItemToEmailElement(ccProxy.value),
        include_full_history: includeFullHistory.value,
      },
      relationships: {
        owner: {
          ...(ownerId.value != undefined ? {
            data: {
              type: 'users',
              id: ownerId.value,
            },
          } : {
            data: null,
          }),
        },
      },
    },
  });

  watch(hasFaelligAt, (newHasFaelligAt) => {
    if (!newHasFaelligAt) {
      faelligAt.value = undefined;

      return;
    }

    if (faelligAt.value === undefined) {
      faelligAt.value = addWeeks(new Date(), 1);
    }
  }, { immediate: true });

  // clear cc & bcc on disabling hasCcAndBcc
  watch(hasCcAndBcc, (newHasCcAndBcc) => {
    if (!newHasCcAndBcc) {
      ccProxy.value = [];
      bccProxy.value = [];
    }
  });

  // enable hasCcAndBcc if cc or bcc are set
  watch([ccProxy, bccProxy], ([newCc, newBcc]) => {
    if (hasCcAndBcc.value) {
      return;
    }

    if (newCc.length === 0 && newBcc.length === 0) {
      return;
    }

    hasCcAndBcc.value = true;
  });

  const initFormForBrief = async (): Promise<void> => {
    if (
      kunde.value === undefined || vorgang.value?.relationships?.vorgangTyp?.data?.id === undefined
    ) {
      return;
    }

    const sparte = computed(() => store.sparten.findRelated(vorgang.value?.relationships?.sparte));
    const vertriebsweg = computed(
      () => store.gesellschaften.findRelated(vorgang.value?.relationships?.vertriebsweg),
    );
    const vorgangstypId = computed(() => vorgang.value?.relationships?.vorgangTyp?.data?.id);
    const mailEmpfaenger = ref<EmailElement[]>([]);
    const mailCc = ref<EmailElement[]>([]);
    const mailBcc = ref<EmailElement[]>([]);

    watch(mailEmpfaenger, (empfaenger) => {
      empfaengerMailProxy.value = emailElementToMultiselectItem(empfaenger as EmailElement[]);
    });

    watch(
      mailCc, (empfaenger) => {
        ccProxy.value = emailElementToMultiselectItem(empfaenger as EmailElement[]);
      },
    );

    watch(
      mailBcc, (empfaenger) => {
        bccProxy.value = emailElementToMultiselectItem(empfaenger as EmailElement[]);
      },
    );

    const {
      vorlageContent,
      updateVorlagen,
    } = useVorlagen({
      versandart,
      kunde,
      vertriebsweg,
      sparte,
      ownerId,
      vorgangstypId,
      mailEmpfaenger,
      mailHasCcAndBcc: hasCcAndBcc,
      mailCc,
      mailBcc,
      resetEmpfaengerFields: (): void => {
        mailEmpfaenger.value = [];
        mailCc.value = [];
        mailBcc.value = [];
        hasCcAndBcc.value = false;
      },
    },
    );
    await updateVorlagen();

    content.value = vorlageContent.value ?? '';
  };

  return {
    hasFaelligAt,
    faelligAt,
    ownerId,
    hasCcAndBcc,
    betreff,
    content,
    empfaengerMailProxy,
    ccProxy,
    bccProxy,
    kundeId,
    gesellschaftId,
    vertraegeIds,
    sparteId,
    vorgangTypId,
    entwurfKorrespondenz,
    includeFullHistory: includeFullHistory,
    externalVertriebswegId,
    isInformal,

    getKorrespondenzDTO,
    initFormForBrief,

    totalFileSize,
    uploadedFiles,
    externalFiles,
    files,
    removeFileFromExistingDocuments,
    saveFiles,
  };
};
