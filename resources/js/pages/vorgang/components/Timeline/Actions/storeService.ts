import { captureException } from '@sentry/vue';

import { get } from '@/api';
import { Store, VorgangResource } from '@/store/resources/types';

export const reloadStoreFromCurrentVorgang = async (
  store: Store, vorgangId: string,
): Promise<void> => {
  try {
    const vorgengeResponse = await get<VorgangResource>(
      `/vorgaenge/${vorgangId}`, {
        include: [
          'timeline.element.files',
          'unterVorgaenge',
          'unterVorgaenge.gesellschaft',
          'ueberVorgang',
          'timeline.ersteller',
          'timeline.owner',
          'verknuepfungen',
          'kunde',
          'gesellschaft',
          'vertriebsweg',
          'vertraege.sparte',
          'participants',
          'sparte',
          'vorgangTyp',
          'folgeVorgaenge',
          'folgeVorgaenge.kunde',
          'folgeVorgaenge.gesellschaft',
          'vorgaenger',
        ],
        sort: [{
          name: 'updatedAt',
        }],
      },
    );
    store.clear();
    store.load(vorgengeResponse.data);
  } catch (error) {
    captureException(new Error(
      'Error while getting vorgang. actions/storeService',
    ));
  }
};
