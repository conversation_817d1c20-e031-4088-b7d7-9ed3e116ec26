<template>
  <div class="space-y-7">
    <FormSection>
      <div class="text-sm">
        Hier können Sie E-Mails hochladen (.eml- oder .msg-Format),
        welche <PERSON><PERSON> in Kommunikation mit dem Kunden ausgetauscht haben.
      </div>
      <FormFiles
        v-model:uploaded-files="uploadedFiles"
        accept-uploaded-files=".eml, .msg"
      />

      <div class="flex">
        <DsCheckbox
          id="takeOverAttachedFiles"
          v-model="includeAttachments"
          data-test="aktionen__include-attachments"
        />
        <label
          class="ml-2 text-sm font-medium text-gray-700"
          for="takeOverAttachedFiles"
        >
          Anhänge aus Mail übernehmen
        </label>
      </div>
    </FormSection>
    <DsButton
      class="float-right w-56"
      icon="upload"
      :disabled="files.length === 0"
      :handler="save"
    >
      Korrespondenz hochladen
    </DsButton>
  </div>
</template>

<script setup lang="ts">
import { DsButton, DsCheckbox } from '@demvsystems/design-components';
import { ref } from 'vue';

import { post } from '@/api';
import FormFiles from '@/components/form/FormFiles.vue';
import FormSection from '@/components/form/FormSection.vue';
import { useFiles } from '@/components/form/useFiles';
import { injectStore, useVorgangFromRoute } from '@/store/resources/composition';
import { BaseFileResource, FileResource } from '@/store/resources/types';

import { reloadStoreFromCurrentVorgang } from './storeService';

const {
  files,
  uploadedFiles,
} = useFiles();

const includeAttachments = ref(false);
const vorgang = useVorgangFromRoute();
const store = injectStore();

const emit = defineEmits<{
  (event: 'resetNavigationStack'):void
}>();

const save = async () => {
  // save files and store the promises in an array.
  const promises = Promise.allSettled(files.value.map(
    (file: BaseFileResource) => {
      return post<FileResource>(`vorgaenge/${vorgang.value?.id ?? ''}/attachMail`, {
        data: file,
        includeAttachments: includeAttachments.value,
      });
    },
  ));

  await promises;

  if (vorgang.value?.id === undefined) {
    return;
  }

  try {
    await reloadStoreFromCurrentVorgang(store, vorgang.value.id);
  } finally {
    emit('resetNavigationStack');
  }
};
</script>
