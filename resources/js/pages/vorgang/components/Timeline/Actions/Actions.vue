<!-- root component, sets and handle navigation stack -->
<template>
  <TimelineListItem
    :icon-config="{
      name: 'plus',
      helpText: 'Aktionen',
    }"
    :first="first"
    class="mb-3"
    last
  >
    <div
      class="space-y-4"
      data-test="aktionen"
    >
      <ActionsNavbar
        :show-back-button="activeComponent.componentName !== 'ActionsMenu'"
        :title="activeComponent.title"
        @pop-component="popComponent"
      />
      <transition
        mode="out-in"
        name="pop"
      >
        <keep-alive :include="cachedComponents">
          <component
            :is="activeComponent.component"
            v-bind="{...activeComponent.props}"
            @push-component="pushNavComponent($event)"
            @reset-navigation-stack="resetNavigationStack"
          />
        </keep-alive>
      </transition>
    </div>
  </TimelineListItem>
</template>

<script setup lang="ts">
import { markRaw } from 'vue';

import TimelineListItem from '@/components/TimelineElement/TimelineListItem.vue';
import { StackComponent } from '@/composables/navigationStack/types';
import { useNavigationStack } from '@/composables/navigationStack/useNavigationStack';
import { eventBus } from '@/store/resources/store';

import ActionsMenu from './ActionsMenu.vue';
import ActionsNavbar from './ActionsNavbar.vue';

const actionsMenu: StackComponent = {
  title: 'Aktionen',
  componentName: 'ActionsMenu',
  component: markRaw(ActionsMenu),
};

defineProps<{
  first: boolean,
}>();

const {
  activeComponent,
  cachedComponents,
  popComponent,
  pushComponent,
  resetNavigationStack,
  stackSize,
} = useNavigationStack(actionsMenu);

const ignoreStackSize = [
  'Preview',
];

const pushNavComponent = (event: StackComponent) => {
  if (stackSize.value > 1 && !ignoreStackSize.includes(event.componentName)) {
    eventBus.emit(
      'openAction',
    );

    return;
  }

  pushComponent(event);
};
</script>

<style scoped>
.pop-enter-from {
  opacity: 0;
  transform: translatex(-20px);
}

.pop-leave-active, .pop-enter-active {
  transition: all .2s;
}

.pop-leave-to {
  opacity: 0;
  transform: translatex(-20px);
}
</style>
