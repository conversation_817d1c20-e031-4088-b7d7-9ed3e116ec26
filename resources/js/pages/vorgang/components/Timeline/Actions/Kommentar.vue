<template>
  <DsForm
    class="space-y-5"
    :validation-errors="validationErrors"
  >
    <div class="space-y-7">
      <FormSection>
        <NachrichtContent
          v-model="content"
          label="Inhalt"
          data-test="aktionen__nachricht__content"
          validation-name="attributes.content"
          required
        />
        <FormFiles
          v-model:uploaded-files="uploadedFiles"
          v-model:existing-documents="externalFiles"
          :kunde-id="kundeId"
          :gesellschaft-id="gesellschaftId"
          :vertraege-ids="vertraegeIds"
          :remove-file-from-existing-documents="removeFileFromExistingDocuments"
        />
      </FormSection>
      <DsButton
        class="float-right w-40"
        icon="paper-plane"
        :handler="save"
      >
        Kommentieren
      </DsButton>
    </div>
  </DsForm>
</template>

<script setup lang="ts">
import { DsButton, DsForm } from '@demvsystems/design-components';
import { ref } from 'vue';

import { extractErrors, isAxiosError, post } from '@/api';
import FormFiles from '@/components/form/FormFiles.vue';
import FormSection from '@/components/form/FormSection.vue';
import NachrichtContent from '@/components/form/NachrichtContent.vue';
import { useFiles } from '@/components/form/useFiles';
import { emptyHtmlToEmptyString } from '@/components/form/utils/emptyHtmlToEmptyString';
import { injectStore, useVorgangFromRoute } from '@/store/resources/composition';
import { KommentarResource, VertragResource } from '@/store/resources/types';

import { reloadStoreFromCurrentVorgang } from './storeService';

const emit = defineEmits<{
  (event: 'resetNavigationStack'): void
}>();

const content = ref<string>('');

const store = injectStore();
const vorgang = useVorgangFromRoute();

const kundeId = vorgang.value?.relationships?.kunde?.data?.id;
const gesellschaftId = vorgang.value?.relationships?.gesellschaft?.data?.id;
const vertraegeIds = store.vertraege.getAll().map(
  ({ id }: VertragResource) => id,
);
const validationErrors = ref({});

const {
  uploadedFiles,
  externalFiles,
  removeFileFromExistingDocuments,
  saveFiles,
} = useFiles();

const save = async () => {
  try {
    const { data: kommentar } = await post<KommentarResource>(
      `/vorgaenge/${vorgang.value?.id ?? ''}/kommentare`,
      {
        data: {
          type: 'kommentare',
          attributes: {
            content: emptyHtmlToEmptyString(content.value),
          },
        },
      },
    );

    if (vorgang.value?.relationships?.timeline?.data === undefined) {
      return;
    }

    const ownerId = kommentar.data?.id;
    const ownerType = kommentar.data?.type;

    if (ownerId === undefined || ownerType === undefined) {
      return;
    }

    await saveFiles(ownerId, ownerType);

    await reloadStoreFromCurrentVorgang(store, vorgang.value.id);

    emit('resetNavigationStack');
  } catch (e) {
    if (!isAxiosError(e) || e.response === undefined) {
      throw e;
    }

    validationErrors.value = extractErrors(e.response.data?.errors ?? []);
  }
};
</script>
