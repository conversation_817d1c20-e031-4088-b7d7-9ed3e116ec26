<template>
  <div
    class="flex h-10 items-center space-x-3"
    data-test="aktionen__navbar"
  >
    <DsButton
      v-if="showBackButton"
      icon="arrow-left"
      variant="secondary"
      data-test="aktionen__navbar__back"
      @click="emits('pop-component')"
    />
    <div
      class="font-semibold"
      v-text="title"
    />
  </div>
</template>

<script setup lang="ts">
import { DsButton } from '@demvsystems/design-components';

defineProps<{
  showBackButton: boolean;
  title: string;
}>();

const emits = defineEmits<{
  (event: 'pop-component'): void;
}>();
</script>
