<template>
  <div ref="emailForm">
    <FormEmail
      v-bind="{...props}"
      :korrespondenz-variant="korrespondenzenVariant"
      @push-component="emitPushComponent"
    />
  </div>
</template>

<script setup lang="ts">
import { onMounted, useTemplateRef } from 'vue';

import { StackComponent } from '@/composables/navigationStack/types';
import { KorrespondenzResource } from '@/store/resources/types';

import FormEmail from './email/FormEmail.vue';
import { KorrespondenzVariant } from './types';

const emailForm = useTemplateRef<HTMLDivElement>('emailForm');

const emit = defineEmits<{
  (event: 'pushComponent', component: StackComponent): void
}>();

const emitPushComponent = (component: StackComponent) => {
  emit('pushComponent', component);
};

const props = withDefaults(defineProps<{
  // eslint-disable-next-line vue/no-unused-properties
  fromBrief?: boolean,
  // eslint-disable-next-line vue/no-unused-properties
  element?: KorrespondenzResource,
}>(), { element: undefined });

onMounted(() => {
  emailForm?.value?.scrollIntoView();
});

const korrespondenzenVariant = KorrespondenzVariant.Korrespondenzen;
</script>
