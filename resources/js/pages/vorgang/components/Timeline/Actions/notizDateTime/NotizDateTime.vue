<template>
  <div class="flex space-x-4">
    <DsFormGroup
      label="Datum"
    >
      <DsInput
        v-model="proxyDateFrom"
        type="date"
      />
    </DsFormGroup>
    <DsFormGroup
      label="Von"
      class="w-3/12"
      validation-name="timeFrom"
    >
      <DsInput
        v-model="proxyTimeFrom"
        type="time"
      />
    </DsFormGroup>
    <DsFormGroup
      label="Bis"
      class="w-3/12"
      validation-name="timeTo"
    >
      <DsInput
        v-model="proxyTimeTo"
        type="time"
      />
    </DsFormGroup>
  </div>
</template>

<script setup lang="ts">
import { DsFormGroup, DsInput } from '@demvsystems/design-components';
import { set as setFns, getHours, getMinutes } from 'date-fns';
import { computed } from 'vue';

const props = defineProps<{
  dateFrom: Date,
  dateTo: Date,
}>();

const emit = defineEmits<{
  (event: 'update:dateFrom', date: Date):void
  (event: 'update:dateTo', date: Date):void
}>();

// validation of time!
const isTime = (input: string) => {
  return /^([0-1]?[0-9]|2[0-3]):([0-5][0-9])?$/.test(input);
};

// Extract me if you need me in another comp!
const getFormatedTime = (date: Date) => {
  const addLeadingZero = (digits: number) => {
    return ('0' + digits.toString()).slice(-2);
  };

  const hours = addLeadingZero(getHours(date));
  const minutes = addLeadingZero(getMinutes(date));

  return `${hours}:${minutes}`;
};

// proxies!
const proxyTimeFrom = computed({
  get: () => getFormatedTime(props.dateFrom),
  set: (newTime) => {
    if (!isTime(newTime)) {
      return;
    }

    emit('update:dateFrom', setFns(props.dateFrom, {
      hours: +newTime.split(':')[0]!,
      minutes: +newTime.split(':')[1]!,
    }));
  },
});

const proxyDateFrom = computed({
  get: () => props.dateFrom,
  set: (newDate) => {
    emit('update:dateFrom', setFns(newDate, {
      hours: +proxyTimeFrom.value.split(':')[0]!,
      minutes: +proxyTimeFrom.value.split(':')[1]!,
    }));
  },
});

const proxyTimeTo = computed({
  get: () => getFormatedTime(props.dateTo),
  set: (newTime) => {
    if (!isTime(newTime)) {
      return;
    }

    emit('update:dateTo', setFns(props.dateTo, {
      hours: +newTime.split(':')[0]!,
      minutes: +newTime.split(':')[1]!,
    }));
  },
});
</script>
