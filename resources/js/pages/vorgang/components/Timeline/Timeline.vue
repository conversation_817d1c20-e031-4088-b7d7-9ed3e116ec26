<template>
  <div class="flow-root">
    <h3 class="text-sm font-semibold uppercase text-gray-700">
      Aktivität
    </h3>

    <ul
      ref="list"
      data-test="vorgang__timeline"
    >
      <component
        :is="getComponent(element)"
        v-for="(element, index) in timeline"
        :key="element.id"
        :index="index"
        :element="element"
        data-test="vorgang__timeline__element"
      />

      <Actions :first="timeline.length === 0" />
    </ul>
  </div>
</template>

<script setup lang="ts">
import { onMounted, useTemplateRef } from 'vue';

import { timeline as timelineDomain } from '@/domains';
import { useVorgangErrorAlert } from '@/pages/vorgang/composables/useVorgangErrorAlert';
import type { TimelineEintragResource } from '@/store/resources/types';

import Actions from './Actions/Actions.vue';

defineProps<{
  timeline: TimelineEintragResource[],
}>();

const list = useTemplateRef<HTMLElement>('list');
const { hasAlert } = useVorgangErrorAlert();

onMounted(() => {
  if (hasAlert.value) {
    return; // don't scroll down
  }

  // scrolls to last element, right before actions
  list.value?.children[list.value?.children.length - 2]?.scrollIntoView({
    behavior: 'smooth',
  });
});

function getComponent(eintrag: TimelineEintragResource) {
  return timelineDomain.value[eintrag.relationships?.element?.data?.type || ''];
}
</script>
