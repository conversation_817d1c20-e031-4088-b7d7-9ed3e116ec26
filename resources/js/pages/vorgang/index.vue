<template>
  <div
    v-if="vorgang && !isVorgangLoading"
    class="flex min-w-0 flex-col"
  >
    <CourtageErfassenModal
      v-if="vorgangId && isVorgangNew"
      :show="showCourtageErfassenModal"
      :vorgang-id="vorgangId"
      @close="showCourtageErfassenModal = false"
    />
    <SchadenErfassenModal
      v-if="vorgangId && isVorgangNew"
      :show="showSchadenErfassenModal"
      :vorgang-id="vorgangId"
      @close="showSchadenErfassenModal = false"
    />

    <VorgangHeader class="shrink-0" />

    <!-- content -->
    <div class="flex grow space-x-5 overflow-auto p-5">
      <div class="min-w-0 grow space-y-5">
        <VorgangErrorAlert />
        <EmptyBestandsuebertragungAlert
          v-if="showEmptyBestandsuebertragungAlert"
        />

        <RelatedVorgang
          v-if="ueberVorgang"
          :related-vorgang="ueberVorgang"
          icon="folder-tree"
          intro-text="Untervorgang von"
        />
        <RelatedVorgang
          v-else-if="vorgaenger"
          :related-vorgang="vorgaenger"
          icon="fast-forward"
          intro-text="Folgevorgang zu"
        />
        <UntervorgaengeList
          v-else-if="vorgang.attributes?.isGroup"
          :vorgangsgruppe="vorgang"
          :show-status-modal="showUntervorgaengeStatusModal"
          @close-status-modal="showUntervorgaengeStatusModal = false"
          @reload-vorgangsgruppe="reloadVorgangsgruppe"
        />
        <Timeline :timeline="timeline" />
      </div>

      <Sidebar class="sticky top-0 w-60 shrink-0 overflow-y-auto xl:w-80" />
    </div>
  </div>

  <VorgangNotFound v-else-if="responseError !== undefined && responseError > 200" />

  <VorgangSkeletons v-else />
</template>

<script setup lang="ts">
import { ignorableWatch } from '@vueuse/core';
import { differenceWith, fromPairs, isEmpty, isEqual, toPairs } from 'lodash-es';
import { computed, ref, watch } from 'vue';

import { get, isAxiosError, put } from '@/api';
import RelatedVorgang from '@/components/relatedVorgang/RelatedVorgang.vue';
import UntervorgaengeList from '@/components/unterVorgaenge/UntervorgaengeList.vue';
import useCurrentUser from '@/components/users/useCurrentUser';
import { useImpersonate } from '@/composables/useImpersonate';
import EmptyBestandsuebertragungAlert
  from '@/pages/vorgang/components/EmptyBestandsuebertragungAlert.vue';
import CourtageErfassenModal from '@/pages/vorgang/components/Modals/CourtageErfassenModal.vue';
import SchadenErfassenModal from '@/pages/vorgang/components/Modals/SchadenErfassenModal.vue';
import VorgangErrorAlert from '@/pages/vorgang/components/VorgangErrorAlert.vue';
import VorgangSkeletons from '@/pages/vorgang/components/VorgangSkeletons.vue';
import { useVorgangErrorAlert } from '@/pages/vorgang/composables/useVorgangErrorAlert';
import { createStore } from '@/store/resources';
import { provideStore } from '@/store/resources/composition';
import { eventBus } from '@/store/resources/store';
import { VorgangResource, Vorgangsart } from '@/store/resources/types';
import { useVorgangstypenStore } from '@/stores/vorgangstypenStore';
import { VorgangStatus } from '@/types';

import Sidebar from './components/Sidebar.vue';
import Timeline from './components/Timeline/Timeline.vue';
import VorgangHeader from './components/VorgangHeader.vue';
import VorgangNotFound from './components/VorgangNotFound.vue';

const objectDiff = <T extends Record<string, unknown>>(
  from: Record<string, unknown>,
  to: Record<string, unknown>,
) => fromPairs(differenceWith(toPairs(to), toPairs(from), isEqual)) as T;

const responseError = ref<number>();
const isVorgangLoading = ref(false);

const props = defineProps<{
  firmaId: string,
  vorgangsnummer: string,
  isVorgangNew?: boolean | number | string,
}>();

const vorgangId = ref<string | undefined>();

const store = createStore();
provideStore(store);

const { hasAlert } = useVorgangErrorAlert();

const { impersonate } = useImpersonate();
const { user } = useCurrentUser();
const vorgangstypenStore = useVorgangstypenStore();

async function impersonateVorgangOwner() {
  try {
    const { data } = await get<VorgangResource>(
      `/vorgaenge/${props.firmaId}/${props.vorgangsnummer}`,
      { include: ['owner', 'ersteller'] },
    );

    let userId = data.data?.relationships?.owner?.data?.id;
    if (userId === undefined) {
      return false;
    }

    const owner = data.included?.find((include) => include.id === userId && include.type === 'users');
    if (owner?.attributes.status === 'storniert') {
      userId = data.data?.relationships?.ersteller?.data?.id;
    }

    if (userId === undefined) {
      return false;
    }

    return await impersonate(userId, { redirectBack: true });
  } catch (error) {
    if (!isAxiosError(error)) {
      return false;
    }

    responseError.value = error.response?.status;
  }
}

watch([
  user,
  () => props.firmaId,
  () => props.vorgangsnummer,
], async ([newUser, firmaId, vorgangsnummer]) => {
  if (newUser === undefined) {
    return;
  }

  if (Number.parseInt(props.firmaId) !== user.value?.attributes.firmaId) {
    void impersonateVorgangOwner().then((wasSuccessful) => {
      if (!wasSuccessful) {
        responseError.value = 403;
      }
    });

    return;
  }

  isVorgangLoading.value = true;

  try {
    responseError.value = undefined;
    const response = await get<VorgangResource>(
      `/vorgaenge/${firmaId}/${vorgangsnummer}`,
      {
        include: [
          'timeline.element.files',
          'notifications',
          'ueberVorgang',
          'timeline.ersteller',
          'timeline.owner',
          'verknuepfungen',
          'kunde',
          'gesellschaft',
          'vertriebsweg',
          'vertraege.sparte',
          'participants',
          'participants.user',
          'sparte',
          'folgeVorgaenge',
          'folgeVorgaenge.kunde',
          'folgeVorgaenge.gesellschaft',
          'vorgaenger',
          'vorgangTyp',
          'bezug',
        ],
        sort: [{
          name: 'updatedAt',
        }],
      },
    );

    store.clear();
    store.load(response.data);

    vorgangId.value = response.data?.data?.id;
    if (vorgangId.value === undefined) {
      return;
    }

    eventBus.emit('vorgangAccessed', vorgangId.value);
  } catch (error) {
    if (!isAxiosError(error)) {
      return;
    }

    responseError.value = error.response?.status;
  } finally {
    isVorgangLoading.value = false;
  }
}, { immediate: true });

const vorgang = computed<VorgangResource | undefined>(() => (
  store.vorgaenge.find(vorgangId.value)
));
const vorgangstyp = computed(() => {
  const vorgangstypId = vorgang.value?.relationships?.vorgangTyp?.data?.id;

  if (vorgangstypId === undefined) {
    return undefined;
  }

  return vorgangstypenStore.findById(vorgangstypId);
});
const vertraege = computed(() => (
  store.vertraege.findAllRelated(vorgang.value?.relationships?.vertraege)
));

const showCourtageErfassenModal = ref(false);
const showSchadenErfassenModal = ref(false);
const showUntervorgaengeStatusModal = ref(false);

const showEmptyBestandsuebertragungAlert = ref(false);

watch(vorgang, () => {
  showCourtageErfassenModal.value = !!props.isVorgangNew
    && vorgangstyp.value?.attributes.titel === 'Antrag einreichen';

  showSchadenErfassenModal.value = !!props.isVorgangNew
    && vorgang.value?.relationships?.kunde?.data?.id !== undefined
    && vorgangstyp.value?.attributes.titel === 'Schadenmeldung'
    && !vertraege.value.some((vertrag) => vertrag.attributes.isSchaden);

  showEmptyBestandsuebertragungAlert.value = !hasAlert.value
    && vorgang.value?.attributes.vorgangsart === Vorgangsart.Vorgangsgruppe
    && vorgang.value?.attributes.untervorgaengeCount === 0
    && vorgang.value?.attributes.titel === 'Bestandsübertragung'
    && store.systemkommentare.getAll().length === 0;
}, { immediate: true });

let oldVorgangId: string | undefined = undefined;

const { ignoreUpdates: ignoreVorgangUpdates } = ignorableWatch(
  () => ({ ...vorgang.value?.attributes }),
  async (newAttributes, oldAttributes) => {
    if (oldVorgangId !== vorgang.value?.id) {
      oldVorgangId = vorgang.value?.id;

      return;
    }

    const attributesDiff = objectDiff(oldAttributes, newAttributes);
    // Don't update when we load the resource or no attribute was changed
    if (isEmpty(oldAttributes) || isEmpty(attributesDiff) || !vorgang.value?.links?.self) {
      return;
    }

    const response = await put<VorgangResource>(
      vorgang.value?.links?.self,
      {
        data: {
          type: 'vorgaenge',
          attributes: attributesDiff,
        },
      },
    );

    // if vorgangsgruppe with open untervorgaenge and update status is set to erledigt:
    // open modal to ask if untervorgaenge should be set to erledigt as well
    const vorgangAttr = vorgang.value.attributes;
    if (
      'status' in attributesDiff
      && vorgangAttr.isGroup
      && vorgangAttr.untervorgaengeCount > vorgangAttr.untervorgaengeErledigtCount
    ) {
      showUntervorgaengeStatusModal.value = attributesDiff.status === VorgangStatus.Erledigt;
    }

    // if has uebervorgang: update it as well
    const uebervorgangId = vorgang.value?.relationships?.ueberVorgang?.data?.id;
    if (uebervorgangId !== undefined) {
      const ueberVorgangResponse = await get<VorgangResource>(
        `vorgaenge/${uebervorgangId}`,
      );

      ignoreVorgangUpdates(() => {
        store.update(ueberVorgangResponse.data);
      });
    }

    // Don't recursively call the watcher when we update the store
    ignoreVorgangUpdates(() => {
      store.update(response.data);
    });
  },
);

const timeline = computed(() => store.timelineEintraege.findAllRelated(
  vorgang.value?.relationships?.timeline,
));

const vorgaengerId = computed(
  () => vorgang.value?.relationships?.vorgaenger?.data?.id,
);
const vorgaenger = computed(() => (
  vorgaengerId.value
    ? store.vorgaenge.find(vorgaengerId.value)
    : undefined
));

const ueberVorgang = computed(() => store.vorgaenge.findRelated(
  vorgang.value?.relationships?.ueberVorgang,
));

async function reloadVorgangsgruppe() {
  if (vorgang.value?.links?.self === undefined) {
    return;
  }

  const vorgangResponse = await get<VorgangResource>(
    vorgang.value.links.self,
  );

  ignoreVorgangUpdates(() => {
    store.update(vorgangResponse.data);
  });
}
</script>
