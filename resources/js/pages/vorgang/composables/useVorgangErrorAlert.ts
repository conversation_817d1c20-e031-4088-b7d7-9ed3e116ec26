import DOMPurify from 'dompurify';
import { computed, ComputedRef } from 'vue';
import { useRoute } from 'vue-router';

export const useVorgangErrorAlert = (): {
  alertText: ComputedRef<string>,
  hasAlert: ComputedRef<boolean>,
} => {
  const { query } = useRoute();

  const sanitizedAlertText = computed(() => {
    const dirtyAlertText = query.alert as string ?? '';

    if (dirtyAlertText.length === 0) {
      return '';
    }

    return DOMPurify.sanitize(dirtyAlertText, {
      ALLOWED_TAGS: [
        'br',
        'ul',
        'li',
        'b',
        'i',
      ],
    });
  });

  const hasAlert = computed(() => sanitizedAlertText.value.length > 0);

  return {
    alertText: sanitizedAlertText,
    hasAlert,
  };
};
