<template>
  <div class="flex h-screen flex-col overflow-hidden bg-gray-50 text-gray-800">
    <!-- header -->
    <AppHeader />

    <!-- toasts -->
    <div class="pointer-events-none absolute inset-x-0 top-0 z-[1000] flex justify-center p-8">
      <ToastList class="w-full sm:w-1/2 lg:w-1/3 2xl:w-1/4" />
    </div>

    <!-- current page -->
    <main class="flex grow overflow-hidden rounded-t-lg bg-white shadow-xl 2xl:container 2xl:mx-auto 2xl:mt-4">
      <router-view class="relative z-0 flex flex-1 overflow-hidden" />
    </main>
  </div>
</template>

<script setup lang="ts">
import { useIntercomChatBot } from '@demv_systems/feu-intercom';
import { useTagManager } from '@demv_systems/feu-tag-manager';
import { watch } from 'vue';

import AppHeader from '@/components/header/AppHeader.vue';
import ToastList from '@/components/toasts/ToastList.vue';
import useCurrentUser from '@/components/users/useCurrentUser';

const { user } = useCurrentUser();

watch(user, () => {
  if (user.value === undefined) {
    return;
  }

  if (import.meta.env.VITE_APP_INTERCOM_APP_ID !== undefined) {
    useIntercomChatBot().install(
      import.meta.env.VITE_APP_INTERCOM_APP_ID,
      user.value.attributes.intercomHash,
      user.value.attributes.externalId.toString(),
      {},
    );
  }

  if (import.meta.env.VITE_APP_GTM_ID !== undefined) {
    useTagManager().init(
      user.value.attributes.externalId.toString(),
      user.value.attributes.excludeFromAnalytics,
      import.meta.env.VITE_APP_GTM_ID,
    );
  }
});
</script>
