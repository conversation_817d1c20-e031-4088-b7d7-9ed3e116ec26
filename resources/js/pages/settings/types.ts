import * as JSONAPI from '../../types/jsonapi';

export type EditableSettingsAttributes = {
  hasLogoInLetter: boolean;
  hasEmailSignatureDelimiter: boolean;
  dateFormat: DateFormat;
  vorgangstitelSource: VorgangstitelSource;
};

export type UserSettingsResource = JSONAPI.ResourceObject<'userSettings', {
  canAccessMailImport: boolean,
  isMailSendingStatusActive: boolean,
  lastMailImportSyncTime: string | null,
  lastMailImportMailTime: string | null,
} & EditableSettingsAttributes>;

export enum DateFormat {
  Dynamisch = 'dynamisch',
  Einheitlich = 'einheitlich',
}

export enum VorgangstitelSource {
  Vorgangstyp = 'vorgangstyp',
  Vorlage = 'vorlage',
}
