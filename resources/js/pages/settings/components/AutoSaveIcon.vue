<template>
  <span
    class="text-gray-500"
    title="Ihre Änderungen werden automatisch gespeichert"
  >
    <DsIcon
      v-if="isSavingCount > 0"
      name="spinner-third"
      fixed-width
      spin
    />
    <DsIcon
      v-else
      name="circle-check"
      variant="regular"
      fixed-width
    />
  </span>
</template>

<script setup lang="ts">
import { DsIcon } from '@demvsystems/design-components';

import { useIsSaving } from '@/pages/settings/uses/useIsSaving';

const { isSavingCount } = useIsSaving();
</script>
