<template>
  <FormSection title="Firmenweit">
    <DsFormGroup
      validation-name="attributes.firmenKuerzel"
      label="Firmenkürzel"
    >
      <DsInput
        v-model="newKuerzel"
        class="w-24"
        placeholder="ABC"
        immediate
        title="Das Firmenkürzel Ihrer Vorgänge kann jederzeit verändert werden. Nach der Änderung wird die Vorgangsnummer sämtlicher neu angelegter Vorgänge innerhalb Ihrer Firma mit dem neuen Firmenkürzel versehen. Als Firmenkürzel gültig sind nur 3 Großbuchstaben (z.B. &quot;VER&quot;)."
        data-test="firmen-settings__firmenkuerzel"
        @input="debounceFirmenkuerzel"
      />
    </DsFormGroup>
  </FormSection>
</template>

<script setup lang="ts">
import { DsFormGroup, DsInput } from '@demvsystems/design-components';
import { ref } from 'vue';

import { extractErrors, isAxiosError, put } from '@/api';
import FormSection from '@/components/form/FormSection.vue';
import useCurrentUser from '@/components/users/useCurrentUser';
import { useIsSaving } from '@/pages/settings/uses/useIsSaving';
import { eventBus } from '@/store/resources/store';
import { FirmaRessource } from '@/store/resources/types';

const emit = defineEmits<{
  (event: 'emitErrors', value: Record<string, string[]> | []): void
}>();

const { firma } = useCurrentUser();
const { isSavingCount } = useIsSaving();

const newKuerzel = ref<string>(firma?.value?.attributes.kuerzel ?? '');

let firmaSettingsTimeout: ReturnType<typeof setTimeout> | null = null;

async function sendKuerzel() {
  const firmenId = firma?.value?.id;

  if (firmenId === undefined) {
    throw new Error('Firmenkuerzel konnte nicht gespeichert werden, weil die Firma fehlt.');
  }

  try {
    emit('emitErrors', []);
    isSavingCount.value++;

    const response = await put(`/firmenSettings/${firmenId}`, {
      data: {
        attributes: {
          firmenKuerzel: newKuerzel.value,
        },
      },
    });

    firma.value = response?.data.data as FirmaRessource;

    eventBus.emit('einstellungenGespeichert');
  } catch (e) {
    if (!isAxiosError(e) || e.response === undefined) {
      throw e;
    }

    emit('emitErrors', extractErrors(e.response.data?.errors ?? []));
  } finally {
    isSavingCount.value--;
  }
}

function debounceFirmenkuerzel() {
  newKuerzel.value = newKuerzel.value.toUpperCase();
  if (firmaSettingsTimeout) {
    clearTimeout(firmaSettingsTimeout);
  }

  if (newKuerzel.value === '') {
    return;
  }

  firmaSettingsTimeout = setTimeout(() => {
    void sendKuerzel();
  }, 1000);
}
</script>
