<template>
  <div class="space-y-7">
    <FormSection title="Brief">
      <DsSwitch
        v-model="userSettingsStore.hasLogoInLetter"
      >
        Firmenlogo in Brief darstellen
      </DsSwitch>
    </FormSection>

    <FormSection title="E-Mail">
      <DsSwitch
        v-model="userSettingsStore.hasEmailSignatureDelimiter"
        title="Fügt &quot;-- &quot; zwischen Nachrichtentext und Signatur ein. Dadurch können die meisten E-Mail-Programme eine Signatur automatisch erkennen und sie beim Beantworten nicht zitieren."
      >
        Signaturtrenner einfügen
        <DsIcon
          name="circle-info"
          variant="regular"
        />
      </DsSwitch>

      <DsFormGroup
        class="space-y-1.5"
      >
        <div class="space-x-3">
          <DsButton
            v-if="userSettingsStore.canAccessMailImport"
            :handler="importMails"
            :disabled="throttleImport"
            icon="inbox"
            variant="secondary"
          >
            E-Mail-Import starten
          </DsButton>

          <DsButton
            v-if="userSettingsStore.pwMailSettingsUrl !== null"
            :href="userSettingsStore.pwMailSettingsUrl"
            title="Ihre E-Mail-Einstellungen in Professional works"
            variant="outline"
            external
          >
            Zu den E-Mail-Einstellungen
          </DsButton>
        </div>

        <p class="text-sm text-gray-500">
          <DsAlert
            v-if="!userSettingsStore.canAccessMailImport"
            label="E-Mail-Empfang fehlgeschlagen"
            class="mb-1"
            type="warning"
          >
            Bitte überprüfen Sie ihre E-Mail-Einstellungen.
          </DsAlert>
          <template
            v-if="
              userSettingsStore.lastMailImportSyncTime !== null
                && userSettingsStore.lastMailImportMailTime !== null
            "
          >
            Der letzte erfolgreiche Email-Import erfolgte am
            <TimeText
              :value="userSettingsStore.lastMailImportSyncTime"
              class="font-semibold"
            />
            <br>
            Die letzte importierte E-Mail ist vom
            <TimeText
              :value="userSettingsStore.lastMailImportMailTime"
              class="font-semibold"
            />
          </template>
          <template v-else>
            In den letzten zwei Wochen wurden keine E-Mails importiert.
            <DsIcon
              name="circle-info"
              variant="regular"
              title="Es werden nur E-Mails importiert, welche einem Vorgang (anhand der Vorgangsnummer im Betreff) zugeordnet werden können und nicht älter als 14 Tage alt sind."
            />
          </template>
        </p>
      </DsFormGroup>
    </FormSection>

    <FormSection title="Darstellung">
      <DsFormGroup label="Datumsformat">
        <DsRadioGroup
          v-model="userSettingsStore.dateFormat"
          variant="button"
          class="mr-auto"
        >
          <DsRadioButton
            v-for="{label, value, dataTest} in dateFormats"
            :key="value"
            :value="value"
            :data-test="dataTest"
          >
            {{ label }}
          </DsRadioButton>
        </DsRadioGroup>
        <p
          v-if="dateFormatConfig"
          class="text-sm text-gray-500"
        >
          <span class="font-medium">
            {{ dateFormatConfig.label }}:
          </span>
          {{ dateFormatConfig.infoText }}
        </p>
      </DsFormGroup>
    </FormSection>

    <FormSection title="Vorgangserstellung">
      <DsFormGroup
        label="Automatischer Vorgangstitel"
        title="Wählen Sie hier aus, ob für den Vorgangstitel standardmäßig der Vorgangstyp oder der Name der Vorlage gesetzt wird."
      >
        <DsRadioGroup
          v-model="userSettingsStore.vorgangstitelSource"
          variant="button"
          class="mr-auto"
        >
          <DsRadioButton
            v-for="{label, value, dataTest} in vorgangstitelSources"
            :key="value"
            :value="value"
            :data-test="dataTest"
          >
            {{ label }}
          </DsRadioButton>
        </DsRadioGroup>
        <p
          v-if="vorgangstitelSourceConfig"
          class="text-sm text-gray-500"
        >
          <span class="font-medium">
            {{ vorgangstitelSourceConfig.label }}:
          </span>
          {{ vorgangstitelSourceConfig.infoText }}
        </p>
      </DsFormGroup>
    </FormSection>
  </div>
</template>

<script setup lang="ts">
import {
  DsAlert,
  DsButton,
  DsFormGroup,
  DsIcon,
  DsSwitch,
  DsRadioButton,
  DsRadioGroup,
} from '@demvsystems/design-components';
import { computed, ref } from 'vue';

import FormSection from '@/components/form/FormSection.vue';
import TimeText from '@/components/tags/TimeText.vue';
import useMailImport from '@/composables/useMailImport';
import { useUserSettingsStore } from '@/pages/settings/stores/userSettingsStore';

import { DateFormat, VorgangstitelSource } from '../types';

const userSettingsStore = useUserSettingsStore();

const { queueMailImport } = useMailImport();

type DateFormatConfig = {
  value: DateFormat,
  label: string,
  infoText: string,
  dataTest: string,
};

type VorgangstitelSourceConfig = {
  value: VorgangstitelSource,
  label: string,
  infoText: string,
  dataTest: string,
};

const throttleImport = ref(false);
const dateFormats: DateFormatConfig[] = [
  {
    value: DateFormat.Dynamisch,
    label: 'Dynamisch',
    infoText: 'Das Datum wird an allen Stellen flexibel auf den heutigen Tag ausgerichtet und je nach dazwischenliegender Zeitspanne anders dargestellt (z.B. „Nächster Donnerstag” oder „28. Juni”).',
    dataTest: 'user-settings__date-format__dynamisch',
  },

  {
    value: DateFormat.Einheitlich,
    label: 'Einheitlich',
    infoText: 'Das Datum (Fälligkeit, Erstelldatum, …) wird an allen Stellen einheitlich im Standardformat DD.MM.YYYY dargestellt.',
    dataTest: 'user-settings__date-format__einheitlich',
  },
];

const vorgangstitelSources: VorgangstitelSourceConfig[] = [
  {
    value: VorgangstitelSource.Vorgangstyp,
    label: 'Vorgangstyp',
    infoText: 'Der Vorgangstitel wird automatisch auf den Namen des ausgewählten Vorgangstypen gesetzt',
    dataTest: 'user-settings__vorgangstitel-source__vorgangstyp',
  },

  {
    value: VorgangstitelSource.Vorlage,
    label: 'Name der Vorlage',
    infoText: 'Der Vorgangstitel wird automatisch auf den Namen der ausgewählten Vorlage gesetzt',
    dataTest: 'user-settings__vorgangstitel-source__vorlage',
  },
];

async function importMails() {
  if (throttleImport.value) {
    return;
  }

  throttleImport.value = true;
  await queueMailImport();
  await userSettingsStore.load({ force: true });
  setTimeout(() => {
    throttleImport.value = false;
  }, 1000);
}

const dateFormatConfig = computed(() => {
  return dateFormats.find(({ value }) => value === userSettingsStore.dateFormat);
});

const vorgangstitelSourceConfig = computed(() => {
  return vorgangstitelSources.find(({ value }) => value === userSettingsStore.vorgangstitelSource);
});
</script>
