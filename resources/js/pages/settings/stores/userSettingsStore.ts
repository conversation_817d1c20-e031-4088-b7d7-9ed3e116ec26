import { useLocalStorage, watchDebounced } from '@vueuse/core';
import { defineStore } from 'pinia';
import { onMounted, readonly, ref, watch } from 'vue';

import { DateFormat, VorgangstitelSource } from '../types';
import { useIsSaving } from '../uses/useIsSaving';
import useUserSettingsApi from '../uses/useUserSettingsApi';

export const useUserSettingsStore = defineStore('userSettings', () => {
  const {
    getUserSettings,
    getUserDisplaySettings,
    putUserSettings,
    isLoading,
  } = useUserSettingsApi();

  const { isSavingCount } = useIsSaving();

  // editable settings
  const hasLogoInLetter = ref(false);
  const hasEmailSignatureDelimiter = ref(false);
  const dateFormat = useLocalStorage<DateFormat>('dateFormat', DateFormat.Dynamisch);
  const vorgangstitelSource = useLocalStorage<VorgangstitelSource>('vorgangstitelSource', VorgangstitelSource.Vorgangstyp);

  // other data
  const canAccessMailImport = ref(false);
  const isMailSendingStatusActive = ref(false);
  const lastMailImportSyncTime = ref<string | null>(null);
  const lastMailImportMailTime = ref<string | null>(null);
  const pwMailSettingsUrl = ref<string | null>(null);

  const isInitialized = ref(false);
  const areDisplaySettingsInitialized = ref(false);

  async function load({ force = false } = {}) {
    if (isLoading.value || (isInitialized.value && !force)) {
      return;
    }

    const settings = await getUserSettings();

    if (settings === null) {
      return;
    }

    hasLogoInLetter.value = settings.attributes.hasLogoInLetter;
    hasEmailSignatureDelimiter.value = settings.attributes.hasEmailSignatureDelimiter;
    dateFormat.value = settings.attributes.dateFormat;
    vorgangstitelSource.value = settings.attributes.vorgangstitelSource;

    canAccessMailImport.value = settings.attributes.canAccessMailImport;
    isMailSendingStatusActive.value = settings.attributes.isMailSendingStatusActive;
    lastMailImportSyncTime.value = settings.attributes.lastMailImportSyncTime;
    lastMailImportMailTime.value = settings.attributes.lastMailImportMailTime;
    pwMailSettingsUrl.value = settings.links?.pwMailSettings ?? null;

    isInitialized.value = true;
  }

  async function save() {
    if (!isInitialized.value || isLoading.value) {
      return;
    }

    isSavingCount.value++;
    await putUserSettings({
      hasLogoInLetter: hasLogoInLetter.value,
      hasEmailSignatureDelimiter: hasEmailSignatureDelimiter.value,
      dateFormat: dateFormat.value,
      vorgangstitelSource: vorgangstitelSource.value,
    });
    isSavingCount.value--;
  }

  function registerAutosaveWatcher() {
    watchDebounced([
      hasLogoInLetter,
      hasEmailSignatureDelimiter,
      dateFormat,
      vorgangstitelSource,
    ], save, { debounce: 1000 });
  }

  async function loadDisplaySettings() {
    if (areDisplaySettingsInitialized.value) {
      return;
    }

    const settings = await getUserDisplaySettings();

    dateFormat.value = settings?.attributes.dateFormat;

    areDisplaySettingsInitialized.value = true;
  }

  watch(isInitialized, registerAutosaveWatcher, { once: true });

  // display settings are needed as soon as the page is loaded
  onMounted(loadDisplaySettings);

  return {
    hasLogoInLetter,
    hasEmailSignatureDelimiter,
    dateFormat,
    vorgangstitelSource,

    canAccessMailImport,
    isMailSendingStatusActive,
    lastMailImportSyncTime,
    lastMailImportMailTime,
    pwMailSettingsUrl,

    isInitialized: readonly(isInitialized),
    load,
  };
});
