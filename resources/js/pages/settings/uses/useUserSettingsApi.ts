import { ref } from 'vue';

import { get, isAxiosError, put } from '@/api';
import { eventBus } from '@/store/resources/store';

import { EditableSettingsAttributes, UserSettingsResource } from '../types';

const isLoading = ref(false);

function emitAToast(e: Error) {
  eventBus.emit(
    isAxiosError(e) && e.response?.status === 422
      ? 'vorlageMissingFields'
      : 'error',
  );
}

async function getUserSettings(): Promise<UserSettingsResource | null> {
  try {
    isLoading.value = true;

    const response = await get<UserSettingsResource>('/userSettings');

    return response.data?.data ?? null;
  } catch (e) {
    emitAToast(e as Error);

    return null;
  } finally {
    isLoading.value = false;
  }
}

async function getUserDisplaySettings(): Promise<UserSettingsResource | null> {
  try {
    const response = await get<UserSettingsResource>('/userSettings', {
      fields: {
        'userSettings': ['dateFormat', 'vorgangstitelSource'],
      },
    });

    return response.data?.data ?? null;
  } catch (e) {
    emitAToast(e as Error);

    return null;
  }
}

async function putUserSettings(editableSettingsAttributes: EditableSettingsAttributes) {
  try {
    isLoading.value = true;

    await put<UserSettingsResource>('/userSettings', {
      data: {
        type: 'userSettings',
        attributes: editableSettingsAttributes,
      },
    });

    eventBus.emit('einstellungenGespeichert');
  } catch (e) {
    emitAToast(e as Error);
  } finally {
    isLoading.value = false;
  }
}

// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types
export default () => ({
  isLoading,
  getUserSettings,
  getUserDisplaySettings,
  putUserSettings,
});
