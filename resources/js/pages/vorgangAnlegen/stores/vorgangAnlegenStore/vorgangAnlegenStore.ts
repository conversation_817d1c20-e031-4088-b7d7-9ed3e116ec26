import { addWeeks } from 'date-fns';
import { defineStore, storeToRefs } from 'pinia';
import { computed, nextTick, ref, watch } from 'vue';

import { FIFTEEN_MB_IN_B, useFiles } from '@/components/form/useFiles';
import { gesellschaften } from '@/components/formBasisInfo/gesellschaft/gesellschaftenList';
import { useKunden } from '@/components/formBasisInfo/kunde/useKunden';
import { sparten } from '@/components/formBasisInfo/sparte/spartenList';
import { useVorlagen } from '@/composables/useVorlagen';
import {
  FONDSFINANZ_GESELLSCHAFT_EXTERNAL_ID,
  VORGANGSTYP_ANTRAG_EINREICHEN_ID,
} from '@/constants/ids';
import { useUserSettingsStore } from '@/pages/settings/stores/userSettingsStore';
import { VorgangstitelSource } from '@/pages/settings/types';
import {
  useContentUpdateConfirmationModal,
} from '@/pages/vorgangAnlegen/composables/useContentUpdateConfirmationModal';
import { useParticipants } from '@/pages/vorgangAnlegen/stores/vorgangAnlegenStore/composables/useParticipants';
import {
  useVertriebsweg,
} from '@/pages/vorgangAnlegen/stores/vorgangAnlegenStore/composables/useVertriebsweg';
import { Versandart } from '@/pages/vorlagen/types';
import {
  BezugElement,
  BriefAddressElement,
  EmailElement,
  KundeResource,
  VertragResource,
} from '@/store/resources/types';
import { useVorgangstypenStore } from '@/stores/vorgangstypenStore';
import { getSenderMailAddress } from '@/utils/getUserMailAddress';

import {
  BriefAbsender,
  BriefEmpfaenger,
  FirstTimelineElementType,
  LockableField,
} from '../../types';

import { useBriefEmpfaenger } from './composables/useBriefEmpfaenger';
import { useInitVorgangAnlegenForm } from './composables/useInitVorgangAnlegenForm';
import { useSubmitVorgangAnlegenForm } from './composables/useSubmitVorgangAnlegenForm';

/**
 * This store contains all form data from the vorgangAnlegen form, that
 * will be sent to backend. It is also responsible for all reactivity
 * between those form attributes.
 */
export const useVorgangAnlegenStore = defineStore('vorgangAnlegen', () => {
  /* --- stores --- */

  const vorgangstypenStore = useVorgangstypenStore();
  const { vorgangstypen } = storeToRefs(useVorgangstypenStore());
  const userSettingsStore = useUserSettingsStore();

  /* --- composables --- */

  const {
    getKunde,
  } = useKunden();

  const {
    files,
    externalFiles,
    uploadedFiles,
    totalFileSize,
    deleteSavedFiles,
    removeFileFromExistingDocuments,
    saveFiles,
    clearSavedFileIds,
  } = useFiles({ preview: true });

  const {
    briefEmpfaenger,
    briefEmpfaengerType,
    resetBriefEmpfaenger,
  } = useBriefEmpfaenger();

  const {
    initFromQueryParams,
    initFromVorgaenger,
  } = useInitVorgangAnlegenForm();

  const {
    submit,
    submitAsEntwurf,
  } = useSubmitVorgangAnlegenForm();

  const {
    setVertriebswegId,
    setVertriebswegIdByGesellschaft,
  } = useVertriebsweg();

  const {
    open: openContentUpdateConfirmationModal,
  } = useContentUpdateConfirmationModal();

  const {
    bearbeiterIds,
    bearbeiter,
    beobachterIds,
    beobachter,
    reset: resetParticipants,
  } = useParticipants();

  /* --- data --- */

  const errors = ref<Record<string, string[]>>({});

  const ownerId = ref<string>();

  const firstTimelineElementType = ref<FirstTimelineElementType>('korrespondenzen');
  const vorgaengerId = ref<string>();

  // from backend after submit
  const id = ref<string>();
  const vorgangsnummer = ref<string>();
  const firstTimelineElementId = ref<string>();

  /* Basis-Informationen */
  const versandart = ref<Versandart>(Versandart.Mail);
  const vorgangstitel = ref<string>('');

  const briefVorgangstypId = ref<string | null>(null);
  const mailVorgangstypId = ref<string | null>(null);
  const vorgangstypId = computed<string | null>({
    get: () => {
      if (versandart.value === Versandart.Mail) {
        return mailVorgangstypId.value;
      }

      return briefVorgangstypId.value;
    },
    set: (newValue: string | null) => {
      if (versandart.value === Versandart.Mail) {
        mailVorgangstypId.value = newValue;

        return;
      }

      briefVorgangstypId.value = newValue;
    },
  });

  const isSparteRequired = computed(() => {
    return (vorgangstypId.value === VORGANGSTYP_ANTRAG_EINREICHEN_ID);
  });

  const faelligAt = ref<Date | null>(addWeeks(new Date(), 2));

  const kunde = ref<KundeResource | null>(null);
  const isKundeLoading = ref(false);
  const kundeId = computed<string | null>({
    get: () => kunde.value?.id ?? null,
    set: (newKundeId) => {
      if (newKundeId === null) {
        kunde.value = null;

        return;
      }

      isKundeLoading.value = true;
      void getKunde(newKundeId).then((newKunde) => {
        kunde.value = newKunde;
      }).finally(() => {
        isKundeLoading.value = false;
      });
    },
  });

  const gesellschaftId = ref<string | null>(null);
  const fondFinanz = computed(() => gesellschaften.value.find(
    (element) => element.attributes.externalId === FONDSFINANZ_GESELLSCHAFT_EXTERNAL_ID,
  ));

  const vertriebswegGesellschaftId = ref<string | null>(null);
  const vertriebswegPoolId = ref<string | null>(null);
  const vertriebswegArt = ref<'pool' | 'gesellschaft'>('gesellschaft');

  const vertriebswegIdProxy = computed({
    get: () => vertriebswegArt.value === 'pool'
      ? vertriebswegPoolId.value
      : vertriebswegGesellschaftId.value,
    set: (newId) => {
      if (newId === null) {
        vertriebswegPoolId.value = null;
        vertriebswegGesellschaftId.value = null;

        return;
      }

      const newVertriebsweg = gesellschaften.value.find(
        (element) => element.id === newId,
      );

      if (newVertriebsweg === undefined) {
        throw new Error('no vertriebsweg found');
      }

      if (newVertriebsweg?.attributes.isPool) {
        vertriebswegPoolId.value = newId;
        vertriebswegArt.value = 'pool';
      } else {
        vertriebswegGesellschaftId.value = newId;
        vertriebswegPoolId.value = fondFinanz.value?.id ?? null;
        vertriebswegArt.value = 'gesellschaft';
      }
    },
  });

  const vertriebsweg = computed(() => gesellschaften.value.find(
    (element) => element.id === vertriebswegIdProxy.value,
  ));

  const sparteId = ref<string | null>(null);
  const sparte = computed(() => sparten.value.find(
    (element) => element.id === sparteId.value,
  ));

  const vertraege = ref<VertragResource[]>([]);

  const lockedFields = ref<Set<LockableField>>(new Set());

  /* Empfaenger */
  // Mail
  const mailEmpfaenger = ref<EmailElement[]>([]);
  const mailCc = ref<EmailElement[]>([]);
  const mailBcc = ref<EmailElement[]>([]);
  const mailHasCcAndBcc = ref<boolean>(false);

  const mailOwnerSenderAddress = ref<EmailElement>();

  const briefAbsender = ref<BriefAbsender>(BriefAbsender.Makler);

  const empfaenger = computed<EmailElement[] | BriefAddressElement>(() => {
    if (versandart.value === Versandart.Mail) {
      return mailEmpfaenger.value;
    }

    return briefEmpfaenger.value;
  });

  // Nachricht
  const briefDatum = ref<Date | string | undefined>(new Date());
  const betreff = ref<string | null>(null);
  const content = ref<string>('');
  const contentOrBetreffWasEdited = ref<boolean>(false);

  // Dokumente
  const kundendokumentIds = ref<string[]>([]);

  // Bezug
  const bezug = ref<BezugElement | null>(null);

  // from backend after submit (tags are replaced here)
  const renderedBetreff = ref<string>();
  const renderedContent = ref<string>();

  const canNotSubmit = computed(() => {
    const hasImagesAndIsBrief = content.value.includes('<img')
      && versandart.value === Versandart.Brief;
    const isTotalFileSizeTooLarge = totalFileSize.value > FIFTEEN_MB_IN_B;

    return isTotalFileSizeTooLarge || hasImagesAndIsBrief;
  });

  const resetEmpfaengerFields = (): void => {
    mailEmpfaenger.value = [];
    mailCc.value = [];
    mailBcc.value = [];
    mailHasCcAndBcc.value = false;

    resetBriefEmpfaenger();
  };

  const {
    anrede,
    vorlagen,
    selectedVorlageId,
    selectedVorlage,
    selectedVorlageMail,
    selectedVorlageBrief,
    hasVorlageInformal,
    vorlageBetreff,
    vorlageContent,
    updateVorlagen,
  } = useVorlagen({
    versandart,
    kunde,
    vertriebsweg,
    sparte,
    ownerId,
    vorgangstypId,
    mailEmpfaenger,
    mailHasCcAndBcc,
    mailCc,
    mailBcc,
    briefEmpfaengerType,
    briefAbsender,
    resetEmpfaengerFields,
  });

  /* --- functions --- */

  const reset = (): void => {
    id.value = undefined;
    errors.value = {};

    ownerId.value = undefined;
    firstTimelineElementType.value = 'korrespondenzen';
    firstTimelineElementId.value = undefined;
    vorgaengerId.value = undefined;

    contentOrBetreffWasEdited.value = false;

    versandart.value = Versandart.Mail;
    vorgangstitel.value = '';

    briefVorgangstypId.value = null;
    mailVorgangstypId.value = null;

    faelligAt.value = addWeeks(new Date(), 2);

    kundeId.value = null;
    gesellschaftId.value = null;
    void nextTick().then(() => {
      vertriebswegPoolId.value = fondFinanz.value?.id ?? null;
    });
    vertriebswegArt.value = 'gesellschaft';
    vertriebswegGesellschaftId.value = null;
    sparteId.value = null;
    vertraege.value = [];
    lockedFields.value.clear();

    resetParticipants();

    resetEmpfaengerFields();

    mailOwnerSenderAddress.value = undefined;

    briefAbsender.value = BriefAbsender.Makler;
    briefDatum.value = new Date();

    vorgangsnummer.value = undefined;

    betreff.value = null;
    content.value = '';

    files.value = [];
    clearSavedFileIds();
    kundendokumentIds.value = [];

    bezug.value = null;
  };

  function setTitelBySource() {
    if (userSettingsStore.vorgangstitelSource === VorgangstitelSource.Vorlage) {
      vorgangstitel.value = selectedVorlage.value?.attributes.name ?? '';
    } else {
      const vorgangstyp = vorgangstypId.value !== null
        ? vorgangstypenStore.findById(vorgangstypId.value)
        : undefined;

      if (vorgangstyp !== undefined) {
        vorgangstitel.value = vorgangstyp.attributes.titel;
      }
    }
  }

  /* --- reactivity --- */

  // if vorlage or anrede changes: set AutomatischerVorgangstitel
  watch([selectedVorlageId, anrede], () => {
    if (contentOrBetreffWasEdited.value) {
      openContentUpdateConfirmationModal();

      return;
    }

    setTitelBySource();

    betreff.value = vorlageBetreff.value ?? null;
    content.value = vorlageContent.value ?? '';
  });

  // vorgangstyp changes: set vertriebsweg
  watch([vorgangstypId, vorgangstypen], async () => {
    const vorgangstyp = vorgangstypId.value !== null
    && vorgangstypId.value !== undefined
      ? vorgangstypenStore.findById(vorgangstypId.value)
      : null;

    if (vorgangstyp !== null
      && vorgangstyp !== undefined
      && (vorgangstyp?.attributes.empfaengerTyp === 'vertriebsweg'
        || vorgangstyp?.attributes.empfaengerTyp === 'gesellschaft')) {
      if (vertraege.value[0]?.id !== undefined) {
        await setVertriebswegId(vertraege.value[0].id, vorgangstyp.id);
      } else if (gesellschaftId.value !== null) {
        await setVertriebswegIdByGesellschaft(gesellschaftId.value, vorgangstyp.id);
      }
    } else {
      vertriebswegIdProxy.value = null;
    }
  });

  // vertraege changes: gesellschaft of first selected vertrag gets selected and set vertriebsweg
  let keepGesellschaftOnVertraegeUpdate = false;
  watch(vertraege, async (newVertraege, oldVertraege) => {
    if (newVertraege.length > 0) {
      sparteId.value = newVertraege[0]?.relationships?.sparte?.data?.id ?? null;
      gesellschaftId.value = newVertraege[0]?.relationships?.bafinGesellschaft?.data?.id
        ?? newVertraege[0]?.relationships?.gesellschaft?.data?.id ?? null;

      const vorgangstyp = vorgangstypId.value !== null
        ? vorgangstypenStore.findById(vorgangstypId.value)
        : null;

      // todo
      if (vorgangstyp?.attributes.empfaengerTyp === 'vertriebsweg' && newVertraege[0]?.id !== undefined) {
        await setVertriebswegId(newVertraege[0].id, vorgangstyp.id);
      } else if (vorgangstyp?.attributes.empfaengerTyp === 'gesellschaft' && gesellschaftId.value !== null) {
        await setVertriebswegIdByGesellschaft(gesellschaftId.value, vorgangstyp.id);
      }
    } else if (oldVertraege.length > 0) {
      sparteId.value = null;
      gesellschaftId.value = null;
    }

    if (keepGesellschaftOnVertraegeUpdate) {
      keepGesellschaftOnVertraegeUpdate = false;

      return;
    }
  }, { deep: true });

  // gesellschaft changes: set vertriebsweg
  // we also need to watch gesellschaften, because the gesellschaftId
  // can be set (e.g. by url) before the gesellschaften are loaded
  watch([gesellschaftId, gesellschaften], async () => {
    if (gesellschaften.value.length === 0) {
      return;
    }

    const vorgangstyp = vorgangstypId.value !== null
      ? vorgangstypenStore.findById(vorgangstypId.value)
      : null;

    if (gesellschaftId.value !== null
      && vorgangstyp !== null
      && (vorgangstyp?.attributes.empfaengerTyp === 'vertriebsweg'
        || vorgangstyp?.attributes.empfaengerTyp === 'gesellschaft')) {
      await setVertriebswegIdByGesellschaft(vorgangstyp.id, gesellschaftId.value);
    } else {
      vertriebswegIdProxy.value = gesellschaftId.value;
    }
  }, { deep: true });

  watch(fondFinanz, () => {
    if (vertriebswegPoolId.value === null) {
      vertriebswegPoolId.value = fondFinanz.value?.id ?? null;
    }
  });

  // kunde changes: clear selected vertraege
  watch(kundeId, () => {
    if (vertraege.value.length === 0 || !vertraege.value.some((vertrag) =>
      !vertrag.relationships?.kunden?.data.some(
        (elem) => elem.id === kundeId.value,
      ),
    )) {
      // don't clear if no vertraege were selected because
      // that means gesellschaft was selected manually by user
      // and also: clear only if there is a vertrag that does not contain given kundenId
      // that means, that kunde was set after vertraege
      return;
    }

    vertraege.value = [];
  });

  // briefEmpfaengerType changes to Kunde: set briefAbsender to Makler
  watch([briefEmpfaengerType, kundeId], ([newBriefEmpfaengerType, newKundeId]) => {
    if (newBriefEmpfaengerType === BriefEmpfaenger.Kunde || newKundeId === null) {
      briefAbsender.value = BriefAbsender.Makler;
    }
  });

  // Update mailOwnerSenderAddress when ownerId changes
  watch(ownerId, (newOwnerId) => {
    void getSenderMailAddress(newOwnerId).then((emailElement) => {
      mailOwnerSenderAddress.value = emailElement;
    });
  }, { immediate: true });

  // reset cc and bcc values when toggled
  watch(mailHasCcAndBcc, (newHasCcAndBcc) => {
    if (!newHasCcAndBcc) {
      mailCc.value = [];
      mailBcc.value = [];
    }
  });

  // reset vorgangsattributes, so we don't save the wrong type by accident.
  watch(firstTimelineElementType, (newType, oldType) => {
    if (newType === oldType) {
      return;
    }

    if (newType === 'kommentare') {
      vertriebswegIdProxy.value = null;
    }

    if (vorgangsnummer.value !== undefined) {
      vorgangsnummer.value = undefined;
    }

    if (firstTimelineElementId.value !== undefined) {
      firstTimelineElementId.value = undefined;
    }

    if (id.value !== undefined) {
      id.value = undefined;
    }
  });

  // clear errors when versandart or vorgangsart changes
  watch([versandart, firstTimelineElementType], () => {
    errors.value = {};
  });

  return {
    errors,
    canNotSubmit,

    id,
    ownerId,
    vorgangsnummer,
    firstTimelineElementType,
    firstTimelineElementId,
    vorgaengerId,

    // Basis-Informationen
    versandart,
    vorgangstitel,
    vorgangstypId,
    faelligAt,
    kundeId,
    kunde,
    isKundeLoading,
    gesellschaftId,
    fondFinanz,
    vertriebsweg,
    vertriebswegArt,
    vertriebswegIdProxy,
    vertriebswegPoolId,
    vertriebswegGesellschaftId,
    vertraege,
    sparteId,
    sparte,
    bearbeiterIds,
    bearbeiter,
    beobachterIds,
    beobachter,
    isSparteRequired,
    lockedFields,

    // Empfaenger
    mailOwnerSenderAddress,
    mailEmpfaenger,
    mailCc,
    mailBcc,
    mailHasCcAndBcc,
    briefEmpfaenger,
    briefEmpfaengerType,
    briefAbsender,
    empfaenger,

    // Nachricht
    betreff,
    content,
    contentOrBetreffWasEdited,
    renderedBetreff,
    renderedContent,
    briefDatum,
    files,
    totalFileSize,
    uploadedFiles,
    externalFiles,

    // Dokumente
    kundendokumentIds,

    // Bezug
    bezug,

    // Vorlage
    vorlagen,
    vorlageBetreff,
    vorlageContent,
    selectedVorlageId,
    selectedVorlage,
    selectedVorlageMail,
    selectedVorlageBrief,
    anrede,
    hasVorlageInformal,
    updateVorlagen,

    // functions
    saveFiles: async () => saveFiles(
      firstTimelineElementId.value ?? '',
      firstTimelineElementType.value,
    ),
    removeFileFromExistingDocuments,

    submit,
    submitAsEntwurf,

    resetBriefEmpfaenger,
    resetEmpfaengerFields,
    reset,
    initFromQueryParams,
    initFromVorgaenger,
    deleteSavedFiles,
    setTitelBySource,
  };
});
