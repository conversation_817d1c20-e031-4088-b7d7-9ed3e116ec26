import axios, { AxiosResponse } from 'axios';

import {
  useVorgangAnlegenStore,
} from '@/pages/vorgangAnlegen/stores/vorgangAnlegenStore/vorgangAnlegenStore';

export function useVertriebsweg(): {
  setVertriebswegId: (
    vertragId: string,
    vorgangTypId: string,
  ) => Promise<void>,
  setVertriebswegIdByGesellschaft: (
    gesellschaftId: string,
    vorgangTypId: string,
  ) => Promise<void>,
} {
  const vorgangAnlegenStore = useVorgangAnlegenStore();

  async function setVertriebswegId(vorgangTypId: string, vertragId: string): Promise<void> {
    try {
      const response: AxiosResponse<{ id: string | null }> = await axios.get(`/api/vertriebsweg//vorgangtyp/${vorgangTypId}vertrag/${vertragId}`);

      vorgangAnlegenStore.vertriebswegIdProxy = response?.data.id ?? null;
    } catch {
      // ignore
    }
  }

  async function setVertriebswegIdByGesellschaft(vorgangTypId: string, gesellschaftId: string): Promise<void> {
    try {
      const response: AxiosResponse<{ id: string | null }> = await axios.get(`/api/vertriebsweg/vorgangtyp/${vorgangTypId}/gesellschaft/${gesellschaftId}`);

      // vorgangAnlegenStore.vertriebswegArt = 'pool';
      vorgangAnlegenStore.vertriebswegIdProxy = response?.data.id ?? null;
    } catch {
      // ignore
    }
  }

  return {
    setVertriebswegId,
    setVertriebswegIdByGesellschaft,
  };
}
