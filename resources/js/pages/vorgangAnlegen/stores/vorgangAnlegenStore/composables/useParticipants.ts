import { computed, ref, watch } from 'vue';

import useCurrentUser from '@/components/users/useCurrentUser';
import useHierarchy from '@/components/users/useHierarchy';
import { useZustaendigerVermittler } from '@/composables/useZustaendigerVermittler';
import { KundeResource } from '@/store/resources/types';

import { useVorgangAnlegenStore } from '../vorgangAnlegenStore';

// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types
export function useParticipants() {
  const { visibleFirmenMembers } = useHierarchy();
  const { user } = useCurrentUser();
  const {
    loadZustaendigerVermittler,
    clearZustaendigerVermittler,
    zustaendigerVermittler,
  } = useZustaendigerVermittler();

  const vorgangAnlegenStore = useVorgangAnlegenStore();

  /* --- data --- */
  const bearbeiterIds = ref<string[]>([]);
  const bearbeiter = computed(() => visibleFirmenMembers.value.filter(
    (element) => bearbeiterIds.value?.includes(element.id),
  ));

  const beobachterIds = ref<string[]>([]);
  const beobachter = computed(() => visibleFirmenMembers.value.filter(
    (element) => beobachterIds.value?.includes(element.id),
  ));

  /* --- functions --- */
  const reset = (): void => {
    bearbeiterIds.value = [];
    beobachterIds.value = [];
    if (user.value !== undefined) {
      bearbeiterIds.value = [user.value.id];
      beobachterIds.value = [user.value.id];
    }

    clearZustaendigerVermittler();
  };

  /* --- watchers --- */

  // Initialize bearbeiter and beobachter with current user
  watch(user, (newUser) => {
    if (newUser !== undefined) {
      bearbeiterIds.value = [newUser.id];
      beobachterIds.value = [newUser.id];
    }
  }, { immediate: true });

  // kunde changes: set/remove zustaendiger Vermittler as beobachter
  watch(() => vorgangAnlegenStore.kunde, async (newKunde: KundeResource | null): Promise<void> => {
    if (![user.value?.id, vorgangAnlegenStore.ownerId].includes(zustaendigerVermittler.value?.id)) {
      beobachterIds.value = beobachterIds.value.filter(
        (bid) => bid !== zustaendigerVermittler.value?.id,
      );
    }

    clearZustaendigerVermittler();

    if (newKunde === null) {
      return;
    }

    await loadZustaendigerVermittler(newKunde.id);

    const zustaendigerVermittlerId = zustaendigerVermittler.value?.id;
    if (zustaendigerVermittlerId && !beobachterIds.value.includes(zustaendigerVermittlerId)) {
      beobachterIds.value.push(zustaendigerVermittlerId);
    }
  });

  // ownerId changes: add/remove owner from bearbeiter/beobachter
  watch(() => vorgangAnlegenStore.ownerId, (newOwnerId, oldOwnerId) => {
    if (newOwnerId !== undefined) {
      bearbeiterIds.value.push(newOwnerId);
      beobachterIds.value.push(newOwnerId);
    }

    if (oldOwnerId !== undefined && oldOwnerId !== newOwnerId) {
      bearbeiterIds.value = bearbeiterIds.value.filter((bid) => bid != oldOwnerId);

      if (zustaendigerVermittler.value?.id !== oldOwnerId) {
        beobachterIds.value = beobachterIds.value.filter((bid) => bid != oldOwnerId);
      }
    }
  }, { immediate: true });

  return {
    // state
    bearbeiterIds,
    bearbeiter,
    beobachterIds,
    beobachter,

    // functions
    reset,
    clearZustaendigerVermittler,
    loadZustaendigerVermittler,
  };
}
