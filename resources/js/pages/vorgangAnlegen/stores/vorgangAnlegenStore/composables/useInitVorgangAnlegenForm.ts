import { useRoute } from 'vue-router';

import { get } from '@/api';
import { FirstTimelineElementType, LockableField } from '@/pages/vorgangAnlegen/types';
import { Versandart } from '@/pages/vorlagen/types';
import {
  VertragResource,
  VorgangResource,
  VorgangTypResource,
} from '@/store/resources/types';
import getRelation from '@/utils/getRelation';

import { useVorgangAnlegenStore } from '../vorgangAnlegenStore';

/**
 * This composable contains the logic for initializing the vorgangAnlegenStore,
 * either by path parameters or by a Vorgaenger-Vorgang.
 */
export function useInitVorgangAnlegenForm(): {
  initFromQueryParams: () => Promise<void>,
  initFromVorgaenger: (newVorgaenger: VorgangResource) => Promise<void>,
} {
  const vorgangAnlegenStore = useVorgangAnlegenStore();

  async function initVertraege(vertraegeIds: string[], kundeId: string): Promise<void> {
    for (const id of vertraegeIds) {
      if (id === '') {
        continue;
      }

      try {
        const vertrag = (await get<VertragResource>(
          `/vertraege/${id}`,
          {
            include: ['kunden'],
            fields: {
              kunden: ['name'],
            },
            filter: {
              ...(kundeId !== null ? {
                kunden: kundeId,
              } : {}),
            },
          },
        )).data.data;

        if (vertrag === undefined) {
          continue;
        }

        vorgangAnlegenStore.vertraege.push(vertrag);
      } catch {
        // ignore
      }
    }
  }

  async function initFromQueryParams(): Promise<void> {
    vorgangAnlegenStore.reset();

    const query = useRoute().query;

    if (Object.keys(query).length === 0) {
      return;
    }

    if (query['vorgangAnlegen[vorgangsart]'] !== undefined) {
      vorgangAnlegenStore.firstTimelineElementType = query['vorgangAnlegen[vorgangsart]'] as FirstTimelineElementType;
    }

    if (query['vorgangAnlegen[lock]'] !== undefined) {
      (query['vorgangAnlegen[lock]'] as string).split(',').forEach((field: string) => {
        if (!Object.values<string>(LockableField).includes(field)) {
          return;
        }

        vorgangAnlegenStore.lockedFields.add(<LockableField>field);
      });
    }

    if (query['vorgangAnlegen[faelligAt]'] !== undefined) {
      vorgangAnlegenStore.faelligAt = new Date(query['vorgangAnlegen[faelligAt]'] as string);
    }

    const kundeId = query['vorgangAnlegen[kunde]'] === undefined
      ? undefined
      : query['vorgangAnlegen[kunde]'] as string;
    if (kundeId !== undefined) {
      vorgangAnlegenStore.kundeId = kundeId;
    }

    if (query['vorgangAnlegen[sparte]'] !== undefined) {
      vorgangAnlegenStore.sparteId = query['vorgangAnlegen[sparte]'] as string;
    }

    if (Object.values<string>(Versandart).includes(query['vorgangAnlegen[versandart]'] as string)) {
      vorgangAnlegenStore.versandart = query['vorgangAnlegen[versandart]'] as Versandart;
    }

    if (query['vorgangAnlegen[vorgangstyp]'] !== undefined) {
      vorgangAnlegenStore.vorgangstypId = query['vorgangAnlegen[vorgangstyp]'] as string;
    }

    if (query['vorgangAnlegen[vertraege]'] !== undefined && kundeId !== undefined) {
      const vertraegeIds = (query['vorgangAnlegen[vertraege]'] as string).split(',');

      await initVertraege(vertraegeIds, kundeId);
    } else if (query['vorgangAnlegen[gesellschaft]'] !== undefined) {
      vorgangAnlegenStore.gesellschaftId = query['vorgangAnlegen[gesellschaft]'] as string;
    }

    if (query['vorgangAnlegen[kundendokumente]'] !== undefined) {
      vorgangAnlegenStore.kundendokumentIds = (query['vorgangAnlegen[kundendokumente]'] as string).split(',');
    }

    if (query['vorgangAnlegen[bezugBenennung]'] !== undefined) {
      vorgangAnlegenStore.bezug = { benennung: query['vorgangAnlegen[bezugBenennung]'] as string };

      if (query['vorgangAnlegen[bezugReferenz]'] !== undefined) {
        vorgangAnlegenStore.bezug.referenz = query['vorgangAnlegen[bezugReferenz]'] as string;
      }

      if (query['vorgangAnlegen[bezugUrl]'] !== undefined) {
        vorgangAnlegenStore.bezug.url = new URL(query['vorgangAnlegen[bezugUrl]'] as string).toString();
      }
    }
  }

  async function initFromVorgaenger(newVorgaenger: VorgangResource): Promise<void> {
    vorgangAnlegenStore.reset();

    vorgangAnlegenStore.vorgaengerId = newVorgaenger.id;

    const kundeId = newVorgaenger?.relationships?.kunde?.data?.id ?? null;
    vorgangAnlegenStore.kundeId = kundeId;
    vorgangAnlegenStore.sparteId = (
      newVorgaenger?.relationships?.sparte?.data?.id ?? null
    );

    if (newVorgaenger?.relationships?.vorgangTyp?.data?.id !== undefined) {
      const vorgangstyp = await getRelation<VorgangTypResource>(
        '/api/vorgangTypen',
        newVorgaenger?.relationships?.vorgangTyp?.data?.id,
      );

      const isOnlyBrief = !vorgangstyp?.attributes.isEmail && vorgangstyp?.attributes.isBrief;

      vorgangAnlegenStore.versandart = isOnlyBrief
        ? Versandart.Brief
        : Versandart.Mail;
    }

    if (newVorgaenger?.relationships?.vertraege?.data !== undefined && kundeId !== null) {
      const vertraegeIds = newVorgaenger?.relationships?.vertraege?.data.map(
        (vertrag) => vertrag.id,
      );

      await initVertraege(vertraegeIds, kundeId);
    } else {
      vorgangAnlegenStore.gesellschaftId = (
        newVorgaenger?.relationships?.gesellschaft?.data?.id ?? null
      );
    }
  }

  return {
    initFromQueryParams,
    initFromVorgaenger,
  };
}
