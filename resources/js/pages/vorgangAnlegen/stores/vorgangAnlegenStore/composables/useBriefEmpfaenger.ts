import { ignorableWatch } from '@vueuse/core';
import { isEqual } from 'lodash-es';
import { Ref, ref, watch } from 'vue';

import { get } from '@/api';
import { BriefAddressElement, PwAddressResource, SalutationType } from '@/store/resources/types';
import getDemvGesellschaft from '@/utils/getDemvGesellschaft';

import { BriefEmpfaenger } from '../../../types';
import { useVorgangAnlegenStore } from '../vorgangAnlegenStore';

/**
 * This composable contains briefEmpfaenger, briefEmpfaengerType and
 * the address of Kunde, Gesellschaft and DEMV.
 * It watches for changes in Kunde or Gesellschaft and updates their address.
 */
export const useBriefEmpfaenger = (): {
  briefEmpfaenger: Ref<BriefAddressElement>,
  briefEmpfaengerType: Ref<BriefEmpfaenger>,
  resetBriefEmpfaenger: () => void,
} => {
  // use vorgangAnlegenStore to watch for changes in Kunde or Gesellschaft
  const vorgangAnlegenStore = useVorgangAnlegenStore();

  /* --- data --- */

  const emptyAddress = <BriefAddressElement>{
    name: '',
    titel: '',
    fax: '',
    adresszeile1: '',
    adresszeile2: '',
    plz: '',
    stadt: '',
    land: 'Deutschland',
  };
  const getEmptyAddress = () => ({ ...emptyAddress });

  const briefEmpfaenger = ref<BriefAddressElement>(getEmptyAddress());
  const briefEmpfaengerType = ref<BriefEmpfaenger>(BriefEmpfaenger.Eigene);

  // the address of the selected kunde
  const selectedKundeAddress = ref<BriefAddressElement>(getEmptyAddress());
  // the address of the selected gesellschaft
  const selectedGesellschaftAddress = ref<BriefAddressElement>(
    getEmptyAddress(),
  );
  // demv address
  const demvAddress = ref<BriefAddressElement>(getEmptyAddress());

  /* --- functions --- */

  const resetBriefEmpfaenger = () => {
    briefEmpfaenger.value = getEmptyAddress();
  };

  const toBriefAddressElement = (
    name: string,
    titel?: string,
    resource?: PwAddressResource,
    salutationType?: SalutationType,
  ) => ({
    name,
    titel,
    salutationType,
    fax: resource?.attributes.fax ?? '',
    adresszeile1: (
      `${resource?.attributes.strasse} ${resource?.attributes.hausnummer}`
    ),
    adresszeile2: resource?.attributes.adresszusatz ?? '',
    plz: resource?.attributes.plz ?? '',
    stadt: resource?.attributes.ort ?? '',
    land: resource?.attributes.country?.name ?? 'Deutschland',
  });

  const updateGesellschaftAddress = async (): Promise<void> => {
    selectedGesellschaftAddress.value = getEmptyAddress();

    const newGesellschaftId = vorgangAnlegenStore.gesellschaftId;

    if (newGesellschaftId === null) {
      return;
    }

    try {
      const response = await get<PwAddressResource>(
        `/gesellschaften/${newGesellschaftId}/address`,
      );

      if (response.data?.data !== undefined) {
        selectedGesellschaftAddress.value = toBriefAddressElement(
          vorgangAnlegenStore.vertriebsweg?.attributes.name ?? '',
          undefined,
          response.data?.data,
        );
      }
    } catch (e) {
      selectedGesellschaftAddress.value = getEmptyAddress();
    }
  };

  const updateKundeAddress = async (): Promise<void> => {
    selectedKundeAddress.value = getEmptyAddress();

    const newKundeId = vorgangAnlegenStore.kundeId;

    if (newKundeId === null) {
      return;
    }

    selectedKundeAddress.value.name = vorgangAnlegenStore.kunde?.attributes.name ?? '';
    selectedKundeAddress.value.titel = vorgangAnlegenStore.kunde?.attributes.title ?? '';
    selectedKundeAddress.value.salutationType = (
      vorgangAnlegenStore.kunde?.attributes.salutationType
    );

    try {
      const response = await get<PwAddressResource>(
        `/kunden/${newKundeId}/address`,
      );

      if (response.data?.data !== undefined) {
        selectedKundeAddress.value = toBriefAddressElement(
          vorgangAnlegenStore.kunde?.attributes.name ?? '',
          vorgangAnlegenStore.kunde?.attributes.title ?? '',
          response.data?.data,
          vorgangAnlegenStore.kunde?.attributes.salutationType,
        );
      }
    } catch (e) {
      // ignore
    }
  };

  const getAddressForSelectedEmpfaengerType = (): BriefAddressElement => {
    let newAddress: BriefAddressElement;

    switch (briefEmpfaengerType.value) {
      case BriefEmpfaenger.Demv:
        newAddress = demvAddress.value;

        break;

      case BriefEmpfaenger.Gesellschaft:
        newAddress = selectedGesellschaftAddress.value;

        break;

      case BriefEmpfaenger.Kunde:
        newAddress = selectedKundeAddress.value;

        break;

      default:
        newAddress = getEmptyAddress();

        break;
    }

    return { ...newAddress }; // copy
  };

  /* --- reactivity --- */

  // gesellschaft or kunde cleared: reset briefEmpfaengerType if was selected
  watch([
    () => vorgangAnlegenStore.gesellschaftId,
    () => vorgangAnlegenStore.kundeId,
  ], ([newGesellschaftId, newKundeId]) => {
    const isBriefEmpfaengerGesellschaftAndGesellschaftNull = (
      briefEmpfaengerType.value === BriefEmpfaenger.Gesellschaft
      && newGesellschaftId == null
    );

    const isBriefEmpfaengerKundeAndKundeNull = (
      briefEmpfaengerType.value === BriefEmpfaenger.Kunde
      && newKundeId == null
    );

    if (
      isBriefEmpfaengerGesellschaftAndGesellschaftNull
      || isBriefEmpfaengerKundeAndKundeNull
    ) {
      briefEmpfaengerType.value = BriefEmpfaenger.Eigene;
    }
  });

  // briefEmpfaengerType changes to eigene: reset briefEmpfaenger
  const {
    ignoreUpdates: ignoreBriefEmpfaengerTypeUpdates,
  } = ignorableWatch(briefEmpfaengerType, (newBriefEmpfaengerType) => {
    if (newBriefEmpfaengerType === BriefEmpfaenger.Eigene) {
      resetBriefEmpfaenger();
    }
  });

  watch(briefEmpfaenger, () => {
    if (isEqual(briefEmpfaenger.value, getAddressForSelectedEmpfaengerType())) {
      return;
    }

    ignoreBriefEmpfaengerTypeUpdates(() => {
      briefEmpfaengerType.value = BriefEmpfaenger.Eigene;
    });
  }, { deep: true });

  // briefEmpfaengerType, gesellschaft or kunde changes:
  // update kunde/gesellschaft address if changed and set empfaenger
  watch([
    () => vorgangAnlegenStore.gesellschaftId,
    () => vorgangAnlegenStore.kundeId,
    briefEmpfaengerType,
  ], async ([newGesellschaftId, newKundeId], [oldGesellschaftId, oldKundeId]) => {
    if (newGesellschaftId !== oldGesellschaftId) {
      await updateGesellschaftAddress();
    }

    if (newKundeId !== oldKundeId) {
      await updateKundeAddress();
    }

    if (briefEmpfaengerType.value === BriefEmpfaenger.Eigene) {
      return;
    }

    if (briefEmpfaengerType.value === BriefEmpfaenger.Demv && demvAddress.value.name === '') {
      const demvId = (await getDemvGesellschaft()).id;

      const response = await get<PwAddressResource>(
        `/gesellschaften/${demvId}/address`,
      );

      if (response.data?.data !== undefined) {
        demvAddress.value = toBriefAddressElement(
          'DEMV Deutscher Maklerverbund GmbH',
          undefined,
          response.data?.data,
        );
      }
    }

    briefEmpfaenger.value = getAddressForSelectedEmpfaengerType();
  });

  return {
    briefEmpfaenger,
    briefEmpfaengerType,

    resetBriefEmpfaenger,
  };
};
