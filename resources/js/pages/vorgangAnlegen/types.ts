export enum EmailType {
  Outgoing = 'outgoing',
  Incoming = 'incoming',
  Uploaded = 'uploaded',
}

export type FirstTimelineElementType = 'kommentare' | 'korrespondenzen';

// for brief
export enum BriefEmpfaenger {
  Gesellschaft = 'gesellschaft',
  Kunde = 'kunde',
  Demv = 'demv',
  Eigene = 'eigene',
}

export enum BriefAbsender {
  Makler = 'makler',
  Kunde = 'kunde',
}

export enum Anrede {
  Siezen = 'siezen',
  Duzen = 'duzen',
}

export enum LockableField {
  Kunde = 'kunde',
  Vertraege = 'vertraege',
  Gesellschaft = 'gesellschaft',
  Sparte = 'sparte',
}
