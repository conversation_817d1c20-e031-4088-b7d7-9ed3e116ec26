<template>
  <div class="space-y-3">
    <h3 class="text-base font-semibold leading-none">
      Basis-Informationen
    </h3>

    <!-- Versandart, Vorgangstyp & Fällig zum -->
    <div class="flex flex-col gap-x-4 gap-y-3 sm:flex-row">
      <DsFormGroup
        label="Versandart"
        class="shrink-0"
      >
        <DsRadioGroup
          v-model="vorgangAnlegenStore.versandart"
          variant="button"
        >
          <DsRadioButton
            value="email"
            icon="at"
            data-test="step1__vorgangsart__email"
          >
            E-Mail
          </DsRadioButton>
          <DsRadioButton
            value="brief"
            icon="envelope"
            data-test="step1__vorgangsart__brief"
          >
            Brief
          </DsRadioButton>
        </DsRadioGroup>
      </DsFormGroup>

      <Vorgangstyp
        v-model="vorgangAnlegenStore.vorgangstypId"
        :versandart="vorgangAnlegenStore.versandart"
      />
    </div>

    <Vorgangstitel />

    <FaelligAt
      v-model="vorgangAnlegenStore.faelligAt"
      data-test-prefix="basis-info"
    />

    <div class="grid gap-x-4 gap-y-3 md:grid-cols-2">
      <DsFormGroup label="Kunde">
        <KundeSelect
          v-model="vorgangAnlegenStore.kunde"
          :is-loading="vorgangAnlegenStore.isKundeLoading"
          :disabled="vorgangAnlegenStore.lockedFields.has(LockableField.Kunde)"
          kunde-as-value
        />
      </DsFormGroup>

      <Vertraege
        v-model="vorgangAnlegenStore.vertraege"
        :kunde-id="vorgangAnlegenStore.kundeId"
        :gesellschaft-id="vorgangAnlegenStore.gesellschaftId"
        :disabled="vorgangAnlegenStore.lockedFields.has(LockableField.Vertraege)"
      />

      <DsFormGroup label="Gesellschaft">
        <Gesellschaft
          v-model="vorgangAnlegenStore.gesellschaftId"
          :disabled="vorgangAnlegenStore.lockedFields.has(LockableField.Gesellschaft)"
          :only-gesellschaften="true"
          data-test="basis-info__gesellschaft__select"
        />
      </DsFormGroup>

      <Sparte
        v-model="vorgangAnlegenStore.sparteId"
        :disabled="vorgangAnlegenStore.lockedFields.has(LockableField.Sparte)"
        :required="vorgangAnlegenStore.isSparteRequired"
      />
    </div>
    <Vertriebsweg v-if="showVertriebsweg" />
    <FondsFinanzBestandsuebertragungAlert
      v-if="showFFAlert"
    />
  </div>
</template>

<script setup lang="ts">
import { DsFormGroup, DsRadioButton, DsRadioGroup } from '@demvsystems/design-components';
import { computed, ref, watch } from 'vue';

import FaelligAt from '@/components/formBasisInfo/faelligAt/FaelligAt.vue';
import Gesellschaft from '@/components/formBasisInfo/gesellschaft/Gesellschaft.vue';
import KundeSelect from '@/components/formBasisInfo/kunde/KundeSelect.vue';
import Sparte from '@/components/formBasisInfo/sparte/Sparte.vue';
import Vertraege from '@/components/formBasisInfo/vertrag/Vertraege.vue';
import Vertriebsweg from '@/components/formBasisInfo/vertriebsweg/Vertriebsweg.vue';
import Vorgangstyp from '@/components/formBasisInfo/vorgangstyp/Vorgangstyp.vue';
import FondsFinanzBestandsuebertragungAlert
  from '@/pages/vorgangAnlegen/components/korrespondenzen/FondsFinanzBestandsuebertragungAlert.vue';
import Vorgangstitel from '@/pages/vorgangAnlegen/components/korrespondenzen/Vorgangstitel.vue';
import { LockableField } from '@/pages/vorgangAnlegen/types';
import { useVorgangstypenStore } from '@/stores/vorgangstypenStore';

import { useVorgangAnlegenStore } from '../../stores/vorgangAnlegenStore/vorgangAnlegenStore';

const vorgangAnlegenStore = useVorgangAnlegenStore();
const vorgangstypenStore = useVorgangstypenStore();
const showVertriebsweg = ref<boolean>(true);
const bestandsuebertragungTypen = ['10', '35', '84', '85', '90', '106', '118'];

const showFFAlert = computed(() => vorgangAnlegenStore.gesellschaftId == '583'
  && bestandsuebertragungTypen.includes(
    vorgangAnlegenStore.vorgangstypId ?? '-1',
  ));

watch(() => vorgangAnlegenStore.vorgangstypId, () => {
  if (vorgangAnlegenStore.vorgangstypId === null) {
    return;
  }

  const vorgangstyp = vorgangstypenStore.findById(vorgangAnlegenStore.vorgangstypId);
  showVertriebsweg.value = vorgangstyp?.attributes.empfaengerTyp !== 'kunde'
    && vorgangstyp?.attributes.empfaengerTyp !== 'demv';
});
</script>
