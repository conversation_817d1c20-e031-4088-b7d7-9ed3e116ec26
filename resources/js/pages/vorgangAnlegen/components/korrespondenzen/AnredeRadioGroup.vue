<template>
  <DsFormGroup label="Anredeform">
    <DsRadioGroup
      :model-value="modelValue"
      variant="button"
      :disabled="!isVorlageSelected"
      :title="!isVorlageSelected ? 'Bitte wählen Sie zunächst eine Vorlage aus.' : undefined"
      @update:model-value="$emit('update:modelValue', $event)"
    >
      <DsRadioButton
        :value="Anrede.Siezen"
        label="Siezen"
      />
      <DsRadioButton
        :value="Anrede.Duzen"
        label="Duzen"
        :disabled="!hasVorlageInformal"
        :title="!hasVorlageInformal ? 'Für diese Vorlage existiert keine informelle Variante' : undefined"
      />
    </DsRadioGroup>
  </DsFormGroup>
</template>

<script setup lang="ts">
import { DsRadioGroup, DsRadioButton, DsFormGroup } from '@demvsystems/design-components';

import { Anrede } from '../../types';

defineProps<{
  modelValue: Anrede,
  hasVorlageInformal: boolean,
  isVorlageSelected: boolean,
}>();

defineEmits<{
  (event: 'update:modelValue', anrede: Anrede): void,
}>();
</script>
