<template>
  <DsFormGroup label="Vorlage">
    <DsSelect
      :model-value="modelValue ?? undefined"
      :disabled="disabled"
      :title="disabled ? 'Bitte wählen Si<PERSON> zunächst einen Vorgangstyp aus.' : undefined"
      :data="sortedVorlagen"
      group-key="attributes.type"
      value-key="id"
      data-test="step1__vorlagenselect"
      :search-keys="['attributes.type', 'attributes.name']"
      @update:model-value="$emit('update:modelValue', $event)"
    >
      <template #entry="{entry}">
        {{ entry.attributes?.name ?? '' }}
      </template>
    </DsSelect>
  </DsFormGroup>
</template>

<script setup lang="ts">
import { DsFormGroup, DsSelect } from '@demvsystems/design-components';
import { sortBy } from 'lodash-es';
import { computed } from 'vue';

import { VorlageResource } from '@/store/resources/types';

defineEmits<{
  (event: 'update:modelValue', selectedVorlageId: string): void
}>();

const props = defineProps<{
  modelValue: string | null,
  vorlagen: VorlageResource[],
}>();

const sortedVorlagen = computed(() => (
  sortBy(props.vorlagen, (vorlage) => vorlage.attributes.name)
));

const disabled = computed(() => props.vorlagen.length === 0);
</script>
