<template>
  <KorrespondenzDetails
    v-if="vorgang !== undefined"
    :vorgang="vorgang"
    :korrespondenz="korrespondenz"
    :files="vorgangAnlegenStore.files"
    :expected-sender="vorgangAnlegenStore.mailOwnerSenderAddress"
    :suppress-errors-and-retry="true"
  />
</template>

<script setup lang="ts">
import { format } from 'date-fns';

import type {
  BasicVorgangResource, KorrespondenzResource,
} from '@/store/resources/types';

import KorrespondenzDetails from '../../../korrespondenzDetails/korrespondenzDetails.vue';
import { useVorgangAnlegenStore } from '../../stores/vorgangAnlegenStore/vorgangAnlegenStore';

const vorgangAnlegenStore = useVorgangAnlegenStore();

const vorgang = {
  type: 'vorgaenge',
  id: vorgangAnlegenStore.id,
  attributes: {
    faelligAt: vorgangAnlegenStore.faelligAt !== null
      ? format(vorgangAnlegenStore.faelligAt, 'yyyy-MM-dd')
      : null,
    isWichtig: false,
    titel: '',
  },
} as BasicVorgangResource;

const korrespondenz = {
  type: 'korrespondenzen',
  id: vorgangAnlegenStore.firstTimelineElementId,
  attributes: {
    versandart: vorgangAnlegenStore.versandart,
    betreff: vorgangAnlegenStore.renderedBetreff,
    content: vorgangAnlegenStore.renderedContent,
    empfaenger: vorgangAnlegenStore.mailEmpfaenger,
    cc: vorgangAnlegenStore.mailCc,
    bcc: vorgangAnlegenStore.mailBcc,
  },
} as KorrespondenzResource;
</script>
