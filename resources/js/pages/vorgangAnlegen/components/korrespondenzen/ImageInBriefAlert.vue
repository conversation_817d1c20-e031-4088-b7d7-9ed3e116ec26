<template>
  <DsAlert
    v-if="isVersandartBrief && hasImages"
    type="error"
    label="Bilder werden in Briefen nicht unterstützt."
  >
    <p>
      Bitte entfernen Sie alle Bilder aus der Nachricht.
    </p>
  </DsAlert>
</template>

<script setup lang="ts">
import { DsAlert } from '@demvsystems/design-components';
import { computed } from 'vue';

import { useVorgangAnlegenStore } from '@/pages/vorgangAnlegen/stores/vorgangAnlegenStore/vorgangAnlegenStore';
import { Versandart } from '@/pages/vorlagen/types';

const vorgangAnlegenStore = useVorgangAnlegenStore();

const isVersandartBrief = computed(
  () => vorgangAnlegenStore.versandart === Versandart.Brief,
);

const hasImages = computed(
  () => vorgangAnlegenStore.content.includes('<img'),
);
</script>
