<template>
  <DsForm
    class="flex flex-row flex-wrap gap-5"
    :validation-errors="vorgangAnlegenStore.errors"
  >
    <div class="min-w-0 shrink grow space-y-5 lg:w-2/3">
      <TimelineElementRadioGroup />

      <OwnerSelection
        v-model:owner-id="vorgangAnlegenStore.ownerId"
        headline="Vorgang"
        form-group-label="Vorgang anlegen"
        data-test-prefix="vorgang-anlegen"
      />

      <FormBasisInfo />

      <FormVorlage />

      <FormEmpfaengerMail
        v-if="isVersandartMail"
      />
      <FormEmpfaengerBrief v-else />

      <FormNachricht />

      <ErrorAlert />

      <ImageInBriefAlert />

      <ContentUpdateConfirmationModal />
    </div>

    <Sidebar />
  </DsForm>
</template>

<script setup lang="ts">
import { DsForm } from '@demvsystems/design-components';
import { computed } from 'vue';

import OwnerSelection from '@/components/form/OwnerSelection.vue';
import Sidebar from '@/pages/vorgangAnlegen/components/Sidebar/Sidebar.vue';
import TimelineElementRadioGroup
  from '@/pages/vorgangAnlegen/components/korrespondenzen/TimelineElementRadioGroup.vue';
import {
  useVorgangAnlegenStore,
} from '@/pages/vorgangAnlegen/stores/vorgangAnlegenStore/vorgangAnlegenStore';
import { Versandart } from '@/pages/vorlagen/types';

import ErrorAlert from '../ErrorAlert.vue';

import ContentUpdateConfirmationModal from './ContentUpdateConfirmationModal.vue';
import ImageInBriefAlert from './ImageInBriefAlert.vue';
import FormBasisInfo from './formBasisInfo.vue';
import FormEmpfaengerBrief from './formEmpfaengerBrief.vue';
import FormEmpfaengerMail from './formEmpfaengerMail.vue';
import FormNachricht from './formNachricht.vue';
import FormVorlage from './formVorlage.vue';

const vorgangAnlegenStore = useVorgangAnlegenStore();

const isVersandartMail = computed(
  () => vorgangAnlegenStore.versandart === Versandart.Mail,
);
</script>
