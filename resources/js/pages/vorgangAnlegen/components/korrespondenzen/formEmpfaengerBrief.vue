<template>
  <div class="space-y-3">
    <h3 class="text-base font-semibold leading-none">
      Anschrift
    </h3>

    <div
      class="grid gap-x-4 gap-y-3 md:grid-cols-2"
      data-test="step1__address"
    >
      <!-- Em<PERSON><PERSON><PERSON> & Absender -->
      <div class="flex">
        <DsFormGroup label="Empfänger">
          <DsRadioGroup
            v-model="vorgangAnlegenStore.briefEmpfaengerType"
            variant="button"
            data-test="step1__address__empfaenger"
          >
            <DsRadioButton
              value="gesellschaft"
              data-test="step1__address__empfaenger__gesellschaft"
              :disabled="!isGesellschaftSelected"
            >
              Gesellschaft
            </DsRadioButton>
            <DsRadioButton
              value="kunde"
              data-test="step1__address__empfaenger__kunde"
              :disabled="!isKundeSelected"
            >
              Kunde
            </DsRadioButton>
            <DsRadioButton value="demv">
              DEMV
            </DsRadioButton>
            <DsRadioButton value="eigene">
              Eigene
            </DsRadioButton>
          </DsRadioGroup>
        </DsFormGroup>
      </div>

      <div class="flex">
        <DsFormGroup label="Absender">
          <DsRadioGroup
            v-model="vorgangAnlegenStore.briefAbsender"
            variant="button"
          >
            <DsRadioButton value="makler">
              Makler
            </DsRadioButton>
            <DsRadioButton
              value="kunde"
              :disabled="isBriefEmpfaengerTypeKunde || !isKundeSelected"
              data-test="step1__address__absender__kunde"
            >
              Kunde
            </DsRadioButton>
          </DsRadioGroup>
        </DsFormGroup>
      </div>

      <!-- Name & Fax -->
      <FormKundeSalutationType />
      <DsFormGroup
        label="Titel"
        validation-name="attributes.vorgangsart.attributes.empfaenger.titel"
        data-test="step1__address__titel"
      >
        <DsInput
          v-model="vorgangAnlegenStore.briefEmpfaenger.titel"
        />
      </DsFormGroup>

      <DsFormGroup
        class="col-start-1"
        label="Name"
        validation-name="attributes.vorgangsart.attributes.empfaenger.name"
        data-test="step1__address__name"
        required
      >
        <DsInput
          v-model="vorgangAnlegenStore.briefEmpfaenger.name"
        />
      </DsFormGroup>
      <DsFormGroup
        label="Fax"
        validation-name="attributes.vorgangsart.attributes.empfaenger.fax"
        data-test="step1__address__fax"
      >
        <DsInput
          v-model="vorgangAnlegenStore.briefEmpfaenger.fax"
        />
      </DsFormGroup>

      <!-- adresszeile 1 & 2 -->
      <DsFormGroup
        label="Adresszeile 1"
        validation-name="attributes.vorgangsart.attributes.empfaenger.adresszeile1"
        data-test="step1__address__adresszeile1"
        required
      >
        <DsInput
          v-model="vorgangAnlegenStore.briefEmpfaenger.adresszeile1"
          placeholder="Straßenname und Hausnummer"
        />
      </DsFormGroup>
      <DsFormGroup
        label="Adresszeile 2"
        validation-name="attributes.vorgangsart.attributes.empfaenger.adresszeile2"
        data-test="step1__address__addresszeile2"
      >
        <DsInput
          v-model="vorgangAnlegenStore.briefEmpfaenger.adresszeile2"
          placeholder="Unternehmensname, Postfach, usw."
          required
        />
      </DsFormGroup>

      <!-- plz, stadt & land -->
      <div class="grid grid-cols-3 gap-x-4 gap-y-3">
        <DsFormGroup
          label="PLZ"
          validation-name="attributes.vorgangsart.attributes.empfaenger.plz"
          data-test="step1__address__plz"
          required
        >
          <DsInput
            v-model="vorgangAnlegenStore.briefEmpfaenger.plz"
          />
        </DsFormGroup>
        <DsFormGroup
          label="Stadt"
          class="col-span-2"
          validation-name="attributes.vorgangsart.attributes.empfaenger.stadt"
          data-test="step1__address__stadt"
          required
        >
          <DsInput
            v-model="vorgangAnlegenStore.briefEmpfaenger.stadt"
          />
        </DsFormGroup>
      </div>
      <DsFormGroup
        label="Land"
        validation-name="attributes.vorgangsart.attributes.empfaenger.land"
        required
      >
        <DsSelect
          v-model="vorgangAnlegenStore.briefEmpfaenger.land"
          :data="[
            {
              label: 'Deutschland',
              value: 'Deutschland',
            },
          ]"
          data-test="step1__address__land"
          disabled
          required
          title="Momentan wird nur Deutschland als Empfängerland unterstützt."
        />
      </DsFormGroup>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  DsFormGroup,
  DsInput,
  DsRadioButton,
  DsRadioGroup,
  DsSelect,
} from '@demvsystems/design-components';
import { computed } from 'vue';

import { useVorgangAnlegenStore } from '../../stores/vorgangAnlegenStore/vorgangAnlegenStore';
import { BriefEmpfaenger } from '../../types';

import FormKundeSalutationType from './formKundeSalutationType.vue';

const vorgangAnlegenStore = useVorgangAnlegenStore();

const isGesellschaftSelected = computed(
  () => vorgangAnlegenStore.gesellschaftId != undefined,
);

const isKundeSelected = computed(
  () => vorgangAnlegenStore.kundeId != undefined,
);

const isBriefEmpfaengerTypeKunde = computed(() => (
  vorgangAnlegenStore.briefEmpfaengerType === BriefEmpfaenger.Kunde
));
</script>
