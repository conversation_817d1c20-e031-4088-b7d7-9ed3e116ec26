<template>
  <DsFormGroup
    label="Titel"
    data-test="basis-info__vorgangstitel"
    required
  >
    <DsInput
      v-model="titleProxy"
      required
    />
  </DsFormGroup>
</template>

<script setup lang="ts">
import { DsFormGroup, DsInput } from '@demvsystems/design-components';
import { computed } from 'vue';

import {
  useVorgangAnlegenStore,
} from '@/pages/vorgangAnlegen/stores/vorgangAnlegenStore/vorgangAnlegenStore';

const vorgangAnlegenStore = useVorgangAnlegenStore();

const titleProxy = computed({
  get: () => vorgangAnlegenStore.vorgangstitel,
  set: (newTitle: string) => {
    vorgangAnlegenStore.vorgangstitel = newTitle;
    vorgangAnlegenStore.contentOrBetreffWasEdited = true;
  },
});
</script>
