<template>
  <div class="space-y-3">
    <h3 class="text-base font-semibold leading-none">
      Vorlage
    </h3>

    <div class="grid gap-x-4 gap-y-3 md:grid-cols-2">
      <VorlageSelect
        v-model="vorgangAnlegenStore.selectedVorlageId"
        :vorlagen="vorgangAnlegenStore.vorlagen"
        class="grow"
      />

      <div class="flex items-end space-x-4">
        <AnredeRadioGroup
          v-model="vorgangAnlegenStore.anrede"
          :is-vorlage-selected="vorgangAnlegenStore.selectedVorlage !== undefined"
          :has-vorlage-informal="vorgangAnlegenStore.hasVorlageInformal"
          class="grow"
        />
        <DsButton
          href="/vorlagen"
          class="md:hidden"
          variant="secondary"
          external
        >
          Vorlagen verwalten
        </DsButton>
      </div>
      <DsButton
        class="hidden md:flex"
        href="/vorlagen"
        variant="secondary"
        external
      >
        Vorlagen verwalten
      </DsButton>
    </div>
  </div>
</template>

<script setup lang="ts">
import { DsButton } from '@demvsystems/design-components';

import { useVorgangAnlegenStore } from '../../stores/vorgangAnlegenStore/vorgangAnlegenStore';

import AnredeRadioGroup from './AnredeRadioGroup.vue';
import VorlageSelect from './VorlageSelect.vue';

const vorgangAnlegenStore = useVorgangAnlegenStore();
</script>
