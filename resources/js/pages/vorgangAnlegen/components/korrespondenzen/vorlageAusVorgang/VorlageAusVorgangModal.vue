<template>
  <DsButton
    size="sm"
    variant="secondary"
    icon="save"
    button-icon-align="right"
    :disabled="disabled"
    data-test="vorgang-anlegen__save-as-vorlage"
    @click="open"
  >
    Als Vorlage speichern
  </DsButton>

  <DsModal
    :show="show"
    size="sm"
    anchor="top"
    hide-buttons
    @close="close"
  >
    <VorlageAusVorgangForm @close="close" />
  </DsModal>
</template>

<script setup lang="ts">
import {
  DsButton,
  DsModal,
} from '@demvsystems/design-components';
import { ref } from 'vue';

import VorlageAusVorgangForm from './VorlageAusVorgangForm.vue';

const show = ref(false);

const open = () => {
  show.value = true;
};

const close = () => {
  show.value = false;
};

defineProps<{
  disabled?: boolean,
}>();
</script>
