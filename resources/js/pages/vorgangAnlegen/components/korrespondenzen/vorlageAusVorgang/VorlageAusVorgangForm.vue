<template>
  <div class="text-base">
    <h2 class="mb-5 text-lg font-bold leading-none tracking-wide">
      Als Vorlage speichern
    </h2>

    <DsAlert
      v-if="missingFields.length > 0"
      type="warning"
      icon="file-times"
      :label="`${missingFields.length} Pflichtfeld${missingFields.length > 1 ? 'er sind' : ' ist'}  nicht ausgefüllt`"
    >
      <p class="my-2">
        Bitte füllen Si<PERSON> zu<PERSON>t die folgenden Pflichtfelder im
        Vorgang-Anlegen-Formular aus:
      </p>

      <ul>
        <li
          v-for="field in missingFields"
          :key="field"
          class="list-inside list-disc font-semibold capitalize"
          v-text="field"
        />
      </ul>
    </DsAlert>

    <DsForm
      v-else
      class="space-y-7"
    >
      <DsAlert
        v-if="contentContainsImages"
        type="warning"
        label="Der Inhalt enthält Bilder"
      >
        Bilder werden beim Speichern der Vorlage nicht berücksichtigt.
      </DsAlert>

      <DsRadioGroup
        v-model="saveType"
        variant="card"
        class="grid-cols-2"
      >
        <DsRadioButton
          value="create"
          icon="plus"
          label="Neu"
        >
          Neue Vorlage erstellen
        </DsRadioButton>

        <DsRadioButton
          :disabled="vorgangAnlegenStore.selectedVorlage === undefined"
          :title="vorgangAnlegenStore.selectedVorlage === undefined ? 'Keine Vorlage ausgewählt' : undefined"
          value="update"
          icon="pen"
          label="Aktualisieren"
        >
          Ausgewählte Vorlage aktualisieren
        </DsRadioButton>
      </DsRadioGroup>

      <div class="flex space-x-4">
        <DsButton
          variant="danger"
          size="sm"
          icon="eraser"
          @click="vorlageAnlegenStore.resetForm"
        >
          Zurücksetzen
        </DsButton>
        <DsButton
          variant="secondary"
          size="sm"
          icon="pen"
          :disabled="vorgangAnlegenStore.selectedVorlage === undefined"
          :title="
            vorgangAnlegenStore.selectedVorlage === undefined
              ? 'Keine Vorlage ausgewählt'
              : 'Das Formular mit den Daten aus der ausgewählten Vorlage befüllen'
          "
          @click="initFromVorlage"
        >
          Aus ausgewählter Vorlage befüllen
        </DsButton>
      </div>

      <DsFormGroup
        label="Vorlagenname"
        validation-name="attributes.name"
        required
      >
        <DsInput
          v-model="vorlageAnlegenStore.name"
          data-test="vorlage-aus-vorgang__vorlagenname"
          required
        />
      </DsFormGroup>

      <FormSection
        v-if="vorgangAnlegenStore.versandart === Versandart.Mail"
        title="Empfänger"
      >
        <div class="flex flex-col md:flex-row">
          <DsFormGroup
            label="An"
            class="grow"
            required
          >
            <DsMultiselect
              v-model="mailEmpfaengersProxy"
              :options="empfaengerOptions"
              data-test="vorlage-aus-vorgang__empfaenger-an"
              required
              object-as-value
            />
          </dsformgroup>
        </div>

        <DsFormGroup label="CC">
          <DsMultiselect
            v-model="mailCcProxy"
            :options="empfaengerOptions"
            data-test="vorlage-aus-vorgang__empfaenger-cc"
            object-as-value
          />
        </DsFormGroup>

        <DsFormGroup label="BCC">
          <DsMultiselect
            v-model="mailBccProxy"
            :options="empfaengerOptions"
            data-test="vorlage-aus-vorgang__empfaenger-bcc"
            object-as-value
          />
        </DsFormGroup>
      </FormSection>

      <EmpfaengerAndAbsender
        v-else
        v-model:empfaenger="vorlageAnlegenStore.briefEmpfaenger"
        v-model:absender="vorlageAnlegenStore.absender"
      />
    </DsForm>

    <div class="mt-5 flex justify-end space-x-4">
      <DsButton
        variant="secondary"
        @click="$emit('close')"
      >
        Abbrechen
      </DsButton>
      <DsButton
        :disabled="missingFields.length > 0"
        data-test="vorlage-aus-vorgang__submit"
        :handler="submit"
      >
        Vorlage {{ saveType === 'create' ? 'erstellen' : 'aktualisieren' }}
      </DsButton>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  DsAlert,
  DsButton,
  DsForm,
  DsFormGroup,
  DsInput,
  DsMultiselect,
  DsRadioButton,
  DsRadioGroup,
  MultiselectItem,
} from '@demvsystems/design-components';
import { storeToRefs } from 'pinia';
import { computed, ref, watch } from 'vue';

import FormSection from '@/components/form/FormSection.vue';
import {
  getLabelForEmpfaengerType,
  makeVorlageMailEmpfaengerMultiselectProxy,
} from '@/components/form/utils/mail';
import EmpfaengerAndAbsender
  from '@/pages/vorlagen/components/briefVorlage/EmpfaengerAndAbsender.vue';
import { useVorlageAnlegenStore } from '@/pages/vorlagen/stores/vorlageAnlegenStore';
import { Versandart, Vorlagenart } from '@/pages/vorlagen/types';
import { eventBus } from '@/store/resources/store';
import { VorlageMailEmpfaengerType } from '@/store/resources/types';

import { useVorgangAnlegenStore } from '../../../stores/vorgangAnlegenStore/vorgangAnlegenStore';
import { BriefAbsender, BriefEmpfaenger } from '../../../types';

import { CreateOrUpdate } from './types';

const emit = defineEmits<{
  (event: 'close'): void
}>();

const vorgangAnlegenStore = useVorgangAnlegenStore();
const vorlageAnlegenStore = useVorlageAnlegenStore();

const missingFields = computed(() => {
  const fields = [];

  if (vorgangAnlegenStore.vorgangstypId == null) {
    fields.push('Vorgangstyp');
  }

  if (vorgangAnlegenStore.versandart === Versandart.Mail && (
    vorgangAnlegenStore.betreff === null
  )) {
    fields.push('Betreff');
  }

  if (vorgangAnlegenStore.content === '') {
    fields.push('Inhalt');
  }

  return fields;
});

const contentWithoutImages = computed(() => {
  return vorgangAnlegenStore.content.replace(/<img[^>]*>?/g, '');
});

const contentContainsImages = computed(() => {
  return vorgangAnlegenStore.content.includes('<img');
});

const empfaengerOptions = Object.values(VorlageMailEmpfaengerType)
  .map((value): MultiselectItem => ({
    value,
    label: getLabelForEmpfaengerType(value),
  }));

const { mailEmpfaenger, cc, bcc } = storeToRefs(vorlageAnlegenStore);

const mailEmpfaengersProxy = makeVorlageMailEmpfaengerMultiselectProxy(
  mailEmpfaenger,
);

const mailCcProxy = makeVorlageMailEmpfaengerMultiselectProxy(
  cc,
);
const mailBccProxy = makeVorlageMailEmpfaengerMultiselectProxy(
  bcc,
);

const initFromVorlage = () => {
  vorlageAnlegenStore.name = vorgangAnlegenStore.selectedVorlage?.attributes.name ?? '';

  if (vorgangAnlegenStore.selectedVorlageMail !== undefined) {
    vorlageAnlegenStore.mailEmpfaenger = (
      vorgangAnlegenStore.selectedVorlageMail?.attributes.empfaengerTypes ?? []
    );
    vorlageAnlegenStore.cc = vorgangAnlegenStore.selectedVorlageMail?.attributes.cc ?? [];
    vorlageAnlegenStore.bcc = vorgangAnlegenStore.selectedVorlageMail?.attributes.bcc ?? [];

    return;
  }

  vorlageAnlegenStore.briefEmpfaenger = (
    vorgangAnlegenStore.selectedVorlageBrief?.attributes.empfaengerType
    ?? BriefEmpfaenger.Eigene
  );
  vorlageAnlegenStore.absender = (
    vorgangAnlegenStore.selectedVorlageBrief?.attributes.senderTyp ?? BriefAbsender.Makler
  );
};

const initFromVorgang = () => {
  vorlageAnlegenStore.vorlagenart = Vorlagenart.Vorgang;
  vorlageAnlegenStore.versandart = vorgangAnlegenStore.versandart;
  vorlageAnlegenStore.vorgangTypId = vorgangAnlegenStore.vorgangstypId ?? '';
  vorlageAnlegenStore.formalSubject = vorgangAnlegenStore.betreff;
  vorlageAnlegenStore.formalContent = contentWithoutImages.value;

  if (vorgangAnlegenStore.selectedVorlage != undefined) {
    initFromVorlage();
  }
};

const saveType = ref<CreateOrUpdate>('create');

watch(
  () => vorgangAnlegenStore.vorgangstypId,
  initFromVorgang,
  { immediate: true },
);

watch(
  () => vorgangAnlegenStore.selectedVorlage,
  initFromVorlage,
  { immediate: true },
);

const submit = async ()  => {
  if (saveType.value === 'update') {
    vorlageAnlegenStore.id = vorgangAnlegenStore.selectedVorlageId ?? '';
  } else {
    vorlageAnlegenStore.id = '';
  }

  const vorlage = await vorlageAnlegenStore.save();
  if (vorlage?.data == undefined) {
    return;
  }

  const event = saveType.value === 'create'
    ? 'vorlageErstellt'
    : 'vorlageUpdated';
  eventBus.emit(event);

  await vorgangAnlegenStore.updateVorlagen();

  if (saveType.value === 'create') {
    vorgangAnlegenStore.selectedVorlageId = vorlage.data.id;
  }

  emit('close');
};
</script>
