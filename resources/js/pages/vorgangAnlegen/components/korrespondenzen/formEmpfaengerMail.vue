<template>
  <EmpfaengerFormSection
    v-model:has-cc-and-bcc="vorgangAnlegenStore.mailHasCcAndBcc"
    :validation-names="{
      empfaenger: 'attributes.vorgangsart.attributes.empfaenger',
      cc: 'attributes.vorgangsart.attributes.cc',
      bcc: 'attributes.vorgangsart.attributes.bcc',
    }"
  >
    <template #empfaenger>
      <EmpfaengerMultiselect
        v-model="empfaengerMailProxy"
        :suggestion-context="empfaengerSuggestionContext"
        data-test="step1__empfaenger__form"
      />
    </template>
    <template #cc>
      <EmpfaengerMultiselect
        v-model="ccProxy"
        :suggestion-context="empfaengerSuggestionContext"
        data-test="step1__cc__form"
      />
    </template>
    <template #bcc>
      <EmpfaengerMultiselect
        v-model="bccProxy"
        :suggestion-context="empfaengerSuggestionContext"
        data-test="step1__bcc__form"
      />
    </template>
  </EmpfaengerFormSection>
</template>

<script setup lang="ts">
import { MultiselectItem } from '@demvsystems/design-components';
import { computed } from 'vue';

import EmpfaengerFormSection from '@/components/form/EmpfaengerFormSection.vue';
import EmpfaengerMultiselect from '@/components/form/EmpfaengerMultiselect.vue';
import { EmpfaengerSuggestionContext } from '@/components/form/useEmpfaengerSuggestions';
import {
  emailElementToMultiselectItem,
  multiselectItemToEmailElement,
} from '@/components/form/utils/mail';

import { useVorgangAnlegenStore } from '../../stores/vorgangAnlegenStore/vorgangAnlegenStore';

const vorgangAnlegenStore = useVorgangAnlegenStore();

const empfaengerMailProxy = computed<MultiselectItem[]>({
  get: () => emailElementToMultiselectItem(vorgangAnlegenStore.mailEmpfaenger),
  set: (items: MultiselectItem[]) => {
    vorgangAnlegenStore.mailEmpfaenger = multiselectItemToEmailElement(items);
  },
});

const ccProxy = computed<MultiselectItem[]>({
  get: () => emailElementToMultiselectItem(vorgangAnlegenStore.mailCc),
  set: (items: MultiselectItem[]) => {
    vorgangAnlegenStore.mailCc = multiselectItemToEmailElement(items);
  },
});

const bccProxy = computed<MultiselectItem[]>({
  get: () => emailElementToMultiselectItem(vorgangAnlegenStore.mailBcc),
  set: (items: MultiselectItem[]) => {
    vorgangAnlegenStore.mailBcc = multiselectItemToEmailElement(items);
  },
});

const empfaengerSuggestionContext = computed<EmpfaengerSuggestionContext>(() => {
  return {
    kundeId: vorgangAnlegenStore.kundeId,
    gesellschaftId: vorgangAnlegenStore.gesellschaftId,
    sparteId: vorgangAnlegenStore.sparteId,
    vorgangstypId: vorgangAnlegenStore.vorgangstypId,
    vorgangId: vorgangAnlegenStore.vorgaengerId,
    vertriebswegId: vorgangAnlegenStore.vertriebswegIdProxy,
  };
});
</script>
