<template>
  <div>
    <UserSelectMenu
      :preselected-users="preselectedUsers"
      :user-type="userType"
    >
      <template #button>
        <SidebarCta
          title="Bearbeiter/Beobachter hinzufügen oder entfernen"
          :label="userType"
          action-icon="cog"
        />
      </template>
    </UserSelectMenu>
  </div>
</template>

<script setup lang="ts">
import SidebarCta from '@/pages/vorgang/components/SidebarCta.vue';
import UserSelectMenu from '@/pages/vorgangAnlegen/components/Sidebar/UserSelectMenu.vue';
import { UserResource } from '@/store/resources/types';

withDefaults(defineProps<{
  preselectedUsers: UserResource[],
  userType: 'bearbeiter' | 'beobachter',
}>(), {
  preselectedUsers: () => ([]),
});
</script>
