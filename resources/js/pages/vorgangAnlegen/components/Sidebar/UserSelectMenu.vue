<template>
  <SelectMenu
    :selected-items="preselectedUsers"
    :items="visibleFirmenMembers"
    :fuse-options="fuseOptions"
    :loading="isLoading"
    :disabled="disabled"
    @open="loadVisibleFirmenMembers"
    @select="handleSelect"
    @deselect="handleDeselect"
  >
    <template #button>
      <slot
        type="button"
        name="button"
      />
    </template>

    <template #item="{item}">
      <UserTag
        v-if="!isLoading"
        :user="item"
      />
    </template>

    <template #selected-items="{items}">
      <UserList
        v-if="items && items.length > 0"
        :users="items as UserResource[]"
      />
    </template>
  </SelectMenu>
</template>

<script setup lang="ts">
import { onBeforeMount } from 'vue';

import SelectMenu from '@/components/SelectMenu/SelectMenu.vue';
import UserTag from '@/components/tags/UserTag.vue';
import UserList from '@/components/users/UserList.vue';
import useHierarchy from '@/components/users/useHierarchy';
import { useVorgangAnlegenStore } from '@/pages/vorgangAnlegen/stores/vorgangAnlegenStore/vorgangAnlegenStore';
import { UserResource } from '@/store/resources/types';

const props = withDefaults(defineProps<{
  preselectedUsers: UserResource[],
  userType: 'bearbeiter' | 'beobachter',
}>(), {
  preselectedUsers: () => ([]),
});
defineEmits<{
  (event: 'selectedItems'): void
}>();

const vorgangAnlegenStore = useVorgangAnlegenStore();
const disabled = false;
const {
  isLoading,
  visibleFirmenMembers,
  loadVisibleFirmenMembers,
} = useHierarchy();

onBeforeMount(async () => {
  await loadVisibleFirmenMembers();
});

const handleSelect = (event: UserResource) => {
  if (props.userType === 'bearbeiter') {
    vorgangAnlegenStore.bearbeiterIds.push(event.id);
  } else {
    vorgangAnlegenStore.beobachterIds.push(event.id);
  }
};

const handleDeselect = (event: UserResource) => {
  if (props.userType === 'bearbeiter') {
    vorgangAnlegenStore.bearbeiterIds = vorgangAnlegenStore
      .bearbeiterIds
      .filter(
        (elem) => elem !== event.id,
      );
  } else {
    vorgangAnlegenStore.beobachterIds = vorgangAnlegenStore
      .beobachterIds
      .filter(
        (elem) => elem !== event.id,
      );
  }
};

const fuseOptions = {
  keys: ['data.attributes.name'],
  threshold: 0.4,
};
</script>
