<template>
  <div
    class="flex grow flex-row flex-wrap rounded-md bg-gray-100 p-3
    lg:w-1/4 lg:flex-col lg:justify-start"
  >
    <div class="top-0 flex flex-row gap-3 lg:sticky lg:flex-col">
      <SidebarUsers
        user-type="bearbeiter"
        class="flex-1 rounded bg-white px-5 py-3
            md:max-w-xs md:shrink md:grow
            xl:w-auto xl:max-w-full xl:grow-0"
        :preselected-users="vorgangAnlegenStore.bearbeiter"
      />
      <SidebarUsers
        user-type="beobachter"
        class="flex-1 rounded bg-white px-5 py-3
            md:max-w-xs md:shrink md:grow
            xl:w-auto xl:max-w-full xl:grow-0"
        :preselected-users="vorgangAnlegenStore.beobachter"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import SidebarUsers from '@/pages/vorgangAnlegen/components/Sidebar/SidebarUsers.vue';
import { useVorgangAnlegenStore } from '@/pages/vorgangAnlegen/stores/vorgangAnlegenStore/vorgangAnlegenStore';

const vorgangAnlegenStore = useVorgangAnlegenStore();
</script>
