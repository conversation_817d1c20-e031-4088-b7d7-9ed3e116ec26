<template>
  <DsAlert
    v-if="errors.length > 0"
    type="error"
    label="Der Vorgang kann leider so nicht angelegt werden:"
  >
    <ul class="list-inside list-disc">
      <li
        v-for="error in errors"
        :key="error"
        v-text="error"
      />
    </ul>
  </DsAlert>
</template>

<script setup lang="ts">
import { DsAlert } from '@demvsystems/design-components';
import { flatten } from 'lodash-es';
import { computed } from 'vue';

import { useVorgangAnlegenStore } from '../stores/vorgangAnlegenStore/vorgangAnlegenStore';

const vorgangAnlegenStore = useVorgangAnlegenStore();

const errors = computed<string[]>(
  () => flatten(Object.values(vorgangAnlegenStore.errors)),
);
</script>
