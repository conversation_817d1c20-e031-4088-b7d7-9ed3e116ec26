<template>
  <FormSection title="Basis-Informationen">
    <div class="flex flex-col space-y-3 md:flex-row md:space-x-4 md:space-y-0">
      <DsFormGroup
        class="grow"
        label="Titel"
        required
        validation-name="attributes.titel"
      >
        <DsInput
          v-model="vorgangAnlegenStore.vorgangstitel"
          data-test="aufgabe__form-basis-info__input__titel"
          class="titel"
          required
        />
      </DsFormGroup>
    </div>

    <FaelligAt
      v-model="vorgangAnlegenStore.faelligAt"
      data-test-prefix="basis-info"
    />

    <div class="grid gap-x-4 gap-y-3 md:grid-cols-2">
      <DsFormGroup label="Kunde">
        <KundeSelect
          v-model="vorgangAnlegenStore.kunde"
          :is-loading="vorgangAnlegenStore.isKundeLoading"
          :disabled="vorgangAnlegenStore.lockedFields.has(LockableField.Kunde)"
          kunde-as-value
        />
      </DsFormGroup>

      <Vertraege
        v-model="vorgangAnlegenStore.vertraege"
        :kunde-id="vorgangAnlegenStore.kundeId"
        :gesellschaft-id="vorgangAnlegenStore.gesellschaftId"
        :disabled="vorgangAnlegenStore.lockedFields.has(LockableField.Vertraege)"
      />

      <DsFormGroup label="Gesellschaft">
        <Gesellschaft
          v-model="vorgangAnlegenStore.gesellschaftId"
          :disabled="vorgangAnlegenStore.lockedFields.has(LockableField.Gesellschaft)"
          data-test="basis-info__gesellschaft__select"
        />
      </DsFormGroup>

      <Sparte
        v-model="vorgangAnlegenStore.sparteId"
        :disabled="vorgangAnlegenStore.lockedFields.has(LockableField.Sparte)"
      />
    </div>
  </FormSection>
</template>

<script setup lang="ts">
import { DsFormGroup, DsInput } from '@demvsystems/design-components';

import FormSection from '@/components/form/FormSection.vue';
import FaelligAt from '@/components/formBasisInfo/faelligAt/FaelligAt.vue';
import Gesellschaft from '@/components/formBasisInfo/gesellschaft/Gesellschaft.vue';
import KundeSelect from '@/components/formBasisInfo/kunde/KundeSelect.vue';
import Sparte from '@/components/formBasisInfo/sparte/Sparte.vue';
import Vertraege from '@/components/formBasisInfo/vertrag/Vertraege.vue';
import { LockableField } from '@/pages/vorgangAnlegen/types';

import { useVorgangAnlegenStore } from '../../stores/vorgangAnlegenStore/vorgangAnlegenStore';

const vorgangAnlegenStore = useVorgangAnlegenStore();
</script>
