<template>
  <FormSection title="Beschreibung">
    <NachrichtContent
      v-model="vorgangAnlegenStore.content"
      :image-upload-handler="imageUploadHandler"
      required
      validation-name="attributes.vorgangsart.attributes.content"
      data-test="aufgabe__nachricht__content"
    />

    <FormFiles
      v-model:uploaded-files="vorgangAnlegenStore.uploadedFiles"
      v-model:existing-documents="vorgangAnlegenStore.externalFiles"
      :kunde-id="vorgangAnlegenStore.kunde?.id"
      :gesellschaft-id="vorgangAnlegenStore.gesellschaftId ?? undefined"
      :vorgang-id="vorgangAnlegenStore.vorgaengerId ?? undefined"
      :vertraege-ids="vertraegeIds"
      :kundendokument-ids="vorgangAnlegenStore.kundendokumentIds"
      :remove-file-from-existing-documents="vorgangAnlegenStore.removeFileFromExistingDocuments"
    />
  </FormSection>
</template>

<script setup lang="ts">
import { storeToRefs } from 'pinia';
import { computed } from 'vue';

import FormFiles from '@/components/form/FormFiles.vue';
import FormSection from '@/components/form/FormSection.vue';
import NachrichtContent from '@/components/form/NachrichtContent.vue';
import { useImageUploadHandler } from '@/composables/useImageUploadHandler';
import { VertragResource } from '@/store/resources/types';

import { useVorgangAnlegenStore } from '../../stores/vorgangAnlegenStore/vorgangAnlegenStore';

const vorgangAnlegenStore = useVorgangAnlegenStore();
const {
  firstTimelineElementId,
  firstTimelineElementType,
} = storeToRefs(vorgangAnlegenStore);
const { imageUploadHandler } = useImageUploadHandler(
  firstTimelineElementId,
  firstTimelineElementType,
);

const  vertraegeIds = computed(
  () => vorgangAnlegenStore.vertraege.map(
    ({ id }: VertragResource) => id,
  ),
);
</script>
