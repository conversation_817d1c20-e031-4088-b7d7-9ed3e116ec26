<template>
  <DsForm
    class="flex flex-row flex-wrap gap-5"
    :validation-errors="vorgangAnlegenStore.errors"
  >
    <div class="min-w-0 shrink grow space-y-5 lg:w-2/3">
      <TimelineElementRadioGroup />

      <FormBasisInfo />

      <FormBeschreibung />

      <ErrorAlert />
    </div>

    <Sidebar />
  </DsForm>
</template>

<script setup lang="ts">
import { DsForm } from '@demvsystems/design-components';

import Sidebar from '@/pages/vorgangAnlegen/components/Sidebar/Sidebar.vue';
import TimelineElementRadioGroup from '@/pages/vorgangAnlegen/components/korrespondenzen/TimelineElementRadioGroup.vue';

import { useVorgangAnlegenStore } from '../../stores/vorgangAnlegenStore/vorgangAnlegenStore';
import ErrorAlert from '../ErrorAlert.vue';

import FormBasisInfo from './formBasisInfo.vue';
import FormBeschreibung from './formBeschreibung.vue';

const vorgangAnlegenStore = useVorgangAnlegenStore();
</script>
