import { post, put } from '@/api';
import { injectStore } from '@/store/resources/composition';
import { VorgangResource } from '@/store/resources/types';

export function useVorgangNotification(): {
  createNotification: (vorgangId: string) => Promise<void>;
  deleteNotifications: (vorgangId: string) => Promise<void>;
} {
  const store = injectStore();

  async function createNotification(vorgangId: string) {
    const response = await post<VorgangResource>(`vorgaenge/${vorgangId}/notifications`, {
      data: {
        type: 'notifications',
        attributes: {
          type: 'marked_as_unread',
        },
      },
    });

    store.update(response.data);
  }

  async function deleteNotifications(vorgangId: string) {
    const response = await put<VorgangResource>(`vorgaenge/${vorgangId}/notifications`, {
      data: [],
    });

    store.update(response.data);
  }

  return {
    createNotification,
    deleteNotifications,
  };
}
