<template>
  <div class="p-3 py-3.5 text-center">
    <div
      class="mb-3"
      data-test="vorgangsliste__empty-message"
    >
      <template v-if="query.length > 0 && hasFilters">
        <PERSON>ür die <PERSON>e „{{ query }}“ und die gesetzten Filter wurde kein Vorgang gefunden.
      </template>
      <template v-else-if="query.length > 0 && !hasFilters">
        Für die Suche „{{ query }}“ wurde kein Vorgang gefunden.
      </template>
      <template v-else-if="hasFilters">
        Für die gesetzten Filter wurde kein Vorgang gefunden.
      </template>
      <template v-else>
        Es existiert noch kein Vorgang.
      </template>
    </div>

    <div class="flex grow items-center justify-center pr-2 2xl:pr-0">
      <DsButton
        icon="plus"
        @click="() => open()"
      >
        Vorgang anlegen
      </DsButton>
    </div>
  </div>
</template>

<script setup lang="ts">
import { DsButton } from '@demvsystems/design-components';

import useVorgangAnlegenModal from '../../vorgangAnlegen/composables/useVorgangAnlegenModal';

defineProps<{
  query: string,
  hasFilters: boolean,
}>();

const {
  open,
} = useVorgangAnlegenModal();
</script>
