<template>
  <div
    class="group relative"
  >
    <div
      v-if="!spaced"
      class="absolute inset-y-0 left-0 w-0 cursor-pointer transition-[width] group-hover:w-1.5 group-hover:hover:w-2"
      :class="{
        'w-1.5': notificationSeverity !== null,
        'bg-blue-500': notificationSeverity === NotificationSeverity.Info,
        'bg-red-500': notificationSeverity === NotificationSeverity.Error,
        'bg-gray-300': notificationSeverity === null,
      }"
      @click.prevent="markVorgang"
    />

    <router-link
      :to="{
        name: 'vorgaenge.show',
        params: {
          firmaId,
          vorgangsnummer: vorgang.attributes.vorgangsnummer,
        },
      }"
      class="flex h-24 space-x-3 overflow-hidden p-3 hover:bg-gray-100"
      active-class="bg-gray-100 cursor-default"
      :class="{
        'rounded-md border bg-white': spaced,
      }"
      data-test="vorgangsliste__item"
      @click="manuallySetNotification = null"
    >
      <aside
        v-if="!spaced"
        class="inline-flex shrink-0 flex-col items-center"
      >
        <DsCheckbox
          :model-value="selected === undefined ? false : selected"
          class="h-6"
          @click.prevent
          @update:model-value="emit('update:selected', $event)"
        />
      </aside>

      <section class="grow truncate">
        <!-- first row -->
        <div class="flex space-x-2">
          <p
            class="grow truncate font-semibold"
            data-test="vorgangsliste__item__titel"
            :class="{
              'text-blue-700': notificationSeverity === NotificationSeverity.Info,
              'text-red-700': notificationSeverity === NotificationSeverity.Error,
              'text-gray-800': notificationSeverity === null,
            }"
            :title="titelTooltip"
            v-text="titel"
          />
          <VorgangStatus
            class="shrink-0"
            :status="vorgang.attributes.status"
          />
        </div>

        <!-- second row -->
        <div class="flex space-x-1">
          <div class="flex grow space-x-2 truncate">
            <TimeAgoTag
              v-if="vorgang.attributes.faelligAt"
              title="Fällig zum"
              :value="vorgang.attributes.faelligAt"
              :no-urgency-styling="vorgang.attributes.status === VorgangStatusType.Erledigt"
              data-test="vorgangsliste__item__faellig-at"
              class="min-w-0 flex-1 truncate"
            />
            <div
              v-else
              class="min-w-0 flex-1 truncate"
            />
            <Tag
              icon="calendar-day"
              title="Erstellungsdatum"
              data-test="vorgangsliste__item__createdAt"
              class="min-w-0 flex-1 truncate"
            >
              <TimeAgoText
                :value="vorgang.attributes.createdAt"
                only-date
              />
            </Tag>
          </div>
        </div>

        <!-- third row -->
        <div class="flex space-x-1">
          <div class="flex grow space-x-2 truncate">
            <div
              v-if="vorgang.attributes.isWichtig"
              class="w-5 shrink-0"
            >
              <DsIcon
                name="flag"
                class="w-5 text-red-500"
              />
            </div>
            <KundeTag
              v-if="kunde !== undefined"
              :kunde="kunde.attributes.name"
              class="min-w-0 flex-1 truncate"
              data-test="vorgangsliste__item__kunde"
            />
            <GesellschaftTag
              v-if="gesellschaft !== undefined"
              :gesellschaft="gesellschaft.attributes.name"
              class="min-w-0 flex-1 truncate"
              data-test="vorgangsliste__item__gesellschaft"
            />
          </div>

          <DsBadge
            v-if="vorgang.attributes.isGroup"
            :title="`${vorgang.attributes.untervorgaengeErledigtCount} von ${vorgang.attributes.untervorgaengeCount} Untervorgängen erledigt`"
          >
            {{ vorgang.attributes.untervorgaengeErledigtCount }}
            /
            {{ vorgang.attributes.untervorgaengeCount }}
          </DsBadge>
        </div>
      </section>
    </router-link>
  </div>
</template>

<script setup lang="ts">
import { DsBadge, DsCheckbox, DsIcon } from '@demvsystems/design-components';
import { computed, ref } from 'vue';

import VorgangStatus from '@/components/VorgangStatus.vue';
import { sparten } from '@/components/formBasisInfo/sparte/spartenList';
import GesellschaftTag from '@/components/tags/GesellschaftTag.vue';
import KundeTag from '@/components/tags/KundeTag.vue';
import Tag from '@/components/tags/Tag.vue';
import TimeAgoTag from '@/components/tags/TimeAgoTag.vue';
import TimeAgoText from '@/components/tags/TimeAgoText.vue';
import useCurrentUser from '@/components/users/useCurrentUser';
import { useVorgangNotification } from '@/pages/vorgaenge/composables/useVorgangNotifications';
import { injectStore } from '@/store/resources/composition';
import type { VorgangResource } from '@/store/resources/types';
import { NotificationSeverity, sortNotificationSeverityByPriority } from '@/store/resources/types';
import { VorgangStatus as VorgangStatusType } from '@/types';

interface Props {
  vorgang: VorgangResource
  selected?: boolean
  spaced?: boolean
}

const props = defineProps<Props>();
const emit = defineEmits<{
  (event: 'update:selected', selected: boolean): void
}>();

const store = injectStore();
const { user } = useCurrentUser();

const firmaId = computed(() => user.value?.attributes.firmaId);
const kunde = computed(() => store.kunden.findRelated(
  props.vorgang.relationships?.kunde,
));
const sparte = computed(() => sparten.value.find(
  (e) => e.id === props.vorgang.relationships?.sparte?.data?.id,
));

const spartenTitel = computed(
  () => sparte.value?.attributes.abkuerzung != null
    ? sparte.value?.attributes.abkuerzung
    : sparte.value?.attributes.displayName,
);

const titel = computed(
  () => (
    props.vorgang.attributes.isGroup
    && sparte.value !== undefined
  ) ? `${props.vorgang.attributes.titel} (${spartenTitel.value})`
    : props.vorgang.attributes.titel,
);

const titelTooltip = computed(
  () => (
    sparte.value !== undefined
  ) ? `${props.vorgang.attributes.titel} (${sparte.value?.attributes.displayName})`
    : props.vorgang.attributes.titel,
);

const gesellschaft = computed(() => store.gesellschaften.findRelated(
  props.vorgang.relationships?.gesellschaft,
));

const manuallySetNotification = ref<boolean | null>(null);

const notificationSeverity = computed(() => {
  if (manuallySetNotification.value) {
    return NotificationSeverity.Info;
  }

  const notifications = store.notifications.findAllRelated(
    props.vorgang.relationships?.notifications,
  ).sort((a, b) => (
    sortNotificationSeverityByPriority(a.attributes.severity, b.attributes.severity)
  ));

  return notifications[0]?.attributes.severity ?? null;
});

const { createNotification, deleteNotifications } = useVorgangNotification();

const markVorgang = async () => {
  if (notificationSeverity.value !== null) {
    manuallySetNotification.value = false;
    await deleteNotifications(props.vorgang.id);

    return;
  }

  manuallySetNotification.value = true;
  await createNotification(props.vorgang.id);
};
</script>
