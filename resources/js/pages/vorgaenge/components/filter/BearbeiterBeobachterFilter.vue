<template>
  <div
    v-if="!filterStore.isParticipantFilterSplit"
    class="flex min-w-0 flex-col space-y-1"
  >
    <span>
      <label>
        Bearbeiter/Beobachter
      </label>
      <DsButton
        icon="link-slash"
        icon-align="center"
        size="sm"
        variant="clear"
        class="-my-1"
        title="Getrennt filtern"
        data-test="vorgangsliste__filter__toggle-participants-select"
        @click="toggleSelectView"
      />
    </span>
    <DsSelect
      v-model="filterStore.filter.relationFilter.participants"
      :data="visibleFirmenMembers"
      :search-keys="['attributes.name']"
      :is-loading="isLoading"
      value-key="id"
      data-test="vorgangsliste__filter__participants"
    >
      <template #entry="{entry}">
        <UserTag
          :user="entry"
          class="pt-1"
        />
      </template>
    </DsSelect>
  </div>

  <template v-else>
    <div class="flex min-w-0 flex-col space-y-1">
      <span>
        <label>
          Bearbeiter
        </label>
        <DsButton
          icon="link"
          icon-align="center"
          size="sm"
          variant="clear"
          class="-my-1"
          title="Filter zusammenführen"
          data-test="vorgangsliste__filter__toggle-bearbeiter-select"
          @click="toggleSelectView"
        />
      </span>
      <DsSelect
        v-model="filterStore.filter.relationFilter.bearbeiter"
        :data="visibleFirmenMembers"
        :search-keys="['attributes.name']"
        :is-loading="isLoading"
        value-key="id"
        data-test="vorgangsliste__filter__bearbeiter"
      >
        <template #entry="{entry}">
          <UserTag
            :user="entry"
            class="pt-1"
          />
        </template>
      </DsSelect>
    </div>
    <div class="flex min-w-0 flex-col space-y-1">
      <span>
        <label>
          Beobachter
        </label>
        <DsButton
          icon="link"
          icon-align="center"
          size="sm"
          variant="clear"
          class="-my-1"
          title="Filter zusammenführen"
          @click="toggleSelectView"
        />
      </span>
      <DsSelect
        v-model="filterStore.filter.relationFilter.beobachter"
        :data="visibleFirmenMembers"
        :search-keys="['attributes.name']"
        :is-loading="isLoading"
        value-key="id"
        data-test="vorgangsliste__filter__beobachter"
      >
        <template #entry="{entry}">
          <UserTag
            :user="entry"
            class="pt-1"
          />
        </template>
      </DsSelect>
    </div>
  </template>
</template>

<script setup lang="ts">
import { DsButton, DsSelect } from '@demvsystems/design-components';
import { onBeforeMount } from 'vue';

import UserTag from '@/components/tags/UserTag.vue';
import useHierarchy from '@/components/users/useHierarchy';
import { useVorgaengeFilterStore } from '@/pages/vorgaenge/stores/vorgaengeFilterStore';

const filterStore = useVorgaengeFilterStore();

const {
  visibleFirmenMembers,
  loadVisibleFirmenMembers,
  isLoading,
} = useHierarchy();

function toggleSelectView() {
  filterStore.isParticipantFilterSplit = !filterStore.isParticipantFilterSplit;
}

onBeforeMount(async () => {
  await loadVisibleFirmenMembers();
});
</script>
