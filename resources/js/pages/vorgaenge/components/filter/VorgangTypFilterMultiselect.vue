<template>
  <DsFormGroup
    label="Vorgangstyp"
    class="grow"
  >
    <DsMultiselect
      :model-value="modelValue"
      :is-loading="vorgangstypenStore.isLoading"
      :options="vorgangstypenOptions"
      data-test="vorgangsliste__filter__vorgangstypen"
      object-as-value
      @update:model-value="emits('update:modelValue', $event)"
    />
  </DsFormGroup>
</template>

<script setup lang="ts">
import { DsFormGroup, DsMultiselect, MultiselectItem } from '@demvsystems/design-components';
import { computed } from 'vue';

import { useVorgangstypenStore } from '@/stores/vorgangstypenStore';

defineProps<{
  modelValue: MultiselectItem[],
}>();

const emits = defineEmits<{
  (event: 'update:modelValue', vorgangTypen: MultiselectItem[]): void
}>();

const vorgangstypenStore = useVorgangstypenStore();

const vorgangstypenOptions = computed<MultiselectItem[]>(
  () => vorgangstypenStore.vorgangstypen.map(
    (typ) => ({ value: typ.id, label: typ.attributes.titel }),
  ),
);
</script>
