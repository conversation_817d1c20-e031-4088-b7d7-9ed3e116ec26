<template>
  <DsForm
    class="space-y-3"
    data-test="vorgangsliste__filter__modal"
  >
    <BearbeiterBeobachterFilter />

    <DsFormGroup label="Kunde">
      <KundeSelect
        v-model="filterStore.filter.relationFilter.kunde"
        data-test="vorgangsliste__filter__kunde"
      />
    </DsFormGroup>

    <Vertraege
      v-model="filterStore.filter.relationFilter.vertraege"
      :kunde-id="filterStore.filter.relationFilter.kunde"
      :gesellschaft-id="filterStore.filter.relationFilter.gesellschaft"
      data-test="vorgangsliste__filter__vertrag"
    />

    <DsFormGroup label="Gesellschaft">
      <Gesellschaft
        v-model="filterStore.filter.relationFilter.gesellschaft"
        data-test="vorgangsliste__filter__gesellschaft"
      />
    </DsFormGroup>

    <DsFormGroup
      label="Status"
      class="grow"
    >
      <DsSelect
        v-model="filterStore.filter.relationFilter.status"
        :data="[
          VorgangStatusFilterMapping.Offen,
          VorgangStatusFilterMapping.Erledigt,
        ]"
        data-test="vorgangsliste__filter__status"
      >
        <template #entry="{entry}">
          <VorgangStatusComponent
            :status="
              VorgangStatusFilterMapping.Offen === entry
                ? VorgangStatus.Offen
                : VorgangStatus.Erledigt"
          />
        </template>
      </DsSelect>
    </DsFormGroup>

    <DsFormGroup
      label="Vorgangsart"
      class="grow"
    >
      <DsMultiselect
        v-model="filterStore.filter.relationFilter.vorgangsart"
        :options="filterStore.vorgangsartFilterOptions"
        data-test="vorgangsliste__filter__vorgangsart"
        object-as-value
      />
    </DsFormGroup>

    <VorgangstypSelect v-model="filterStore.filter.relationFilter.vorgangTypen" />

    <div class="grid gap-x-4 md:grid-cols-2">
      <DsFormGroup
        label="Fällig zum (von)"
        class="grow"
      >
        <DsInput
          v-model="filterStore.filter.dateFilter.fromDate"
          :datepicker-config="{upperLimit: filterStore.filter.dateFilter.untilDate}"
          type="date"
          placeholder="TT.MM.JJJJ"
          data-test="vorgangsliste__filter__from-date"
        />
      </DsFormGroup>
      <DsFormGroup
        label="Fällig zum (bis)"
        class="grow"
      >
        <DsInput
          v-model="filterStore.filter.dateFilter.untilDate"
          :datepicker-config="{lowerLimit: filterStore.filter.dateFilter.fromDate}"
          type="date"
          placeholder="TT.MM.JJJJ"
          data-test="vorgangsliste__filter__until-date"
        />
      </DsFormGroup>
    </div>
    <DsButton
      icon="eraser"
      size="sm"
      variant="secondary"
      class="pt-1"
      data-test="vorgangsliste__filter__clear"
      @click="filterStore.resetFilters"
    >
      Zurücksetzen
    </DsButton>
  </DsForm>
</template>

<script setup lang="ts">
import {
  DsButton,
  DsForm,
  DsFormGroup,
  DsInput,
  DsMultiselect,
  DsSelect,
} from '@demvsystems/design-components';

import VorgangStatusComponent from '@/components/VorgangStatus.vue';
import Gesellschaft from '@/components/formBasisInfo/gesellschaft/Gesellschaft.vue';
import KundeSelect from '@/components/formBasisInfo/kunde/KundeSelect.vue';
import Vertraege from '@/components/formBasisInfo/vertrag/Vertraege.vue';
import BearbeiterBeobachterFilter
  from '@/pages/vorgaenge/components/filter/BearbeiterBeobachterFilter.vue';
import VorgangstypSelect from '@/pages/vorgaenge/components/filter/VorgangTypFilterMultiselect.vue';
import { useVorgaengeFilterStore } from '@/pages/vorgaenge/stores/vorgaengeFilterStore';
import { VorgangStatus } from '@/types';

import { VorgangStatusFilterMapping } from './types';

const filterStore = useVorgaengeFilterStore();
</script>
