import { VertragResource } from '@/store/resources/types';

export type RelationFilter = {
  participants: string | undefined,
  bearbeiter: string | undefined,
  beobachter: string | undefined,
  kunde: string | null,
  vertraege: VertragResource[],
  gesellschaft: string | null,
  status: string | undefined,
  vorgangsart: FilterOption[],
  vorgangTypen: FilterOption[],
};

export enum VorgangStatusFilterMapping {
  Offen = 'offen,erinnerung_1,erinnerung_2,mahnung_1,mahnung_2',
  Erledigt = 'erledigt',
}

export type DateFilter = {
  untilDate: Date | undefined,
  fromDate: Date | undefined,
};

export type VorgangFilter = {
  relationFilter: RelationFilter,
  dateFilter: DateFilter,
};

export type FilterOption = {
  value: string,
  label: string,
};
