<template>
  <DsButton
    icon="filter"
    button-icon-align="right"
    data-test="vorgangsliste__filter__btn"
    class="whitespace-nowrap"
    variant="secondary"
    @click="show = true"
  >
    {{ filterButtonLabel }}
  </DsButton>
  <DsModal
    :show="show"
    confirm-label="Filter übernehmen"
    cancel-label="Abbrechen"
    overflow="visible"
    title="Filter"
    anchor="top"
    action-required
    @confirm="emit('filterChange')"
    @close="show = false"
  >
    <FilterForm />
  </DsModal>
</template>

<script setup lang="ts">
import { DsButton, DsModal } from '@demvsystems/design-components';
import { computed, defineAsyncComponent, ref, watch } from 'vue';

import useCurrentUser from '@/components/users/useCurrentUser';
import { useVorgaengeFilterStore } from '@/pages/vorgaenge/stores/vorgaengeFilterStore';

const emit = defineEmits<{
  (event: 'filterChange'): void
}>();

const FilterForm = defineAsyncComponent(
  () => import('@/pages/vorgaenge/components/filter/FilterForm.vue'),
);

const vorgaengeFilterStore = useVorgaengeFilterStore();

const show = ref(false);
const { user, isLoading: isUserLoading } = useCurrentUser();

watch(user,  async () => {
  if (user.value === undefined) {
    return;
  }

  if (vorgaengeFilterStore.hasFilterQueryParams()) {
    await vorgaengeFilterStore.initFromQueryParams();
    emit('filterChange');

    return;
  }

  vorgaengeFilterStore.filter.relationFilter.participants = user.value.id;
  emit('filterChange');
}, { immediate: true });

const filterButtonLabel = computed(() => {
  let countStr = '';

  if (vorgaengeFilterStore.activeFilterCount > 0) {
    countStr = vorgaengeFilterStore.activeFilterCount.toString();
  } else if (isUserLoading.value) {
    countStr = '...';
  }

  return `${countStr} Filter`;
});
</script>
