<template>
  <div class="flex flex-col">
    <DsModal
      ref="confirmationDeleteModal"
      title="Ausgewählte Vorgänge löschen?"
      :variant="modalVariantError"
      :confirm-label="
        `Ja, ${selectedVorgaenge.length} ${selectedVorgaenge.length > 1 ? 'Vorgänge' : 'Vorgang'} löschen`
      "
      icon="trash"
      cancel-label="Nein, Abbrechen"
    >
      Möchten Sie die folgenden ausgewählten Vorgänge wirklich löschen?
      <ul class="mt-1 max-h-60 list-inside list-disc overflow-y-auto">
        <li
          v-for="vorgang in selectedVorgaenge"
          :key="vorgang.id"
        >
          <span
            class="font-semibold"
            v-text="vorgang.attributes.titel"
          />
          <span class="text-gray-500">
            #{{ vorgang.attributes.vorgangsnummer }}
          </span>
        </li>
      </ul>
    </DsModal>
    <DsModal
      ref="confirmationCompletedModal"
      title="Ausgewählte Vorgänge auf „Erledigt” setzen?"
      :variant="modalVariantSuccess"
      confirm-label="Bestätigen"
      icon="check"
      cancel-label="Abbrechen"
    >
      Möchten Sie die folgenden Vorgänge wirklich auf „Erledigt”  setzen?
      <ul class="mt-1 max-h-60 list-inside list-disc overflow-y-auto">
        <li
          v-for="vorgang in selectedVorgaenge"
          :key="vorgang.id"
        >
          <span
            class="font-semibold"
            v-text="vorgang.attributes.titel"
          />
          <span class="text-gray-500">
            #{{ vorgang.attributes.vorgangsnummer }}
          </span>
        </li>
      </ul>
    </DsModal>
    <SideList
      :is-loading="loadingState !== 'complete'"
      :side-list-actions="sideListActions"
      :actions-button-text="actionsButtonText"
      data-test="vorgangsliste"
    >
      <template #form>
        <DsInput
          v-model="searchTerm"
          inline
          icon="search"
          class="grow"
          placeholder="Suche"
          data-test="vorgangsliste__search-input"
          @change="loadVorgaenge"
          @submit.prevent
        />
        <FilterModal @filter-change="loadVorgaenge" />
      </template>
      <template #actions>
        <p
          v-if="meta !== undefined"
          class="text-xs font-semibold uppercase leading-none tracking-wide text-gray-700"
          data-test="vorgangsliste__results"
        >
          <samp>
            {{ meta.total }}
            {{ meta.total === 1 ? 'Ergebnis' : 'Ergebnisse' }}
          </samp>
        </p>
      </template>
      <template #nav>
        <!-- cta empty list -->
        <EmptyVorgangsListe
          v-if="vorgaenge.length === 0"
          :query="searchTerm"
          :has-filters="filterStore.activeFilterCount > 0"
        />
        <!-- navigation list -->
        <DsVirtualList
          v-else
          :items="vorgaenge"
          :item-height="97"
          :load-next-handler="loadNext"
          :scroll-throttle="200"
          item-class="border-b"
          outer-container-class="flex-1 min-h-0 overflow-y-auto"
        >
          <template #default="{item: vorgang}">
            <VorgangListItem
              :key="vorgang.id"
              :selected="selectedVorgaenge.some(v => v === vorgang)"
              :vorgang="vorgang"
              @update:selected="handleSelect($event, vorgang)"
            />
          </template>
        </DsVirtualList>
      </template>
    </SideList>
  </div>
</template>

<script setup lang="ts">
import {
  DsInput,
  DsModal,
  DsVirtualList,
  ModalInstance,
  ModalVariant,
} from '@demvsystems/design-components';
import { computed, ref } from 'vue';

import { get } from '@/api';
import SideList from '@/components/sideList/SideList.vue';
import { SideListAction } from '@/components/sideList/types';
import FilterModal from '@/pages/vorgaenge/components/filter/FilterModal.vue';
import { useVorgaengeFilterStore } from '@/pages/vorgaenge/stores/vorgaengeFilterStore';
import router from '@/router';
import bulkComplete from '@/store/bulkChanges/bulkComplete';
import bulkDelete from '@/store/bulkChanges/bulkDelete';
import { createStore } from '@/store/resources';
import { provideStore } from '@/store/resources/composition';
import { eventBus } from '@/store/resources/store';
import type { VorgangResource } from '@/store/resources/types';
import { MetaObject } from '@/types/jsonapi';

import EmptyVorgangsListe from './EmptyVorgangsListe.vue';
import VorgangListItem from './VorgangListItem.vue';

const store = createStore();
provideStore(store);
const loadingState = ref<'initial' | 'loading' | 'complete'>('initial');

const vorgaenge = computed(
  () => store.vorgaenge.getAll().filter((elem) => !elem.attributes.isUntervorgang),
);
const selectedVorgaenge = ref<VorgangResource[]>([]);
const actionsButtonText = computed(() => {
  if (selectedVorgaenge.value.length === 0) {
    return undefined;
  } else if (selectedVorgaenge.value.length === 1) {
    return '1 Vorgang ausgewählt';
  } else {
    return `${selectedVorgaenge.value.length} Vorgänge ausgewählt`;
  }
});
const handleSelect = (selected: boolean, vorgang: VorgangResource) => {
  if (selected) {
    selectedVorgaenge.value.push(vorgang);
  } else {
    selectedVorgaenge.value = selectedVorgaenge.value.filter(
      (v) => v.id !== vorgang.id,
    );
  }
};

const searchTerm = ref('');

const filterStore = useVorgaengeFilterStore();

const meta = ref<MetaObject>();
const next = ref<string | null>(null);

const clearAndLoadVorgangsliste = async () => {
  try {
    loadingState.value = 'loading';

    const response = await get<VorgangResource>('/vorgaenge', {
      include: [
        'kunde',
        'sparte',
        'gesellschaft',
        'notifications',
      ],
      find: {
        key: searchTerm.value,
        fields: {
          kunde: ['name', 'external_id'],
          sparte: ['name'],
          vorgaenge: ['titel', 'vorgangsnummer'],
          gesellschaft: ['name'],
        },
      },
      fields: {
        kunden: ['name'],
        gesellschaften: ['name'],
        korrespondenzen: ['status', 'versandart'],
        mahnungen: ['status'],
        erinnerungen: ['status'],
      },
      sort: [
        {
          name: 'hasNotification',
          order: 'desc',
        }, {
          name: 'faelligAtSortable',
        },
      ],
      dateFilter: filterStore.filterRequestObj.dateFilter,
      filter: {
        ...filterStore.filterRequestObj.filter,
        ueberVorgang: null,
      },
    });

    store.vorgaenge.clear();
    store.load(response.data);

    meta.value = response.data.meta;
    next.value = response.data.links?.next ?? null;
  } catch (e) {
    eventBus.emit('error', 'Die Vorgänge konnten nicht geladen werden.');
  } finally {
    loadingState.value = 'complete';
  }
};

const loadVorgaenge = () => {
  if (loadingState.value !== 'loading') {
    void clearAndLoadVorgangsliste();
  }
};

// reload list when new vorgang is created
eventBus.on('vorgangErstellt', loadVorgaenge);

store.vorgaenge.events.update.subscribe((event) => {
  if (event.state !== store.vorgaenge) {
    store.vorgaenge.update(event.resource);
  }
});

// Update THIS store whenever another store updates their resource
// Otherwise we'd get an infinite loop of updates
store.korrespondenzen.events.update.subscribe((event) => {
  if (event.state !== store.korrespondenzen) {
    store.korrespondenzen.update(event.resource);
  }
});
store.erinnerungen.events.update.subscribe((event) => {
  if (event.state !== store.erinnerungen) {
    store.erinnerungen.update(event.resource);
  }
});
store.mahnungen.events.update.subscribe((event) => {
  if (event.state !== store.mahnungen) {
    store.mahnungen.update(event.resource);
  }
});

const loadNext = async () => {
  if (next.value === null) {
    return;
  }

  const response = await get<VorgangResource>(next.value);

  store.load(response.data);

  next.value = response.data.links?.next ?? null;
};

const confirmationDeleteModal = ref<ModalInstance | null>(null);
const confirmationCompletedModal = ref<ModalInstance | null>(null);
const modalVariantError = ModalVariant.Error;
const modalVariantSuccess = ModalVariant.Success;

const bulkDeleteSelected = async () => {
  const idsToDelete = selectedVorgaenge.value.map((vorgang) => vorgang.id);

  const success = await bulkDelete('vorgaenge', idsToDelete);

  if (!success) {
    eventBus.emit('error', 'Die ausgewählten Vorgänge konnten nicht gelöscht werden.');

    return;
  }

  selectedVorgaenge.value = [];

  await router.push({
    name: 'vorgaenge.index',
  });

  loadVorgaenge();

  eventBus.emit(
    'vorgaengeGeloescht',
    'Die ausgewählten Vorgänge wurden erfolgreich gelöscht.',
  );
};

const bulkCompleteVorgangSelected = async () => {
  const idsToComplete = selectedVorgaenge.value.map((vorgang) => vorgang.id);

  const success = await bulkComplete('vorgaenge', idsToComplete);

  if (!success) {
    eventBus.emit('error', 'Die ausgewählten Vorgänge konnten nicht auf „Erledigt” gesetzt werden.');

    return;
  }

  selectedVorgaenge.value = [];

  await router.push({
    name: 'vorgaenge.index',
  });

  loadVorgaenge();

  eventBus.emit(
    'vorgaengeErledigt',
    'Die ausgewählten Vorgänge wurden erfolgreich auf „Erledigt” gesetzt.',
  );
};

const sideListActions: SideListAction[] = [
  {
    text: 'Ausgewählte Vorgänge löschen',
    handler: () => {
      void confirmationDeleteModal.value?.open({
        confirmed: bulkDeleteSelected,
      });
    },
  },
  {
    text: 'Auf „Erledigt” setzen',
    handler: () => {
      void confirmationCompletedModal.value?.open({
        confirmed: bulkCompleteVorgangSelected,
      });
    },
  },
  {
    text: 'Auswahl zurücksetzen',
    handler: () => {
      selectedVorgaenge.value = [];
    },
  },
];

eventBus.on(
  'vorgangAccessed',
  (vorgangId: unknown) => {
    if (typeof vorgangId !== 'string') {
      return;
    }

    const accessedVorgang = store.vorgaenge.find(vorgangId);
    if (accessedVorgang?.attributes?.hasNotification === undefined) {
      return;
    }

    accessedVorgang.attributes.hasNotification = false;

    if (accessedVorgang.relationships?.notifications?.data === undefined) {
      return;
    }

    accessedVorgang.relationships.notifications.data = [];
  },
);
</script>
