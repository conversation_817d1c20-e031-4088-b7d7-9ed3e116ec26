import { pickBy } from 'lodash-es';
import { defineStore } from 'pinia';
import { computed, ref, watch } from 'vue';
import { useRoute } from 'vue-router';

import { get } from '@/api';
import {
  VorgangFilter,
  FilterOption,
  VorgangStatusFilterMapping,
} from '@/pages/vorgaenge/components/filter/types';
import { VertragResource, VorgangTypResource } from '@/store/resources/types';

/**
 * This store contains all form data from the filter form.
 * It is also responsible for all reactivity between those form attributes.
 */
export const useVorgaengeFilterStore = defineStore('vorgaengeFilter', () => {
  /* --- data --- */
  const vorgangsartFilterOptions: FilterOption[] = [
    {
      value: 'korrespondenz_email',
      label: 'E-Mail',
    },
    {
      value: 'korrespondenz_brief',
      label: 'Brief',
    },
    {
      value: 'aufgabe',
      label: 'Aufgabe',
    },
    {
      value: 'vorgangsgruppe',
      label: 'Vorgangsgruppe',
    },
  ];

  const route = useRoute();
  const query = computed(() => route.query);

  const hasFilterQueryParams = (): boolean => {
    const params = Object.keys(query.value);

    return params.some((elem) => elem.match('^filter\\[[a-zA-Z]+\\]$'));
  };

  const getInitFilter = () => ({
    ...{
      relationFilter: {
        participants: undefined,
        bearbeiter: undefined,
        beobachter: undefined,
        kunde: null,
        gesellschaft: null,
        status: hasFilterQueryParams() ? undefined : VorgangStatusFilterMapping.Offen,
        vorgangsart: [],
        vorgangTypen: [],
        vertraege: [],
      },
      dateFilter: {
        fromDate: undefined,
        untilDate: undefined,
      },
    },
  });

  const isParticipantFilterSplit = ref(false);

  const filter = ref<VorgangFilter>(getInitFilter());

  /* --- functions --- */

  const resetFilters = () => {
    filter.value = getInitFilter();
  };

  const getVertraege = async (vertraegeIds: string[]): Promise<VertragResource[]> => {
    const promises = vertraegeIds.map(async (
      vertragId: string,
    ): Promise<VertragResource | undefined> => {
      if (vertragId === '') {
        return undefined;
      }

      try {
        return (await get<VertragResource>(
          `/vertraege/${vertragId}`,
          {
            include: ['kunden'],
            fields: {
              kunden: ['name'],
            },
            filter: {
              ...(filter.value.relationFilter.kunde !== null ? {
                kunden: filter.value.relationFilter.kunde,
              } : {}),
            },
          },
        )).data.data;
      } catch {
        // ignore
      }
    });

    return (await Promise.all(promises))
      .filter((vertrag) => vertrag !== undefined) as VertragResource[];
  };

  async function getVorgangTypen(vorgangTypenIds: string[]): Promise<VorgangTypResource[]> {
    const promises = vorgangTypenIds.map(async (
      vorgangTypId: string,
    ): Promise<VorgangTypResource | undefined> => {
      if (vorgangTypId === '') {
        return undefined;
      }

      try {
        return (await get<VorgangTypResource>(
          `/vorgangTypen/${vorgangTypId}`,
        )).data.data;
      } catch {
        // ignore
      }
    });

    return (await Promise.all(promises))
      .filter((vertrag) => vertrag !== undefined) as VorgangTypResource[];
  }

  const initFromQueryParams = async (): Promise<void> => {
    if (hasFilterQueryParams()) {
      filter.value = getInitFilter();
    }

    if (query.value['filter[bearbeiter]'] !== undefined) {
      filter.value.relationFilter.bearbeiter = query.value['filter[bearbeiter]'] as string;
      isParticipantFilterSplit.value = true;
    }

    if (query.value['filter[beobachter]'] !== undefined) {
      filter.value.relationFilter.beobachter = query.value['filter[beobachter]'] as string;
      isParticipantFilterSplit.value = true;
    }

    if (query.value['filter[participants]'] !== undefined) {
      filter.value.relationFilter.participants = query.value['filter[participants]'] as string;
    }

    if (query.value['filter[kunde]'] !== undefined) {
      filter.value.relationFilter.kunde = query.value['filter[kunde]'] as string;
    }

    if (filter.value.relationFilter.kunde !== null && query.value['filter[vertraege]'] !== undefined) {
      const vertraegeIds = (query.value['filter[vertraege]'] as string).split(',');

      filter.value.relationFilter.vertraege = await getVertraege(vertraegeIds);
    }

    if (query.value['filter[gesellschaft]'] !== undefined) {
      filter.value.relationFilter.gesellschaft = query.value['filter[gesellschaft]'] as string;
    }

    if (query.value['filter[status]'] !== undefined) {
      switch ((query.value['filter[status]'] as string).toLowerCase()) {
        case 'offen':
          filter.value.relationFilter.status = VorgangStatusFilterMapping.Offen;

          break;
        case 'erledigt':
          filter.value.relationFilter.status = VorgangStatusFilterMapping.Erledigt;

          break;
      }
    }

    if (query.value['filter[vorgangsart]'] !== undefined) {
      filter.value.relationFilter.vorgangsart = [];
      const queryValues = (query.value['filter[vorgangsart]'] as string).split(',');

      queryValues.forEach((vorgangsart) => {
        const vorgangsOption = vorgangsartFilterOptions.find(
          (option) => option.value === vorgangsart,
        );
        if (
          vorgangsOption !== undefined
          && !filter.value.relationFilter.vorgangsart.includes(vorgangsOption)
        ) {
          filter.value.relationFilter.vorgangsart.push(vorgangsOption);
        }
      });
    }

    if (query.value['filter[vorgangstypen]'] !== undefined) {
      filter.value.relationFilter.vorgangTypen = [];
      const queryValues = (query.value['filter[vorgangstypen]'] as string).split(',');
      const vorgangTypen = await getVorgangTypen(queryValues);

      queryValues.forEach((vorgangTypId) => {
        const vorgangTyp = vorgangTypen.find(
          (vorgangTypElem) => vorgangTypElem.id === vorgangTypId,
        );

        if (vorgangTyp === undefined) {
          return;
        }

        const newElem: FilterOption = {
          'label': vorgangTyp.attributes.titel,
          'value': vorgangTyp.id,
        };

        if (filter.value.relationFilter.vorgangTypen.some(
          (elem) => elem.value === newElem.value,
        )) {
          return;
        }

        filter.value.relationFilter.vorgangTypen.push(newElem);
      });
    }

    if (query.value['filter[untilDate]'] !== undefined) {
      filter.value.dateFilter.untilDate = new Date(query.value['filter[untilDate]'] as string);
    }

    if (query.value['filter[fromDate]'] !== undefined) {
      filter.value.dateFilter.untilDate = new Date(query.value['filter[fromDate]'] as string);
    }
  };

  const getVorgangsartRequestStr = (): string | null => {
    if (filter.value.relationFilter.vorgangsart.length === 0) {
      return null;
    }

    return filter.value.relationFilter.vorgangsart
      .map((option: FilterOption) => {
        switch (option.value) {
          case 'aufgabe':
            return 'aufgabe,dunkelverarbeitet';
          default:
            return option.value;
        }
      })
      .join();
  };

  const filterRequestObj = computed(() => {
    const participants = isParticipantFilterSplit.value
      ? {
        'bearbeiter.user_id': filter.value.relationFilter.bearbeiter,
        'beobachter.user_id': filter.value.relationFilter.beobachter,
      }
      : { 'participants.user_id': filter.value.relationFilter.participants };

    return {
      // pickBy removes falsy values eg. null, '' etc
      filter: pickBy({
        ...participants,
        kunde: filter.value.relationFilter.kunde,
        vertraege: filter.value.relationFilter.vertraege.map((vertrag) => vertrag.id).join(),
        gesellschaft: filter.value.relationFilter.gesellschaft,
        status: filter.value.relationFilter.status,
        vorgangsart: getVorgangsartRequestStr(),
        vorgangTyp: filter.value.relationFilter.vorgangTypen
          ?.map((option: FilterOption) => option.value)
          .join(),
      }),
      dateFilter: pickBy({
        fromDate: filter.value.dateFilter.fromDate,
        untilDate: filter.value.dateFilter.untilDate,
      }),
    };
  });

  const activeFilterCount = computed(() => (
    Object.values(filterRequestObj.value.filter).length
    + Object.values(filterRequestObj.value.dateFilter).length
  ));

  /* --- reactivity --- */

  watch(() => filter.value.relationFilter.kunde, () => {
    filter.value.relationFilter.vertraege = [];
  });

  return {
    filter,
    vorgangsartFilterOptions,
    isParticipantFilterSplit,

    filterRequestObj,
    hasFilterQueryParams,
    initFromQueryParams,
    resetFilters,
    activeFilterCount,
  };
});
