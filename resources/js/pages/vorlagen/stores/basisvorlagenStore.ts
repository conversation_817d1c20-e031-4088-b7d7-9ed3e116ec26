import { defineStore } from 'pinia';
import { computed } from 'vue';

import { VorlageResource } from '@/store/resources/types';
import { useExternalList } from '@/utils/useExternalList';

export const useBasisvorlagenStore = defineStore('basisvorlagen', () => {
  const {
    document: basisvorlagenDoc,
    isLoading,
    update,
  } = useExternalList<VorlageResource>('basisvorlagen');

  const basisvorlagen = computed<VorlageResource[]>(() => (
    basisvorlagenDoc.value?.data ?? []
  ));

  return {
    isLoading,
    basisvorlagen,
    update,
  };
});
