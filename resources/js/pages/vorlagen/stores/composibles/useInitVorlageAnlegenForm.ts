import { captureException } from '@sentry/vue';
import { ref } from 'vue';

import { get } from '@/api';
import { Brief<PERSON><PERSON>ender, BriefEmpfaenger } from '@/pages/vorgangAnlegen/types';
import { useVorlageAnlegenStore } from '@/pages/vorlagen/stores/vorlageAnlegenStore';
import { Sichtbarkeit, Versandart, Vorlagenart } from '@/pages/vorlagen/types';
import useVorlageListStore from '@/pages/vorlagen/uses/useVorlageListStore';
import {
  BriefVorlageResource, KampagneMailVorlageResource,
  MailVorlageResource,
  VorgangTypResource,
  VorlageResource,
} from '@/store/resources/types';

// uses
const { isLoading:  isVorlageListStoreLoading } = useVorlageListStore();

// for calls to backend.
const isLoading = ref(false);

// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types
export default () => {
  const vorlageAnlegenStore = useVorlageAnlegenStore();

  const setBriefVorlageAttributes = (
    vorgangTyp: VorgangTypResource,
    vorlage: BriefVorlageResource,
  ) => {
    vorlageAnlegenStore.hasInformal = vorlage.attributes.informalContent !== ''
      && vorlage.attributes.informalContent != undefined;

    vorlageAnlegenStore.vorlagenart = Vorlagenart.Vorgang;
    vorlageAnlegenStore.versandart = Versandart.Brief;
    vorlageAnlegenStore.vorgangTypId = vorgangTyp.id;

    vorlageAnlegenStore.formalContent = vorlage.attributes.formalContent;
    vorlageAnlegenStore.informalContent = vorlage.attributes.informalContent ?? '';

    vorlageAnlegenStore.briefEmpfaenger = vorlage.attributes.empfaengerType
      ?? BriefEmpfaenger.Gesellschaft;
    vorlageAnlegenStore.absender = vorlage.attributes.senderTyp ?? BriefAbsender.Makler;
  };

  const setMailVorlageAttributes = (
    vorgangTyp: VorgangTypResource,
    vorlage: MailVorlageResource,
  ) => {
    vorlageAnlegenStore.hasInformal = vorlage.attributes.informalContent !== null
      && vorlage.attributes.informalContent !== undefined;

    vorlageAnlegenStore.vorlagenart = Vorlagenart.Vorgang;
    vorlageAnlegenStore.versandart = Versandart.Mail;
    vorlageAnlegenStore.vorgangTypId = vorgangTyp.id;

    vorlageAnlegenStore.formalSubject = vorlage.attributes.formalSubject;
    vorlageAnlegenStore.formalContent = vorlage.attributes.formalContent;
    vorlageAnlegenStore.informalSubject = vorlage.attributes.informalSubject ?? null;
    vorlageAnlegenStore.informalContent = vorlage.attributes.informalContent ?? null;

    vorlageAnlegenStore.mailEmpfaenger = vorlage.attributes.empfaengerTypes ?? [];
    vorlageAnlegenStore.cc = vorlage.attributes.cc ?? [];
    vorlageAnlegenStore.bcc = vorlage.attributes.bcc ?? [];
    vorlageAnlegenStore.attachments = vorlage.attributes.attachments ?? [];
  };

  const setKampagneMailVorlageAttributes = (
    vorlage: KampagneMailVorlageResource,
  ) => {
    vorlageAnlegenStore.hasInformal = vorlage.attributes.informalContent !== null
      && vorlage.attributes.informalContent !== undefined;

    vorlageAnlegenStore.vorlagenart = Vorlagenart.Kampagne;
    vorlageAnlegenStore.versandart = Versandart.Mail;

    vorlageAnlegenStore.formalSubject = vorlage.attributes.formalSubject;
    vorlageAnlegenStore.formalContent = vorlage.attributes.formalContent;
    vorlageAnlegenStore.informalSubject = vorlage.attributes.informalSubject ?? null;
    vorlageAnlegenStore.informalContent = vorlage.attributes.informalContent ?? null;
    vorlageAnlegenStore.attachments = vorlage.attributes.attachments ?? [];
  };

  const setKampagneBriefVorlageAttributes = (
    vorlage: KampagneMailVorlageResource,
  ) => {
    vorlageAnlegenStore.hasInformal = vorlage.attributes.informalContent !== null
      && vorlage.attributes.informalContent !== undefined;

    vorlageAnlegenStore.vorlagenart = Vorlagenart.Kampagne;
    vorlageAnlegenStore.versandart = Versandart.Brief;

    vorlageAnlegenStore.formalContent = vorlage.attributes.formalContent;
    vorlageAnlegenStore.informalContent = vorlage.attributes.informalContent ?? null;
  };

  const setVorlageAttributes = (vorlage: VorlageResource) => {
    vorlageAnlegenStore.isEditable = vorlage.attributes.isEditable ?? true;
    vorlageAnlegenStore.isStandard = !vorlage.relationships?.ersteller?.data;

    vorlageAnlegenStore.name = vorlage.attributes.name;
    vorlageAnlegenStore.description = vorlage.attributes.description ?? '';
    vorlageAnlegenStore.sichtbarkeit = vorlage.attributes.usageByOwnerOnly
      ? Sichtbarkeit.Private
      : Sichtbarkeit.Shared;

    if (vorlage.relationships?.ersteller?.data?.id === undefined)  {
      vorlageAnlegenStore.sichtbarkeit = Sichtbarkeit.Standard;
    }

    vorlageAnlegenStore.isBasisvorlage = vorlage.attributes.isBasisvorlage ?? false;
  };

  const fillFormFromApi = async () => {
    isLoading.value = true;
    try {
      const vorlageResource = (await get<VorlageResource>(`vorlagen/${vorlageAnlegenStore.id}`,
        {
          include: [
            'vorlage',
            'vorlage.vorgangTyp',
            'ersteller',
          ],
        },
      )).data;

      isLoading.value = false;

      if (vorlageResource === undefined && ! isVorlageListStoreLoading) {
        // todo: redirect to 404
        return;
      }

      if (vorlageResource?.data === undefined) {
        return;
      }

      setVorlageAttributes(vorlageResource.data);

      if (vorlageResource.data.relationships?.vorlage !== undefined) {
        const vorgangTyp = vorlageResource.included?.find(
          (included) => included.type === 'vorgangTypen',
        ) as VorgangTypResource;

        switch (vorlageResource.data.relationships.vorlage.data?.type) {
          case 'vorlagenBrief': {
            setBriefVorlageAttributes(
              vorgangTyp,
              vorlageResource.included?.find(
                (included) => included.type === 'vorlagenBrief',
              ) as BriefVorlageResource);

            break;
          }
          case 'vorlagenMail': {
            setMailVorlageAttributes(
              vorgangTyp,
              vorlageResource.included?.find(
                (included) => included.type === 'vorlagenMail',
              ) as MailVorlageResource);

            break;
          }
          case 'vorlagenKampagneMail': {
            setKampagneMailVorlageAttributes(
              vorlageResource.included?.find(
                (included) => included.type === 'vorlagenKampagneMail',
              ) as KampagneMailVorlageResource);

            break;
          }
          case 'vorlagenKampagneBrief': {
            setKampagneBriefVorlageAttributes(
              vorlageResource.included?.find(
                (included) => included.type === 'vorlagenKampagneBrief',
              ) as KampagneMailVorlageResource);

            break;
          }
          default:
            throw new Error('Invalid VorlagenType');
        }
      }
    } catch (error) {
      captureException('api error in: uses/useVorlage.ts');
    } finally {
      isLoading.value = false;
    }
  };

  return {
    fillFormFromApi,
    isLoading,
  };
};
