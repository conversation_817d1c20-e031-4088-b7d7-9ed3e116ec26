import { onMounted } from 'vue';
import { useRouter } from 'vue-router';

import { del, extractErrors, isAxiosError, post, put } from '@/api';
import { useBasisvorlagenStore } from '@/pages/vorlagen/stores/basisvorlagenStore';
import { Sichtbarkeit, Versandart, Vorlagenart } from '@/pages/vorlagen/types';
import { injectStore } from '@/store/resources/composition';
import { eventBus } from '@/store/resources/store';
import { Store, VorlageResource } from '@/store/resources/types';
import { Document } from '@/types/jsonapi';

import { useVorlageAnlegenStore } from '../vorlageAnlegenStore';

export default function (): {
  deleteVorlage: () => Promise<void>,
  submit: () => Promise<void>,
  submitForceCreate: () => Promise<void>,
  save: () => Promise<Document<VorlageResource> | null>,
} {
  const vorlageAnlegenStore = useVorlageAnlegenStore();
  const basisvorlagenStore = useBasisvorlagenStore();

  let store: Store | null = null;
  const router = useRouter();

  onMounted(() => {
    store = injectStore();
  });

  const getVorlageAttributes = () => ({
    name: vorlageAnlegenStore.name,
    description: vorlageAnlegenStore.description,
    isStandard: vorlageAnlegenStore.sichtbarkeit === Sichtbarkeit.Standard,
    usageByOwnerOnly: vorlageAnlegenStore.sichtbarkeit === Sichtbarkeit.Private,
    isPrivate: vorlageAnlegenStore.sichtbarkeit === Sichtbarkeit.Private,
    isBasisvorlage: vorlageAnlegenStore.isBasisvorlage,
  });

  const getVorgangTypRelationship = () => ({
    data: {
      type: 'vorgangTypen',
      id: vorlageAnlegenStore.vorgangTypId,
    },
  });

  const getBriefVorlageRelationship = () => ({
    data: {
      type: 'vorlagenBrief',

      attributes: {
        empfaengerType: vorlageAnlegenStore.briefEmpfaenger,
        senderTyp: vorlageAnlegenStore.absender,

        formalContent: vorlageAnlegenStore.formalContent,
        informalContent: vorlageAnlegenStore.hasInformal
          ? vorlageAnlegenStore.informalContent : null,
      },

      relationships: {
        vorgangTyp: getVorgangTypRelationship(),
      },
    },
  });

  const getMailVorlageRelationship = () => ({
    data: {
      type: 'vorlagenMail',

      attributes: {
        empfaengerTypes: vorlageAnlegenStore.mailEmpfaenger,
        cc: vorlageAnlegenStore.cc ?? [],
        bcc: vorlageAnlegenStore.bcc ?? [],
        attachments: vorlageAnlegenStore.attachments ?? [],

        formalSubject: vorlageAnlegenStore.formalSubject,
        formalContent: vorlageAnlegenStore.formalContent,
        informalSubject: vorlageAnlegenStore.hasInformal ? vorlageAnlegenStore.informalSubject : '',
        informalContent: vorlageAnlegenStore.hasInformal ? vorlageAnlegenStore.informalContent : '',
      },

      relationships: {
        vorgangTyp: getVorgangTypRelationship(),
      },
    },
  });

  const getKampagneMailVorlageRelationship = () => ({
    data: {
      type: 'vorlagenKampagneMail',

      attributes: {
        attachments: vorlageAnlegenStore.attachments ?? [],

        formalSubject: vorlageAnlegenStore.formalSubject,
        formalContent: vorlageAnlegenStore.formalContent,
        informalSubject: vorlageAnlegenStore.hasInformal ? vorlageAnlegenStore.informalSubject : '',
        informalContent: vorlageAnlegenStore.hasInformal ? vorlageAnlegenStore.informalContent : '',
      },
    },
  });

  const getKampagneBriefVorlageRelationship = () => ({
    data: {
      type: 'vorlagenKampagneBrief',

      attributes: {
        formalContent: vorlageAnlegenStore.formalContent,
        informalContent: vorlageAnlegenStore.hasInformal ? vorlageAnlegenStore.informalContent : '',
      },
    },
  });

  function getVorlageRelationship() {
    const vorlagenart = vorlageAnlegenStore.vorlagenart;
    const versandart = vorlageAnlegenStore.versandart;

    if (vorlagenart === Vorlagenart.Vorgang) {
      return versandart === Versandart.Mail
        ? getMailVorlageRelationship()
        : getBriefVorlageRelationship();
    }

    return versandart === Versandart.Mail
      ? getKampagneMailVorlageRelationship()
      :  getKampagneBriefVorlageRelationship();
  }

  const getVorlageDto = () => ({
    data: {
      type: 'vorlage',
      attributes: getVorlageAttributes(),
      relationships: {
        vorlage: getVorlageRelationship(),
      },
    },
  });

  const save = async (): Promise<Document<VorlageResource> | null> => {
    try {
      vorlageAnlegenStore.resetErrors();

      const response = await (vorlageAnlegenStore.id === ''
        ? post<VorlageResource>('vorlagen/', getVorlageDto())
        : put<VorlageResource>(`vorlagen/${vorlageAnlegenStore.id}`,
          getVorlageDto()));

      await basisvorlagenStore.update();

      return response.data;
    } catch (e) {
      if (!isAxiosError(e) || e.response === undefined) {
        eventBus.emit('error');

        throw e;
      }

      eventBus.emit(
        e.response?.status === 422
          ? 'vorlageMissingFields'
          : 'error',
      );

      vorlageAnlegenStore.errors = extractErrors(
        e.response.data.errors ?? [],
        'vorlagen',
      );
    }

    return null;
  };

  const submit = async () => {
    const vorlage = await save();
    if (vorlage === null || vorlage.data === undefined) {
      return;
    }

    if (!vorlageAnlegenStore.isNew) {
      eventBus.emit('vorlageUpdated');

      store?.update(vorlage);

      return;
    }

    eventBus.emit('vorlageErstellt');

    await router.push({
      name: 'vorlagen.show',
      params: {
        id: vorlage.data.id,
      },
    });
  };

  const submitForceCreate = async () => {
    vorlageAnlegenStore.id = ''; // triggers creation of new vorlage

    await submit();
  };

  const deleteVorlage = async () => {
    try {
      await del<VorlageResource>(`vorlagen/${vorlageAnlegenStore.id}`);
      await basisvorlagenStore.update();

      eventBus.emit('vorlageGeloescht');

      await router.push({
        name: 'vorlagen.show',
      });
    } catch (e) {
      eventBus.emit('error');
    }
  };

  return {
    deleteVorlage,
    submit,
    submitForceCreate,
    save,
  };
}
