import { defineStore } from 'pinia';
import { computed, ref } from 'vue';

import useInitVorlageAnlegenForm
  from '@/pages/vorlagen/stores/composibles/useInitVorlageAnlegenForm';
import useSubmitVorlageForm from '@/pages/vorlagen/stores/composibles/useSubmitVorlageForm';
import { Attachment, VorlageMailEmpfaenger } from '@/store/resources/types';

import { BriefAbsender, BriefEmpfaenger } from '../../vorgangAnlegen/types';
import { Sichtbarkeit, Versandart, Vorlagenart } from '../types';

export const useVorlageAnlegenStore = defineStore('vorlageAnlegen', () => {
  const initialValues = {
    isEditable: true,
    isStandard: false,
    hasInformal: false,
    vorlagenart: Vorlagenart.Vorgang,
    versandart: Versandart.Mail,
    errors: {},

    id: '',
    name: '',
    sichtbarkeit: Sichtbarkeit.Private,
    description: '',

    vorgangTypId: '',
    formalContent: '',
    informalContent: '',

    mailEmpfaenger: [],
    cc: [],
    bcc: [],
    formalSubject: '',
    informalSubject: '',
    attachments: [],

    briefEmpfaenger: BriefEmpfaenger.Gesellschaft,
    absender: BriefAbsender.Makler,

    isBasisvorlage: false,
  };

  const {
    submit,
    submitForceCreate,
    save,
    deleteVorlage,
  } = useSubmitVorlageForm();

  const {
    fillFormFromApi,
    isLoading,
  } = useInitVorlageAnlegenForm();

  // only frontend
  const isEditable = ref(initialValues.isEditable);
  const isStandard = ref(initialValues.isStandard);
  const hasInformal = ref(initialValues.hasInformal);
  const vorlagenart = ref(initialValues.vorlagenart);
  const versandart = ref(initialValues.versandart);
  const errors = ref<Record<string, string[]>>(initialValues.errors);

  // DTO-Attributes Vorlage
  const id = ref(initialValues.id);
  const name = ref(initialValues.name);
  const sichtbarkeit = ref(initialValues.sichtbarkeit);
  const description = ref(initialValues.description);

  const vorgangTypId = ref(initialValues.vorgangTypId);
  const formalContent = ref<string | null>(initialValues.formalContent);
  const informalContent = ref<string | null>(initialValues.informalContent);

  // DTO-Attributes Vorlage mail
  const mailEmpfaenger = ref<VorlageMailEmpfaenger[]>(initialValues.mailEmpfaenger);
  const cc = ref<VorlageMailEmpfaenger[]>(initialValues.cc);
  const bcc = ref<VorlageMailEmpfaenger[]>(initialValues.bcc);

  // DTO-Attributes Vorlage mail & kampagne
  const formalSubject = ref<string | null>(initialValues.formalSubject);
  const informalSubject = ref<string | null>(initialValues.informalSubject);
  const attachments = ref<Array<Attachment>>(initialValues.attachments);

  // DTO-Attributes Vorlage brief
  const briefEmpfaenger = ref<BriefEmpfaenger>(initialValues.briefEmpfaenger);
  const absender = ref(initialValues.absender);

  const isBasisvorlage = ref(initialValues.isBasisvorlage);

  const resetForm = () => {
    isEditable.value = initialValues.isEditable;
    isStandard.value = initialValues.isStandard;
    hasInformal.value = initialValues.hasInformal;
    vorlagenart.value = initialValues.vorlagenart;
    versandart.value = initialValues.versandart;
    errors.value = initialValues.errors;

    id.value = initialValues.id;
    name.value = initialValues.name;
    sichtbarkeit.value = initialValues.sichtbarkeit;
    description.value = initialValues.description;

    vorgangTypId.value = initialValues.vorgangTypId;
    formalContent.value = initialValues.formalContent;
    informalContent.value = initialValues.informalContent;

    mailEmpfaenger.value = initialValues.mailEmpfaenger;
    cc.value = initialValues.cc;
    bcc.value = initialValues.bcc;
    formalSubject.value = initialValues.formalSubject;
    informalSubject.value = initialValues.informalSubject;
    attachments.value = initialValues.attachments;

    briefEmpfaenger.value = initialValues.briefEmpfaenger;
    absender.value = initialValues.absender;

    isBasisvorlage.value = initialValues.isBasisvorlage;
  };

  const resetErrors = () => {
    errors.value = initialValues.errors;
  };

  return {
    resetForm,
    resetErrors,
    submit,
    submitForceCreate,
    save,
    deleteVorlage,
    fillFormFromApi,
    isLoading,

    // only form
    isNew: computed(() => id.value === ''),
    isEditable,
    isStandard,
    hasInformal,
    vorlagenart,
    versandart,
    errors,

    // shared
    id,
    name,
    sichtbarkeit,
    description,
    vorgangTypId,
    formalContent,
    informalContent,

    // mail
    mailEmpfaenger,
    cc,
    bcc,
    formalSubject,
    informalSubject,
    attachments,

    // brief
    briefEmpfaenger,
    absender,

    isBasisvorlage,
  };
});
