<template>
  <div class="flex items-center space-x-2">
    <DsFormGroup
      label="Als Basisvorlage festlegen"
      label-for="isBasisvorlage"
      inline
    >
      <DsCheckbox
        id="isBasisvorlage"
        v-model="vorlageAnlegenStore.isBasisvorlage"
        :disabled="!vorlageAnlegenStore.isEditable"
      />
    </DsFormGroup>
    <DsIcon
      name="circle-info"
      variant="regular"
      title="Wenn Sie eine Vorlage als Basisvorlage festlegen, wird diese Vorlage in der Vorgangserstellung bei der Auswahl des zugehörigen Vorgangstyps immer automatisch vorausgewählt"
    />
  </div>
  <div
    v-if="vorlageAnlegenStore.vorgangTypId !== '' && currentBasisvorlage !== undefined"
    class="flex w-fit items-center space-x-2 rounded-md border p-2 text-sm"
  >
    <DsIcon
      name="memo-pad"
      variant="regular"
      class="text-gray-500"
    />
    <span>
      Aktuelle Basisvorlage:
    </span>
    <a
      class="text-blue-700"
      :href="currentBasisvorlage.links?.direct"
    >
      {{ currentBasisvorlage?.attributes?.name }}
    </a>
  </div>
</template>

<script setup lang="ts">
import { DsCheckbox, DsFormGroup, DsIcon } from '@demvsystems/design-components';
import { watch, ref } from 'vue';

import { useBasisvorlagenStore } from '@/pages/vorlagen/stores/basisvorlagenStore';
import { useVorlageAnlegenStore } from '@/pages/vorlagen/stores/vorlageAnlegenStore';
import { Versandart } from '@/pages/vorlagen/types';
import { VorlageResource } from '@/store/resources/types';

const vorlageAnlegenStore = useVorlageAnlegenStore();
const basisvorlagenStore = useBasisvorlagenStore();

const currentBasisvorlage = ref<VorlageResource | undefined>();

watch(() => [
  vorlageAnlegenStore.vorgangTypId,
  basisvorlagenStore.basisvorlagen,
], () => {
  currentBasisvorlage.value = undefined;

  if (vorlageAnlegenStore.vorgangTypId === '') {
    return;
  }

  const vorlageType = vorlageAnlegenStore.versandart === Versandart.Mail
    ? 'vorlagenMail'
    : 'vorlagenBrief';

  currentBasisvorlage.value = basisvorlagenStore.basisvorlagen?.find(
    (basisvorlage: VorlageResource) =>
      basisvorlage.attributes.basisvorlageVorgangstyp === vorlageAnlegenStore.vorgangTypId
      && basisvorlage.attributes.basisvorlageVorlagetyp === vorlageType,
  );
}, { immediate: true });
</script>
