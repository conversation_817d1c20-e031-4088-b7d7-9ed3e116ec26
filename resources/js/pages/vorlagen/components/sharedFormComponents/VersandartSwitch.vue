<template>
  <DsFormGroup label="Versandart">
    <DsRadioGroup
      v-model="vorlageAnlegenStore.versandart"
      :disabled="!vorlageAnlegenStore.isEditable"
      variant="button"
    >
      <DsRadioButton
        icon="at"
        :value="Versandart.Mail"
        data-test="vorlage__basic-info__versandart__mail"
      >
        E-Mail
      </DsRadioButton>
      <DsRadioButton
        :value="Versandart.Brief"
        icon="envelope"
        data-test="vorlage__basic-info__versandart__brief"
      >
        Brief
      </DsRadioButton>
    </DsRadioGroup>
  </DsFormGroup>
</template>

<script setup lang="ts">
import { DsFormGroup, DsRadioButton, DsRadioGroup } from '@demvsystems/design-components';

import { useVorlageAnlegenStore } from '../../stores/vorlageAnlegenStore';
import { Versandart } from '../../types';

// toggles between email and brief forms
const vorlageAnlegenStore = useVorlageAnlegenStore();
</script>
