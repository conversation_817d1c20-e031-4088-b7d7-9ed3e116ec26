<template>
  <div class="space-y-3">
    <h3 class="text-base font-semibold leading-none">
      Nachricht
    </h3>

    <VorlageSingleNachricht
      v-model:subject="formalSubjectProxy"
      v-model:content="vorlageAnlegenStore.formalContent"
      :disabled="!vorlageAnlegenStore.isEditable"
      append-to-label="(Siezen)"
      validation-name-subject="vorlagen.relationships.vorlage.data.attributes.formalSubject"
      validation-name-content="vorlagen.relationships.vorlage.data.attributes.formalContent"
      data-test="vorlage__duzen-und-siezen__nachricht__formal"
      required
    >
      <div
        class="text-right"
      >
        <VorlageVorschauModal
          :bcc="vorlageAnlegenStore.bcc"
          :cc="vorlageAnlegenStore.cc"
          :content="vorlageAnlegenStore.formalContent"
          :mail-empfaenger="vorlageAnlegenStore.mailEmpfaenger"
          :subject="formalSubjectProxy"
          :vorlagenart="vorlageAnlegenStore.vorlagenart"
          :versandart="vorlageAnlegenStore.versandart"
          :brief-empfaenger="vorlageAnlegenStore.briefEmpfaenger"
          :brief-absender="vorlageAnlegenStore.absender"
          suppress-errors-and-retry
        />
      </div>
    </VorlageSingleNachricht>
    <DsSwitch
      v-model="vorlageAnlegenStore.hasInformal"
      :disabled="!vorlageAnlegenStore.isEditable"
      data-test="vorlage__duzen-und-siezen__informal-switch"
      @click="scrollToInformalContentEditor"
    >
      Version fürs Duzen (optional)
    </DsSwitch>

    <div
      ref="duzenEditorRef"
      class="space-y-3"
    >
      <VorlageSingleNachricht
        v-show="vorlageAnlegenStore.hasInformal"
        v-model:subject="informalSubjectProxy"
        v-model:content="vorlageAnlegenStore.informalContent"
        :disabled="!vorlageAnlegenStore.isEditable"
        append-to-label="(Duzen)"
        validation-name-subject="vorlagen.relationships.vorlage.data.attributes.informalSubject"
        validation-name-content="vorlagen.relationships.vorlage.data.attributes.informalContent"
        data-test="vorlage__duzen-und-siezen__nachricht__informal"
      >
        <div
          class="text-right"
        >
          <VorlageVorschauModal
            v-if="vorlageAnlegenStore.hasInformal"
            :bcc="vorlageAnlegenStore.bcc"
            :cc="vorlageAnlegenStore.cc"
            :content="vorlageAnlegenStore.informalContent"
            :mail-empfaenger="vorlageAnlegenStore.mailEmpfaenger"
            :subject="informalSubjectProxy"
            :vorlagenart="vorlageAnlegenStore.vorlagenart"
            :versandart="vorlageAnlegenStore.versandart"
            :brief-empfaenger="vorlageAnlegenStore.briefEmpfaenger"
            :brief-absender="vorlageAnlegenStore.absender"
            suppress-errors-and-retry
          />
        </div>
      </VorlageSingleNachricht>
    </div>
  </div>
</template>

<script setup lang="ts">
import { DsSwitch } from '@demvsystems/design-components';
import { computed, useTemplateRef } from 'vue';

import VorlageVorschauModal from '@/pages/vorlagen/components/VorlageVorschauModal.vue';

import { useVorlageAnlegenStore } from '../../stores/vorlageAnlegenStore';
import { Versandart } from '../../types';

import VorlageSingleNachricht from './VorlageSingleNachricht.vue';

const duzenEditorRef = useTemplateRef<HTMLElement>('duzenEditorRef');
const wrapperRef = useTemplateRef<HTMLElement>('wrapperRef');

const vorlageAnlegenStore = useVorlageAnlegenStore();

const hasSubject = computed(
  () => vorlageAnlegenStore.versandart !== Versandart.Brief,
);

const formalSubjectProxy = computed({
  get: () => hasSubject.value ? (vorlageAnlegenStore.formalSubject ?? '') : null,
  set: (subject: string | null) => {
    vorlageAnlegenStore.formalSubject = subject;
  },
});

const informalSubjectProxy = computed({
  get: () => hasSubject.value ? (vorlageAnlegenStore.informalSubject ?? '') : null,
  set: (subject: string | null) => {
    vorlageAnlegenStore.informalSubject = subject;
  },
});

const scrollToInformalContentEditor = () => {
  if (!vorlageAnlegenStore.hasInformal) {
    scrollTo(0, wrapperRef.value?.scrollHeight ?? 0);

    return;
  }

  duzenEditorRef.value?.scrollIntoView({
    behavior: 'smooth',
  });
};
</script>
