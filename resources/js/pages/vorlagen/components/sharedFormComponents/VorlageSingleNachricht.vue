<template>
  <div class="space-y-4">
    <NachrichtSubject
      v-if="subject !== null"
      :disabled="disabled"
      :label="`Betreff ${appendToLabel}`"
      :model-value="subject"
      :required="required"
      :validation-name="validationNameSubject"
      data-test="vorlage__nachricht__subject"
      @update:model-value="$emit('update:subject', $event)"
    />

    <NachrichtContent
      :disabled="disabled"
      :label="`Nachricht ${appendToLabel}`"
      :model-value="content"
      :required="required"
      :validation-name="validationNameContent"
      class="col-span-4"
      data-test="vorlage__nachricht__content"
      @update:model-value="$emit('update:content', $event)"
    >
      <slot />
    </NachrichtContent>
  </div>
</template>

<script setup lang="ts">
import NachrichtContent from '@/components/form/NachrichtContent.vue';
import NachrichtSubject from '@/components/form/NachrichtSubject.vue';

withDefaults(defineProps<{
  subject: string | null,
  content: string | null,
  appendToLabel?: string,
  validationNameSubject?: string,
  validationNameContent?: string,
  required?: boolean,
  disabled?: boolean,
}>(), {
  appendToLabel: '',
  validationNameSubject: '',
  validationNameContent: '',
});

defineEmits<{
  (event: 'update:subject', subject: string): void,
  (event: 'update:content', content: string): void,
}>();
</script>
