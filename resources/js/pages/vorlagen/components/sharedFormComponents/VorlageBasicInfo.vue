<template>
  <div class="space-y-7">
    <VorlagenartSwitch />
    <div class="space-y-3">
      <h3 class="text-base font-semibold leading-none">
        Vorlage
      </h3>
      <div class="flex flex-col space-y-3 md:flex-row md:space-x-4 md:space-y-0">
        <DsFormGroup
          label="Vorlagenname"
          class="grow"
          validation-name="vorlagen.attributes.name"
          required
        >
          <DsInput
            v-model="vorlageAnlegenStore.name"
            :disabled="!vorlageAnlegenStore.isEditable"
            data-test="vorlage__basic-info__name"
          />
        </DsFormGroup>

        <DsFormGroup
          label="Sichtbarkeit"
        >
          <DsRadioGroup
            v-model="vorlageAnlegenStore.sichtbarkeit"
            variant="button"
          >
            <DsRadioButton
              icon="user"
              :disabled="!vorlageAnlegenStore.isEditable"
              :value="Sichtbarkeit.Private"
              title="Die Vorlage ist nur für Sie sichtbar"
              data-test="vorlage__basic-info__sichtbarkeit__private"
            >
              Privat
            </DsRadioButton>
            <DsRadioButton
              icon="users"
              :disabled="!vorlageAnlegenStore.isEditable"
              :value="Sichtbarkeit.Shared"
              title="Die Vorlage ist firmenweit sichtbar und kann nur von Ihnen bearbeitet werden"
              data-test="vorlage__basic-info__sichtbarkeit__shared"
            >
              Firmenweit
            </DsRadioButton>
            <DsRadioButton
              v-if="user?.attributes?.canCreateStandardVorlagen"
              icon="buildings"
              :disabled="!vorlageAnlegenStore.isEditable"
              :value="Sichtbarkeit.Standard"
              title="Die Vorlage ist für alle Nutzer sichtbar und kann nur von Admins bearbeitet werden"
              data-test="vorlage__basic-info__sichtbarkeit__shared"
            >
              Standard
            </DsRadioButton>
          </DsRadioGroup>
        </DsFormGroup>
      </div>

      <DsFormGroup
        label="Kurzbeschreibung"
      >
        <DsInput
          v-model="vorlageAnlegenStore.description"
          :disabled="!vorlageAnlegenStore.isEditable"
          data-test="vorlage__basic-info__description"
        />
      </DsFormGroup>
    </div>

    <div class="space-y-3">
      <h3 class="text-base font-semibold leading-none">
        Basis-Informationen
      </h3>

      <div class="flex flex-col space-y-3 md:flex-row md:space-x-4 md:space-y-0">
        <VersandartSwitch />

        <DsFormGroup
          v-if="vorlageAnlegenStore.vorlagenart === Vorlagenart.Vorgang"
          label="Vorgangstyp"
          class="grow"
          validation-name="vorlagen.relationships.vorlage.data.relationships.vorgangTyp.data.id"
          required
        >
          <DsSelect
            v-model="vorlageAnlegenStore.vorgangTypId"
            value-key="id"
            lavel-key="attributes.titel"
            :search-keys="['attributes.titel']"
            :disabled="!vorlageAnlegenStore.isEditable"
            :data="vorgangstypen"
            :is-loading="vorgangstypenStore.isLoading"
            required
            data-test="vorlage__basic-info__vorgangstyp__select"
          >
            <template #entry="{entry}">
              {{ entry.attributes?.titel ?? '' }}
            </template>
          </DsSelect>
        </DsFormGroup>
      </div>
      <VorlageBasisvorlage
        v-if="vorlageAnlegenStore.vorlagenart === Vorlagenart.Vorgang"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  DsFormGroup,
  DsInput,
  DsRadioButton,
  DsRadioGroup,
  DsSelect,
} from '@demvsystems/design-components';
import { computed } from 'vue';

import useCurrentUser from '@/components/users/useCurrentUser';
import VorlageBasisvorlage
  from '@/pages/vorlagen/components/sharedFormComponents/VorlageBasisvorlage.vue';
import VorlagenartSwitch
  from '@/pages/vorlagen/components/sharedFormComponents/VorlagenartSwitch.vue';
import { useVorgangstypenStore } from '@/stores/vorgangstypenStore';

import { useVorlageAnlegenStore } from '../../stores/vorlageAnlegenStore';
import { Sichtbarkeit, Versandart, Vorlagenart } from '../../types';

import VersandartSwitch from './VersandartSwitch.vue';

const vorlageAnlegenStore = useVorlageAnlegenStore();

const { user } = useCurrentUser();

const vorgangstypenStore = useVorgangstypenStore();

const vorgangstypen = computed(
  () => vorlageAnlegenStore.versandart === Versandart.Mail
    ? vorgangstypenStore.mailVorgangstypen
    : vorgangstypenStore.briefVorgangstypen,
);
</script>
