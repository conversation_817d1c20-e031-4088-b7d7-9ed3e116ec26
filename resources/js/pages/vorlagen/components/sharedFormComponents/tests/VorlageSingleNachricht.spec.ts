import { DsEditor } from '@demvsystems/design-components';
import { mount } from '@vue/test-utils';
import { describe, expect, it } from 'vitest';

import VorlageSingleNachricht from '../VorlageSingleNachricht.vue';

describe('test VorlageSingleNachricht', () => {
  it('shouldnt show subject if is params.subject is null', () => {
    const wrapper = mount(VorlageSingleNachricht, {
      props: {
        subject: null,
        content: 'abc',
      },
    });

    expect(wrapper.find('[data-test="vorlage__nachricht__subject"]').exists()).toBe(false);
  });

  const contentInitial = 'this is my content';
  const subjectInitial = 'bett';
  it('makes betreff and content required if props required is true', () => {
    const wrapper = mount(VorlageSingleNachricht, {
      props: {
        subject: subjectInitial,
        content: contentInitial,
        required: true,
      },
    });

    expect(
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      // eslint-disable-next-line @typescript-eslint/no-unsafe-call
      wrapper.getComponent('[data-test="vorlage__nachricht__subject"]').props('required'),
    ).toEqual(true);

    expect(
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      // eslint-disable-next-line @typescript-eslint/no-unsafe-call
      wrapper.getComponent('[data-test="vorlage__nachricht__content"]').props('required'),
    ).toEqual(true);
  });

  it('emits subject and content on input', async () => {
    const contentChanged = 'this is my content text';
    const subjectChanged = 'this is a test';

    const wrapper = mount(VorlageSingleNachricht, {
      props: {
        subject: subjectInitial,
        content: contentInitial,
        required: true,
      },
    });

    const subjectFormGroup = wrapper.getComponent('[data-test="vorlage__nachricht__subject"]');
    await subjectFormGroup.getComponent(DsEditor).setValue(subjectChanged);
    expect(wrapper.emitted('update:subject')).toEqual([[subjectChanged]]);

    const contentFormGroup = wrapper.getComponent('[data-test="vorlage__nachricht__content"]');
    await contentFormGroup.getComponent(DsEditor).setValue(contentChanged);
    expect(wrapper.emitted('update:content')).toEqual([[contentChanged]]);
  });
});
