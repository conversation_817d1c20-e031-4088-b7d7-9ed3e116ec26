<template>
  <DsFormGroup>
    <DsRadioGroup
      v-model="vorlageAnlegenStore.vorlagenart"
      :disabled="!vorlageAnlegenStore.isEditable"
      variant="card"
      class="md:grid-cols-2"
    >
      <DsRadioButton
        icon="inbox"
        :value="Vorlagenart.Vorgang"
        label="Vorgangsvorlage"
        data-test="vorlage__basic-info__vorlagenart__vorgang"
      >
        Vorlage für einen Vorgang
      </DsRadioButton>
      <DsRadioButton
        :value="Vorlagenart.Kampagne"
        icon="envelopes-bulk"
        label="Kampagnenvorlage"
        data-test="vorlage__basic-info__vorlagenart__kampagne"
      >
        Vorlage für eine Kampagne
      </DsRadioButton>
    </DsRadioGroup>
  </DsFormGroup>
</template>

<script setup lang="ts">
import { DsFormGroup, DsRadioButton, DsRadioGroup } from '@demvsystems/design-components';

import { useVorlageAnlegenStore } from '../../stores/vorlageAnlegenStore';
import { Vorlagenart } from '../../types';

// toggles between vorgangsvorlagen and kampagnevorlagen forms
const vorlageAnlegenStore = useVorlageAnlegenStore();
</script>
