<template>
  <DsForm
    ref="wrapperRef"
    :validation-errors="vorlageAnlegenStore.errors"
    class="space-y-7"
  >
    <VorlageBasicInfo />

    <div class="space-y-3">
      <VorlageDuzenUndSiezen />

      <DsFormGroup label="Automatischer Dateianhang">
        <DsMultiselect
          v-model="attachmentsProxy"
          :disabled="!vorlageAnlegenStore.isEditable"
          :options="attachmentOptions"
          data-test="kampagne-mail-vorlage-form__attachments"
          object-as-value
        />
      </DsFormGroup>
    </div>
  </DsForm>
</template>

<script setup lang="ts">
import { DsForm, DsFormGroup, DsMultiselect } from '@demvsystems/design-components';
import { storeToRefs } from 'pinia';

import { Attachment, attachmentLabels } from '@/store/resources/types';
import { getMultiselectOptions, makeMultiselectItemProxy } from '@/utils/multiselectEnumHelper';

import { useVorlageAnlegenStore } from '../../stores/vorlageAnlegenStore';
import VorlageBasicInfo from '../sharedFormComponents/VorlageBasicInfo.vue';
import VorlageDuzenUndSiezen from '../sharedFormComponents/VorlageDuzenUndSiezen.vue';

const vorlageAnlegenStore = useVorlageAnlegenStore();

const { attachments } = storeToRefs(vorlageAnlegenStore);

const attachmentsProxy = makeMultiselectItemProxy(attachments, attachmentLabels);

const attachmentOptions = getMultiselectOptions(Attachment, attachmentLabels);
</script>
