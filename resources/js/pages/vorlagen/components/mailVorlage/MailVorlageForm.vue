<template>
  <DsForm
    ref="wrapperRef"
    :validation-errors="vorlageAnlegenStore.errors"
    class="space-y-7"
  >
    <VorlageBasicInfo />

    <EmpfaengerFormSection
      v-model:has-cc-and-bcc="hasCcAndBcc"
      :validation-names="{
        empfaenger: 'vorlagen.relationships.vorlage.data.attributes.empfaengerTypes',
        cc: 'vorgangsart.attributes.cc',
        bcc: 'vorgangsart.attributes.bcc',
      }"
      :disabled="!vorlageAnlegenStore.isEditable"
    >
      <template #empfaenger>
        <DsMultiselect
          v-model="empfaengerProxy"
          :disabled="!vorlageAnlegenStore.isEditable"
          :options="empfaengerOptions"
          :validation="isEmail"
          class="w-full md:w-auto md:grow"
          allow-new-options-text="Tippen Sie, um eine E-Mail-Adresse hinzuzufügen"
          data-test="mail-vorlage-form__empfaenger"
          allow-new-options
          required
          object-as-value
        />
      </template>

      <template #cc>
        <DsMultiselect
          v-model="ccProxy"
          :disabled="!vorlageAnlegenStore.isEditable"
          :options="empfaengerOptions"
          :validation="isEmail"
          allow-new-options-text="Tippen Sie, um eine E-Mail-Adresse hinzuzufügen"
          data-test="mail-vorlage-form__cc"
          allow-new-options
          object-as-value
        />
      </template>

      <template #bcc>
        <DsMultiselect
          v-model="bccProxy"
          :disabled="!vorlageAnlegenStore.isEditable"
          :options="empfaengerOptions"
          :validation="isEmail"
          allow-new-options-text="Tippen Sie, um eine E-Mail-Adresse hinzuzufügen"
          data-test="mail-vorlage-form__bcc"
          allow-new-options
          object-as-value
        />
      </template>
    </EmpfaengerFormSection>

    <div class="space-y-3">
      <VorlageDuzenUndSiezen />

      <DsFormGroup label="Automatischer Dateianhang">
        <DsMultiselect
          v-model="attachmentsProxy"
          :disabled="!vorlageAnlegenStore.isEditable"
          :options="attachmentOptions"
          data-test="mail-vorlage-form__attachments"
          object-as-value
        />
      </DsFormGroup>
    </div>
  </DsForm>
</template>

<script setup lang="ts">
import { DsForm, DsFormGroup, DsMultiselect, MultiselectItem } from '@demvsystems/design-components';
import { storeToRefs } from 'pinia';
import { ref, watch } from 'vue';

import EmpfaengerFormSection from '@/components/form/EmpfaengerFormSection.vue';
import {
  getLabelForEmpfaengerType,
  isEmail,
  makeVorlageMailEmpfaengerMultiselectProxy,
} from '@/components/form/utils/mail';
import { Attachment, attachmentLabels, VorlageMailEmpfaengerType } from '@/store/resources/types';
import { getMultiselectOptions, makeMultiselectItemProxy } from '@/utils/multiselectEnumHelper';

import { useVorlageAnlegenStore } from '../../stores/vorlageAnlegenStore';
import VorlageBasicInfo from '../sharedFormComponents/VorlageBasicInfo.vue';
import VorlageDuzenUndSiezen from '../sharedFormComponents/VorlageDuzenUndSiezen.vue';

const vorlageAnlegenStore = useVorlageAnlegenStore();

const hasCcAndBcc = ref(false);

const { mailEmpfaenger, cc, bcc, attachments } = storeToRefs(vorlageAnlegenStore);

const empfaengerProxy = makeVorlageMailEmpfaengerMultiselectProxy(
  mailEmpfaenger,
);

const ccProxy = makeVorlageMailEmpfaengerMultiselectProxy(
  cc,
);

const bccProxy = makeVorlageMailEmpfaengerMultiselectProxy(
  bcc,
);

const attachmentOptions = getMultiselectOptions(Attachment, attachmentLabels);

const attachmentsProxy = makeMultiselectItemProxy(attachments, attachmentLabels);

const empfaengerOptions = Object.values(VorlageMailEmpfaengerType)
  .map((value): MultiselectItem => ({
    value,
    label: getLabelForEmpfaengerType(value),
  }));

watch([vorlageAnlegenStore.cc, vorlageAnlegenStore.bcc], () => {
  if (hasCcAndBcc.value) {
    return;
  }

  if (vorlageAnlegenStore.cc.length > 0 || vorlageAnlegenStore.bcc.length > 0) {
    hasCcAndBcc.value = true;
  }
}, { immediate: true });
</script>
