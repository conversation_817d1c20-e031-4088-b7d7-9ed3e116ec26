<template>
  <DsAlert
    type="info"
    icon="lock"
    label="Vorlage nicht editierbar"
  >
    <p>
      <template v-if="standard">
        Diese Vorlage ist eine Standardvorlage und kann daher nicht bearbeitet werden.
      </template>
      <template v-else>
        Diese Vorlage wurde durch einen anderen Benutzer erstellt,
        wesha<PERSON>b Sie nicht berechtigt sind, diese zu bearbeiten.
      </template>

      Gerne können Sie die Vorlage für sich duplizieren,
      um diese anschließend zu bearbeiten.
    </p>

    <DsButton
      class="mt-2"
      icon="copy"
      @click="vorlageAnlegenStore.submitForceCreate"
    >
      Vorlage duplizieren
    </DsButton>
  </DsAlert>
</template>

<script setup lang="ts">
import { DsAlert, DsButton } from '@demvsystems/design-components';

import { useVorlageAnlegenStore } from '@/pages/vorlagen/stores/vorlageAnlegenStore';

const vorlageAnlegenStore = useVorlageAnlegenStore();

defineProps<{
  standard: boolean,
}>();
</script>
