<template>
  <DsForm
    class="space-y-7"
    :validation-errors="vorlageAnlegenStore.errors"
  >
    <VorlageBasicInfo />

    <EmpfaengerAndAbsender
      v-model:empfaenger="vorlageAnlegenStore.briefEmpfaenger"
      v-model:absender="vorlageAnlegenStore.absender"
      :disabled="!vorlageAnlegenStore.isEditable"
    />

    <VorlageDuzenUndSiezen />
  </DsForm>
</template>

<script setup lang="ts">
import { DsForm } from '@demvsystems/design-components';

import { useVorlageAnlegenStore } from '../../stores/vorlageAnlegenStore';
import VorlageBasicInfo from '../sharedFormComponents/VorlageBasicInfo.vue';
import VorlageDuzenUndSiezen from '../sharedFormComponents/VorlageDuzenUndSiezen.vue';

import EmpfaengerAndAbsender from './EmpfaengerAndAbsender.vue';

const vorlageAnlegenStore = useVorlageAnlegenStore();
</script>
