<template>
  <DsForm
    class="space-y-7"
    :validation-errors="vorlageAnlegenStore.errors"
  >
    <VorlageBasicInfo />

    <VorlageDuzenUndSiezen />
  </DsForm>
</template>

<script setup lang="ts">
import { DsForm } from '@demvsystems/design-components';

import { useVorlageAnlegenStore } from '../../stores/vorlageAnlegenStore';
import VorlageBasicInfo from '../sharedFormComponents/VorlageBasicInfo.vue';
import VorlageDuzenUndSiezen from '../sharedFormComponents/VorlageDuzenUndSiezen.vue';

const vorlageAnlegenStore = useVorlageAnlegenStore();
</script>
