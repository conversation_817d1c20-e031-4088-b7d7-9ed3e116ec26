<template>
  <FormSection
    title="Anschrift"
    class="space-y-3"
  >
    <div class="flex space-x-4">
      <DsFormGroup label="Empfänger">
        <DsRadioGroup
          variant="button"
          :model-value="empfaenger"
          @update:model-value="emitAbsenderAndUpdateEmpfaenger"
        >
          <DsRadioButton
            value="gesellschaft"
            :disabled="disabled"
            data-test="empfaenger-and-absender__empfaenger__gesellschaft"
          >
            Gesellschaft
          </DsRadioButton>
          <DsRadioButton
            value="kunde"
            :disabled="disabled"
            data-test="empfaenger-and-absender__empfaenger__kunde"
          >
            Kunde
          </DsRadioButton>
          <DsRadioButton
            value="demv"
            :disabled="disabled"
            data-test="empfaenger-and-absender__empfaenger__demv"
          >
            DEMV
          </DsRadioButton>
        </DsRadioGroup>
      </DsFormGroup>

      <DsFormGroup label="Absender">
        <DsRadioGroup
          variant="button"
          :model-value="absender"
          @update:model-value="emitAbsender"
        >
          <DsRadioButton
            value="makler"
            :disabled="disabled"
            data-test="empfaenger-and-absender__absender__makler"
          >
            Makler
          </DsRadioButton>
          <DsRadioButton
            value="kunde"
            :disabled="isEmpfaengerKunde || disabled"
            data-test="empfaenger-and-absender__absender__kunde"
          >
            Kunde
          </DsRadioButton>
        </DsRadioGroup>
      </DsFormGroup>
    </div>
  </FormSection>
</template>

<script setup lang="ts">
import { DsFormGroup, DsRadioButton, DsRadioGroup } from '@demvsystems/design-components';
import { computed, defineEmits, defineProps } from 'vue';

import FormSection from '@/components/form/FormSection.vue';
import { BriefAbsender, BriefEmpfaenger } from '@/pages/vorgangAnlegen/types';

const props = defineProps<{
  empfaenger: BriefEmpfaenger;
  absender: BriefAbsender;
  disabled?: boolean;
}>();

const emit = defineEmits(['update:empfaenger', 'update:absender']);

const emitAbsender = (event: unknown) => {
  emit('update:absender', event);
};

const emitAbsenderAndUpdateEmpfaenger = (event: unknown) => {
  if (event === BriefEmpfaenger.Kunde) {
    emitAbsender(BriefAbsender.Makler);
  }

  emit('update:empfaenger', event);
};

const isEmpfaengerKunde = computed(() => props.empfaenger === BriefEmpfaenger.Kunde);
</script>
