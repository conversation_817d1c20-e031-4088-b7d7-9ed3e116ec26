import { mount } from '@vue/test-utils';
import { describe, expect, it } from 'vitest';

import { BriefAbsender, BriefEmpfaenger } from '@/pages/vorgangAnlegen/types';

import EmpfaengerAndAbsender from '../EmpfaengerAndAbsender.vue';

describe('EmpfaengerAndAbsender', () => {
  it('emits absender', async () => {
    const wrapper = mount(EmpfaengerAndAbsender, {
      props: {
        empfaenger: BriefEmpfaenger.Gesellschaft,
        absender: BriefAbsender.Makler,
      },
    });

    await wrapper.get('[data-test="empfaenger-and-absender__absender__kunde"]').trigger('click');
    await wrapper.get('[data-test="empfaenger-and-absender__absender__makler"]').trigger('click');

    expect(wrapper.emitted('update:absender'))
      .toEqual([['makler'], ['kunde'], ['makler']]);
  });

  it('emits empfaenger', async () => {
    const wrapper = mount(EmpfaengerAndAbsender, {
      props: {
        empfaenger: BriefEmpfaenger.Gesellschaft,
        absender: BriefAbsender.Makler,
      },
    });

    await wrapper.get('[data-test="empfaenger-and-absender__empfaenger__kunde"]').trigger('click');
    await wrapper.get('[data-test="empfaenger-and-absender__empfaenger__gesellschaft"]').trigger('click');
    await wrapper.get('[data-test="empfaenger-and-absender__empfaenger__demv"]').trigger('click');

    expect(wrapper.emitted('update:empfaenger'))
      .toEqual([['gesellschaft'], ['kunde'], ['gesellschaft'], ['demv']]);
    expect(wrapper.emitted('update:absender'))
      .toEqual([['makler'], ['makler']]);
  });

  it('disables kunde if empfaenger is kunde', () => {
    const wrapper = mount(EmpfaengerAndAbsender, {
      props: {
        empfaenger: BriefEmpfaenger.Kunde,
        absender: BriefAbsender.Makler,
      },
    });

    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    // eslint-disable-next-line @typescript-eslint/no-unsafe-call
    expect(wrapper.getComponent('[data-test="empfaenger-and-absender__absender__kunde"]').props('disabled')).toEqual(true);
  });
});
