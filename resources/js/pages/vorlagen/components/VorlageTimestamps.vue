<template>
  <div
    v-if="vorlage !== undefined"
    class="text-gray-700"
  >
    <div>
      Erstellt am
      <TimeText
        :value="vorlage.attributes.createdAt"
        class="font-semibold"
        only-date
      />
    </div>
    <div>
      Geändert am
      <TimeText
        :value="vorlage.attributes.updatedAt"
        class="font-semibold"
        only-date
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

import TimeText from '@/components/tags/TimeText.vue';
import { useVorlageAnlegenStore } from '@/pages/vorlagen/stores/vorlageAnlegenStore';
import { injectStore } from '@/store/resources/composition';
import { VorlageResource } from '@/store/resources/types';

const vorlageAnlegenStore = useVorlageAnlegenStore();

const store = injectStore();

const vorlage = computed<VorlageResource | undefined>(() => (
  store.vorlagen.find(vorlageAnlegenStore.id)
));
</script>
