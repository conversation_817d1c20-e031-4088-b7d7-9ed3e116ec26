<template>
  <div class="max-w-2xl">
    <NotEditableInfo
      v-if="!vorlageAnlegenStore.isEditable"
      class="mb-5"
      :standard="vorlageAnlegenStore.isStandard"
    />
    <template v-if="vorlageAnlegenStore.vorlagenart === Vorlagenart.Vorgang">
      <MailVorlageForm v-if="vorlageAnlegenStore.versandart === Versandart.Mail" />
      <BriefVorlageForm v-else />
    </template>
    <template v-else>
      <KampagneMailVorlageForm v-if="vorlageAnlegenStore.versandart === Versandart.Mail" />
      <KampagneBriefVorlageForm v-else />
    </template>
  </div>
</template>

<script setup lang="ts">
import { Versandart, Vorlagenart } from '@/pages/vorlagen/types';

import { useVorlageAnlegenStore } from '../stores/vorlageAnlegenStore';

import NotEditableInfo from './NotEditableInfo.vue';
import BriefVorlageForm from './briefVorlage/BriefVorlageForm.vue';
import KampagneBriefVorlageForm from './briefVorlage/KampagneBriefVorlageForm.vue';
import KampagneMailVorlageForm from './mailVorlage/KampagneMailVorlageForm.vue';
import MailVorlageForm from './mailVorlage/MailVorlageForm.vue';

const vorlageAnlegenStore = useVorlageAnlegenStore();
</script>
