<template>
  <div class="z-10 border-t px-5 py-4 shadow">
    <div class="-m-2 flex flex-wrap-reverse justify-end">
      <div class="m-2">
        <DsButton
          v-if="!vorlageAnlegenStore.isNew && vorlageAnlegenStore.isEditable"
          variant="danger"
          icon="trash"
          data-test="vorlage-form__footer__delete"
          @click="vorlageAnlegenStore.deleteVorlage"
        >
          Vorlage löschen
        </DsButton>
      </div>

      <div class="m-2 flex grow justify-end space-x-4">
        <DsButton
          v-if="vorlageAnlegenStore.isNew"
          icon="plus"
          data-test="vorlage-form__footer__submit"
          @click="vorlageAnlegenStore.submit"
        >
          Vorlage erstellen
        </DsButton>

        <template v-else-if="vorlageAnlegenStore.isEditable">
          <DsButton
            variant="secondary"
            icon="copy"
            data-test="vorlage-form__footer__as-new"
            @click="vorlageAnlegenStore.submitForceCreate"
          >
            Als neue Vorlage
          </DsButton>

          <DsButton
            icon="save"
            data-test="vorlage-form__footer__speichern"
            @click="vorlageAnlegenStore.submit"
          >
            Vorlage speichern
          </DsButton>
        </template>

        <template v-else>
          <DsButton
            icon="copy"
            data-test="vorlage-form__footer__duplizieren"

            @click="vorlageAnlegenStore.submitForceCreate"
          >
            Vorlage duplizieren
          </DsButton>
        </template>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { DsButton } from '@demvsystems/design-components';

import { useVorlageAnlegenStore } from '@/pages/vorlagen/stores/vorlageAnlegenStore';

const vorlageAnlegenStore = useVorlageAnlegenStore();
</script>
