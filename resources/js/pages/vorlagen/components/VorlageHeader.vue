<template>
  <div class="relative z-10 flex items-center justify-between px-5 py-3 shadow-sm">
    <h2
      class="flex space-x-3 text-xl font-semibold text-gray-800"
      v-text="vorlageAnlegenStore.isNew ? 'Vorlage erstellen' : vorlageAnlegenStore.name"
    />

    <div
      v-if="vorlage !== undefined && ersteller !== undefined"
      class="text-gray-700"
    >
      Erstellt von&nbsp;
      <UserTag
        v-if="ersteller !== undefined"
        :user="ersteller"
        class="font-semibold"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

import { useVorlageAnlegenStore } from '@/pages/vorlagen/stores/vorlageAnlegenStore';
import { injectStore } from '@/store/resources/composition';
import { UserResource, VorlageResource } from '@/store/resources/types';

import UserTag from '../../../components/tags/UserTag.vue';

const vorlageAnlegenStore = useVorlageAnlegenStore();

const store = injectStore();

const vorlage = computed<VorlageResource | undefined>(() => (
  store.vorlagen.find(vorlageAnlegenStore.id)
));

const ersteller = computed<UserResource | undefined>(() => (
  store.users.findRelated(vorlage.value?.relationships?.ersteller)
));
</script>
