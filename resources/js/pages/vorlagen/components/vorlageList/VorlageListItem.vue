<template>
  <router-link
    :to="{
      name: 'vorlagen.show',
      params: {id: vorlage.id},
    }"
    active-class="bg-gray-100 cursor-default"
    class="
      h-18 flex flex-col space-y-1
      overflow-hidden p-3 leading-tight hover:bg-gray-100
    "
    data-test="vorlagenliste__item"
  >
    <div class="flex items-center">
      <div class="block w-5 flex-none text-center">
        <DsIcon
          :name="typeIcon"
          variant="regular"
          class="text-gray-700"
          :title="typeName"
        />
      </div>

      <span
        class="ml-1 mr-2 grow truncate font-semibold text-gray-800"
        data-test="vorlagenliste__item__name"
        v-text="vorlage.attributes.name"
      />

      <VorlageOrigin
        :origin="origin"
        data-test="vorlagenliste__item__origin"
      />
    </div>

    <!-- second row -->
    <div class="flex items-center space-x-2">
      <span
        class="truncate text-gray-500"
        data-test="vorlagenliste__item__titel"
        v-text="vorgangstyp?.attributes?.titel ?? 'Kampagnenvorlage'"
      />
    </div>
  </router-link>
</template>

<script setup lang="ts">
import { DsIcon } from '@demvsystems/design-components';
import { computed } from 'vue';

import { injectStore } from '@/store/resources/composition';
import { VorlageResource } from '@/store/resources/types';
import { useVorgangstypenStore } from '@/stores/vorgangstypenStore';
import { RelationshipObject, ResourceLinkHasOne } from '@/types/jsonapi';

import { Origin } from '../../types';
import VorlageOrigin from '../vorlageOrigin.vue';

const isBriefVorlageRelationship = (
  x: RelationshipObject<ResourceLinkHasOne> | null | undefined,
): x is RelationshipObject<ResourceLinkHasOne<'vorlagenBrief'>> => (
  x?.data?.type.valueOf() === 'vorlagenBrief'
);

const isMailVorlageRelationship = (
  x: RelationshipObject<ResourceLinkHasOne> | null | undefined,
): x is RelationshipObject<ResourceLinkHasOne<'vorlagenMail'>> => (
  x?.data?.type.valueOf() === 'vorlagenMail'
);

const props = defineProps<{
  vorlage: VorlageResource,
}>();

const store = injectStore();
const vorgangstypenStore = useVorgangstypenStore();

const vorlageContent = computed(() => {
  const vorlageRelationship = props.vorlage.relationships?.vorlage;

  if (isBriefVorlageRelationship(vorlageRelationship)) {
    return store
      .vorlagenBrief
      .findRelated(vorlageRelationship);
  }

  if (isMailVorlageRelationship(vorlageRelationship)) {
    return store
      .vorlagenMail
      .findRelated(vorlageRelationship);
  }

  return undefined;
});

const origin = computed<Origin>(()  => {
  if (
    props.vorlage.attributes.isEditable
    && props.vorlage.relationships?.ersteller?.data?.id !== undefined
  ) {
    return Origin.Eigene;
  }

  if (!props.vorlage.relationships?.ersteller?.data) {
    return Origin.Standard;
  }

  return Origin.Firma;
});

const vorgangstyp = computed(() => {
  const vorgangstypId = vorlageContent.value?.relationships?.vorgangTyp?.data?.id;

  if (vorgangstypId === undefined) {
    return undefined;
  }

  return vorgangstypenStore.findById(vorgangstypId);
});

const typeIcon = computed((): string => {
  switch (props.vorlage.relationships?.vorlage?.data?.type) {
    case 'vorlagenMail':
    case 'vorlagenKampagneMail':
      return 'at';
    case 'vorlagenBrief':
    case 'vorlagenKampagneBrief':
      return 'envelope';
    default:
      return '';
  }
});
const typeName = computed((): string => {
  switch (props.vorlage.relationships?.vorlage?.data?.type) {
    case 'vorlagenMail':
      return 'Vorgangsvorlage E-Mail';
    case 'vorlagenBrief':
      return 'Vorgangsvorlage Brief';
    case 'vorlagenKampagneMail':
      return 'Kampagnenvorlage E-Mail';
    case 'vorlagenKampagneBrief':
      return 'Kampagnenvorlage Brief';
    default:
      return '';
  }
});
</script>
