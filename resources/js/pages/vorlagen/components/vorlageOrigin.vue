<template>
  <DsBadge
    :type="config.type"
    class="whitespace-nowrap"
    :title="config.label"
  >
    <span> {{ config.label }} </span>
  </DsBadge>
</template>

<script setup lang="ts">
import { DsBadge, BadgeType } from '@demvsystems/design-components';
import { computed } from 'vue';

import { Origin } from '../types';

const props = defineProps<{
  origin: Origin,
}>();

const origins: {
  value: Origin;
  label: string;
  type: BadgeType;
}[] = [
  {
    value: Origin.Firma,
    label: 'Firma',
    type: 'default',
  },
  {
    value: Origin.Standard,
    label: 'Standard',
    type: 'primary',
  },
  {
    value: Origin.Eigene,
    label: 'Eigene',
    type: 'success',
  },
];

const vorgangToVisibilityBadge = origins.reduce((acc, entry) => {
  acc[entry.value] = entry;

  return acc;
}, {} as Record<Origin, {
  label: string;
  type: BadgeType;
}>);

const config = computed(() => vorgangToVisibilityBadge[props.origin]);
</script>
