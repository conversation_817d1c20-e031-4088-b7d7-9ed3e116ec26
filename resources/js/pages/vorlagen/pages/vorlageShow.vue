<template>
  <div
    v-if="!vorlageAnlegenStore.isLoading"
    class="flex grow flex-col"
  >
    <VorlageHeader />

    <div class="grow overflow-y-auto p-5">
      <div class="flex flex-wrap">
        <VorlageForm class="mb-5 max-w-2xl grow" />

        <VorlageTimestamps
          v-if="!vorlageAnlegenStore.isNew"
          class="mb-5 max-w-2xl grow text-right"
        />
      </div>
    </div>

    <VorlageFormFooter />
  </div>

  <!-- skellies -->
  <div
    v-else
    class="flex w-full flex-col"
  >
    <div class="px-5 py-3 shadow-sm">
      <DsSkeleton class="h-7 w-80" />
    </div>

    <div class="max-w-2xl grow space-y-7 p-5">
      <div class="flex h-10 space-x-3">
        <DsSkeleton class="w-2/3" />
        <DsSkeleton class="w-1/3" />
      </div>

      <DsSkeleton class="h-10 w-full opacity-50" />

      <div class="flex h-10 space-x-3 opacity-25">
        <DsSkeleton class="w-2/3" />
        <DsSkeleton class="w-1/3" />
      </div>
    </div>

    <div class="flex w-full space-x-3 border-t px-5 py-4 shadow-sm">
      <div class="grow">
        <DsSkeleton class="h-8 w-32" />
      </div>
      <DsSkeleton class="h-8 w-32" />
      <DsSkeleton class="h-8 w-32" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { DsSkeleton } from '@demvsystems/design-components';
import { watch } from 'vue';

import VorlageForm from '../components/VorlageForm.vue';
import VorlageFormFooter from '../components/VorlageFormFooter.vue';
import VorlageHeader from '../components/VorlageHeader.vue';
import VorlageTimestamps from '../components/VorlageTimestamps.vue';
import { useVorlageAnlegenStore } from '../stores/vorlageAnlegenStore';

const vorlageAnlegenStore = useVorlageAnlegenStore();

const {
  id = '',
} = defineProps<{
  id?: string;
}>();

vorlageAnlegenStore.resetForm();

watch(() => id, async (newId) => {
  if (newId === '') {
    vorlageAnlegenStore.resetForm();

    return;
  }

  vorlageAnlegenStore.id = newId;

  await vorlageAnlegenStore.fillFormFromApi();
}, {
  immediate: true,
});

watch(() => vorlageAnlegenStore.name, () => {
  document.title = `${vorlageAnlegenStore.name} | Vorgangsmanager`;
});
</script>
