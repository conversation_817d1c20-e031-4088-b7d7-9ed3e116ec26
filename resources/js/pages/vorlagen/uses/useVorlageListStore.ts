import { invoke } from '@vueuse/core';
import { ref } from 'vue';

import { get, JsonApiUrl } from '@/api';
import { createStore } from '@/store/resources/';
import { eventBus } from '@/store/resources/store';
import { VorlageResource } from '@/store/resources/types';
import { MetaObject } from '@/types/jsonapi';

const isLoading = ref(false);
const store = createStore();
const meta = ref<MetaObject>();
const next = ref<string | null>(null);

const loadVorlageList = (options: JsonApiUrl<VorlageResource> = {
  include: [
    'vorlage',
    'vorlage.vorgangTyp',
    'ersteller',
  ],
}) => {
  invoke<void>(async () => {
    try {
      isLoading.value = true;
      store.clear();

      const response = await get<VorlageResource>('/vorlagen', options);
      store.load(response.data);
      meta.value = response.data.meta;
      next.value = response.data.links?.next ?? null;
    } catch {
      // todo: handle error
    } finally {
      isLoading.value = false;
    }
  });
};

loadVorlageList();

const reloadVorlagenList = (options?: JsonApiUrl<VorlageResource>): void => {
  store.vorlagen.clear();
  loadVorlageList(options);
};

eventBus.on('vorlageErstellt', reloadVorlagenList);
eventBus.on('vorlageGeloescht', reloadVorlagenList);

store.vorlagen.events.update.subscribe((event) => {
  // Update THIS store whenever another store updates their resource
  // Otherwise we'd get an infinite loop of updates
  if (event.state !== store.vorlagen) {
    store.vorlagen.update(event.resource);
  }
});

// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types
export default () => {
  // reload list when new vorlage is created
  return {
    meta,
    next,
    store,
    isLoading,
    loadVorlageList,
    reloadVorlagenList,
  };
};
